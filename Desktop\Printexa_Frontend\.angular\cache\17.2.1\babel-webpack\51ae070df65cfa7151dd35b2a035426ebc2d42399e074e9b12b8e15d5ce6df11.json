{"ast": null, "code": "import { __decorate, __param } from \"tslib\";\nimport { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nlet BulkDeleteConfirmationComponent = class BulkDeleteConfirmationComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onNoClick() {\n    this.dialogRef.close(false);\n  }\n  confirmDelete() {\n    this.dialogRef.close(true);\n  }\n  // Calculer le total des prix des produits sélectionnés\n  getTotalValue() {\n    return this.data.selectedProducts.reduce((total, product) => {\n      return total + product.prixUnitaireTTC;\n    }, 0);\n  }\n  // Obtenir un résumé des types de produits\n  getProductTypesSummary() {\n    const summary = {};\n    this.data.selectedProducts.forEach(product => {\n      summary[product.type] = (summary[product.type] || 0) + 1;\n    });\n    return summary;\n  }\n  // Obtenir les clés du résumé des types\n  getProductTypesKeys() {\n    return Object.keys(this.getProductTypesSummary());\n  }\n};\nBulkDeleteConfirmationComponent = __decorate([Component({\n  selector: 'app-bulk-delete-confirmation',\n  templateUrl: './bulk-delete-confirmation.component.html',\n  styleUrls: ['./bulk-delete-confirmation.component.scss'],\n  standalone: true,\n  imports: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, MatIconModule, MatDialogClose, CommonModule]\n}), __param(1, Inject(MAT_DIALOG_DATA))], BulkDeleteConfirmationComponent);\nexport { BulkDeleteConfirmationComponent };", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "Component", "Inject", "MatButtonModule", "MatIconModule", "CommonModule", "BulkDeleteConfirmationComponent", "constructor", "dialogRef", "data", "onNoClick", "close", "confirmDelete", "getTotalValue", "selectedProducts", "reduce", "total", "product", "prixUnitaireTTC", "getProductTypesSummary", "summary", "for<PERSON>ach", "type", "getProductTypesKeys", "Object", "keys", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "MatDialogClose", "__param"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\bulk-delete-confirmation\\bulk-delete-confirmation.component.ts"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport { Produit } from '../../Model/Produit';\n\nexport interface BulkDeleteDialogData {\n  selectedProducts: Produit[];\n  totalCount: number;\n}\n\n@Component({\n  selector: 'app-bulk-delete-confirmation',\n  templateUrl: './bulk-delete-confirmation.component.html',\n  styleUrls: ['./bulk-delete-confirmation.component.scss'],\n  standalone: true,\n  imports: [\n    MatDialogTitle,\n    MatDialogContent,\n    MatDialogActions,\n    MatButtonModule,\n    MatIconModule,\n    MatDialogClose,\n    CommonModule,\n  ],\n})\nexport class BulkDeleteConfirmationComponent {\n  constructor(\n    public dialogRef: MatDialogRef<BulkDeleteConfirmationComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: BulkDeleteDialogData\n  ) {}\n\n  onNoClick(): void {\n    this.dialogRef.close(false);\n  }\n\n  confirmDelete(): void {\n    this.dialogRef.close(true);\n  }\n\n  // Calculer le total des prix des produits sélectionnés\n  getTotalValue(): number {\n    return this.data.selectedProducts.reduce((total, product) => {\n      return total + product.prixUnitaireTTC;\n    }, 0);\n  }\n\n  // Obtenir un résumé des types de produits\n  getProductTypesSummary(): { [key: string]: number } {\n    const summary: { [key: string]: number } = {};\n    this.data.selectedProducts.forEach(product => {\n      summary[product.type] = (summary[product.type] || 0) + 1;\n    });\n    return summary;\n  }\n\n  // Obtenir les clés du résumé des types\n  getProductTypesKeys(): string[] {\n    return Object.keys(this.getProductTypesSummary());\n  }\n}\n"], "mappings": ";AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,0BAA0B;AAC5H,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AACjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AAuBvC,IAAMC,+BAA+B,GAArC,MAAMA,+BAA+B;EAC1CC,YACSC,SAAwD,EAC/BC,IAA0B;IADnD,KAAAD,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;EACnC;EAEHC,SAASA,CAAA;IACP,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACJ,SAAS,CAACG,KAAK,CAAC,IAAI,CAAC;EAC5B;EAEA;EACAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACJ,IAAI,CAACK,gBAAgB,CAACC,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;MAC1D,OAAOD,KAAK,GAAGC,OAAO,CAACC,eAAe;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAC,sBAAsBA,CAAA;IACpB,MAAMC,OAAO,GAA8B,EAAE;IAC7C,IAAI,CAACX,IAAI,CAACK,gBAAgB,CAACO,OAAO,CAACJ,OAAO,IAAG;MAC3CG,OAAO,CAACH,OAAO,CAACK,IAAI,CAAC,GAAG,CAACF,OAAO,CAACH,OAAO,CAACK,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1D,CAAC,CAAC;IACF,OAAOF,OAAO;EAChB;EAEA;EACAG,mBAAmBA,CAAA;IACjB,OAAOC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,sBAAsB,EAAE,CAAC;EACnD;CACD;AAlCYb,+BAA+B,GAAAoB,UAAA,EAf3CzB,SAAS,CAAC;EACT0B,QAAQ,EAAE,8BAA8B;EACxCC,WAAW,EAAE,2CAA2C;EACxDC,SAAS,EAAE,CAAC,2CAA2C,CAAC;EACxDC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACPjC,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBG,eAAe,EACfC,aAAa,EACb4B,cAAc,EACd3B,YAAY;CAEf,CAAC,EAIG4B,OAAA,IAAA/B,MAAM,CAACL,eAAe,CAAC,E,EAHfS,+BAA+B,CAkC3C"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}