{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      code: client.code.trim(),\n      syntax: client.syntax ? client.syntax.trim() : undefined,\n      matFiscal: client.matFiscal ? client.matFiscal.trim() : undefined,\n      email: client.email ? client.email.trim() : undefined,\n      telephone: client.telephone ? client.telephone.trim() : undefined\n    };\n    console.log('Tentative de création du client:', clientData);\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      console.log('Réponse complète du serveur:', response);\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      console.log('Client créé avec succès:', newClient);\n      // Mettre à jour le cache local\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(error => {\n      console.error('Erreur détaillée lors de la création:', {\n        status: error.status,\n        statusText: error.statusText,\n        error: error.error,\n        message: error.message,\n        url: error.url,\n        headers: error.headers\n      });\n      return this.handleCreateError(error);\n    }));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    return this.http.put(`${this.baseUrl}${id}`, client, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Client mis à jour avec succès');\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(error => {\n      // Simulation locale en cas d'erreur\n      if (error.status === 0 || error.status === 500) {\n        return this.simulateLocalDeletion(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Simulation locale de mise à jour\n  simulateLocalUpdate(id, updateData) {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Simulation locale de suppression\n  simulateLocalDeletion(ids) {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length\n        });\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "syntax", "undefined", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "console", "log", "post", "observe", "response", "newClient", "body", "currentData", "value", "status", "statusText", "message", "url", "handleCreateError", "updateClient", "put", "clearCurrentClient", "deleteSelectedClients", "ids", "length", "delete", "filter", "includes", "simulateLocalDeletion", "deleteClient", "simulateLocalUpdate", "updateData", "index", "findIndex", "updatedClient", "observer", "setTimeout", "complete", "deletedCount", "removeItem", "data", "getDialogData", "dialogData", "generateGuid", "replace", "c", "r", "Math", "random", "v", "toString", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\n  private currentClientSubject: BehaviorSubject<Client | null>;\n  public currentClient$: Observable<Client | null>;\n  dataChange = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n\n  constructor(private http: HttpClient, private router: Router) {\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n\n  private getClientFromStorage(): Client | null {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\n    });\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false; // Désactivation du loading\n        this.dataChange.next(clients);\n      }),\n      catchError(error => {\n        this.isTblLoading = false; // Désactivation en cas d'erreur\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n\n    // Préparer les données à envoyer\n    const clientData: CreateClientSimpleDto = {\n      code: client.code.trim(),\n      syntax: client.syntax ? client.syntax.trim() : undefined,\n      matFiscal: client.matFiscal ? client.matFiscal.trim() : undefined,\n      email: client.email ? client.email.trim() : undefined,\n      telephone: client.telephone ? client.telephone.trim() : undefined\n    };\n\n    console.log('Tentative de création du client:', clientData);\n\n    return this.http.post<Client>(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(\n      map((response: any) => {\n        console.log('Réponse complète du serveur:', response);\n        const newClient: Client = response.body;\n        if (!newClient) {\n          throw new Error('Aucune donnée reçue du serveur');\n        }\n        return newClient;\n      }),\n      tap((newClient: Client) => {\n        console.log('Client créé avec succès:', newClient);\n        // Mettre à jour le cache local\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError((error: HttpErrorResponse) => {\n        console.error('Erreur détaillée lors de la création:', {\n          status: error.status,\n          statusText: error.statusText,\n          error: error.error,\n          message: error.message,\n          url: error.url,\n          headers: error.headers\n        });\n        return this.handleCreateError(error);\n      })\n    );\n  }\n\n  // Mettre à jour un client\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\n    return this.http.put<void>(`${this.baseUrl}${id}`, client, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        console.log('Client mis à jour avec succès');\n        this.clearCurrentClient();\n      }),\n      catchError(this.handleError)\n    );\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n      }),\n      catchError(error => {\n        // Simulation locale en cas d'erreur\n        if (error.status === 0 || error.status === 500) {\n          return this.simulateLocalDeletion(ids);\n        }\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => client.id !== id));\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Simulation locale de mise à jour\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    \n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n\n    const updatedClient: Client = { ...currentData[index], ...updateData };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    \n    return new Observable<Client>(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Simulation locale de suppression\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    \n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({ deletedCount: ids.length });\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthodes utilitaires\n  private generateGuid(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n}\n"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE;IACA,MAAMrB,UAAU,GAA0B;MACxCsB,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBC,MAAM,EAAEP,MAAM,CAACO,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACD,IAAI,EAAE,GAAGE,SAAS;MACxDC,SAAS,EAAET,MAAM,CAACS,SAAS,GAAGT,MAAM,CAACS,SAAS,CAACH,IAAI,EAAE,GAAGE,SAAS;MACjEE,KAAK,EAAEV,MAAM,CAACU,KAAK,GAAGV,MAAM,CAACU,KAAK,CAACJ,IAAI,EAAE,GAAGE,SAAS;MACrDG,SAAS,EAAEX,MAAM,CAACW,SAAS,GAAGX,MAAM,CAACW,SAAS,CAACL,IAAI,EAAE,GAAGE;KACzD;IAEDI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE9B,UAAU,CAAC;IAE3D,OAAO,IAAI,CAACT,IAAI,CAACwC,IAAI,CAAS,IAAI,CAACtC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B2B,OAAO,EAAE;KACV,CAAC,CAACtB,IAAI,CACLtB,GAAG,CAAE6C,QAAa,IAAI;MACpBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,QAAQ,CAAC;MACrD,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIb,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOa,SAAS;IAClB,CAAC,CAAC,EACFhD,GAAG,CAAEgD,SAAiB,IAAI;MACxBL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,SAAS,CAAC;MAClD;MACA,MAAME,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;MACzC,IAAI,CAAC3C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGwB,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF/C,UAAU,CAAE0B,KAAwB,IAAI;MACtCgB,OAAO,CAAChB,KAAK,CAAC,uCAAuC,EAAE;QACrDyB,MAAM,EAAEzB,KAAK,CAACyB,MAAM;QACpBC,UAAU,EAAE1B,KAAK,CAAC0B,UAAU;QAC5B1B,KAAK,EAAEA,KAAK,CAACA,KAAK;QAClB2B,OAAO,EAAE3B,KAAK,CAAC2B,OAAO;QACtBC,GAAG,EAAE5B,KAAK,CAAC4B,GAAG;QACdhC,OAAO,EAAEI,KAAK,CAACJ;OAChB,CAAC;MACF,OAAO,IAAI,CAACiC,iBAAiB,CAAC7B,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA8B,YAAYA,CAAC3B,EAAU,EAAEC,MAAuB;IAC9C,OAAO,IAAI,CAAC1B,IAAI,CAACqD,GAAG,CAAO,GAAG,IAAI,CAACnD,OAAO,GAAGuB,EAAE,EAAE,EAAEC,MAAM,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC7FxB,GAAG,CAAC,MAAK;MACP2C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACe,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF1D,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EACA;EACAgC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,EAAEC,MAAM,EAAE;MAChB,OAAO/D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,iBAAiB,CAAC,CAAC;;IAGvD,OAAO,IAAI,CAAC9B,IAAI,CAAC0D,MAAM,CAAC,GAAG,IAAI,CAACxD,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B8B,IAAI,EAAEY;KACP,CAAC,CAACrC,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMkD,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;MACzC,IAAI,CAAC3C,UAAU,CAACkB,IAAI,CAACwB,WAAW,CAACc,MAAM,CAACjC,MAAM,IAAI,CAAC8B,GAAG,CAACI,QAAQ,CAAClC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACF7B,UAAU,CAAC0B,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACyB,MAAM,KAAK,CAAC,IAAIzB,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;QAC9C,OAAO,IAAI,CAACc,qBAAqB,CAACL,GAAG,CAAC;;MAExC,OAAO,IAAI,CAACjC,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAwC,YAAYA,CAACrC,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAAC0D,MAAM,CAAO,GAAG,IAAI,CAACxD,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,MAAMkD,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;MACzC,IAAI,CAAC3C,UAAU,CAACkB,IAAI,CAACwB,WAAW,CAACc,MAAM,CAACjC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACQwC,mBAAmBA,CAACtC,EAAU,EAAEuC,UAA2B;IACjE,MAAMnB,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;IACzC,MAAMmB,KAAK,GAAGpB,WAAW,CAACqB,SAAS,CAACxC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIwC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOvE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,MAAMqC,aAAa,GAAW;MAAE,GAAGtB,WAAW,CAACoB,KAAK,CAAC;MAAE,GAAGD;IAAU,CAAE;IACtEnB,WAAW,CAACoB,KAAK,CAAC,GAAGE,aAAa;IAClC,IAAI,CAAChE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGwB,WAAW,CAAC,CAAC;IAEtC,OAAO,IAAIpD,UAAU,CAAS2E,QAAQ,IAAG;MACvCC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/C,IAAI,CAAC8C,aAAa,CAAC;QAC5BC,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACQT,qBAAqBA,CAACL,GAAa;IACzC,MAAMX,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;IACzC,IAAI,CAAC3C,UAAU,CAACkB,IAAI,CAACwB,WAAW,CAACc,MAAM,CAACjC,MAAM,IAAI,CAAC8B,GAAG,CAACI,QAAQ,CAAClC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,IAAIhC,UAAU,CAAC2E,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/C,IAAI,CAAC;UAAEkD,YAAY,EAAEf,GAAG,CAACC;QAAM,CAAE,CAAC;QAC3CW,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACAhB,kBAAkBA,CAAA;IAChB5C,YAAY,CAAC8D,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACnE,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIoD,IAAIA,CAAA;IACN,OAAO,IAAI,CAACtE,UAAU,CAAC2C,KAAK;EAC9B;EAEA4B,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQC,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC;MACvE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBA9MUtF,aAAa,EAAAuF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb7F,aAAa;IAAA8F,OAAA,EAAb9F,aAAa,CAAA+F,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}