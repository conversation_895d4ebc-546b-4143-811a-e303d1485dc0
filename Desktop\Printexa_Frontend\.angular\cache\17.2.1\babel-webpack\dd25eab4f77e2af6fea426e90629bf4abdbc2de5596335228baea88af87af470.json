{"ast": null, "code": "import { __decorate, __param } from \"tslib\";\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ProduitModel } from '../../Model/Produit';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatSelectModule } from '@angular/material/select';\nimport { CommonModule } from '@angular/common';\nlet FormProduitComponent = class FormProduitComponent {\n  constructor(dialogRef, data, produitService, fb) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.produitService = produitService;\n    this.fb = fb;\n    this.isDetails = false;\n    // Types de produits disponibles\n    this.typesProduits = [{\n      value: 'Service',\n      label: 'Service'\n    }, {\n      value: 'Produit',\n      label: 'Produit'\n    }, {\n      value: 'Matériel',\n      label: 'Matériel'\n    }, {\n      value: 'Logiciel',\n      label: 'Logiciel'\n    }, {\n      value: 'Formation',\n      label: 'Formation'\n    }, {\n      value: 'Consultation',\n      label: 'Consultation'\n    }];\n    this.formControl = new UntypedFormControl('', [Validators.required]);\n    // Set the defaults\n    this.action = data.action;\n    if (this.action === 'edit') {\n      this.isDetails = false;\n      this.dialogTitle = `Modifier: ${data.produit.type}`;\n      this.produit = new ProduitModel(data.produit);\n      this.produitForm = this.createProduitForm();\n    } else if (this.action === 'details') {\n      this.produit = new ProduitModel(data.produit);\n      this.isDetails = true;\n    } else {\n      this.isDetails = false;\n      this.dialogTitle = 'Nouveau Produit';\n      const blankObject = {};\n      this.produit = new ProduitModel(blankObject);\n      this.produitForm = this.createProduitForm();\n    }\n  }\n  getErrorMessage() {\n    return this.formControl.hasError('required') ? 'Champ requis' : this.formControl.hasError('min') ? 'La valeur doit être positive' : '';\n  }\n  createProduitForm() {\n    const form = this.fb.group({\n      id: [this.produit.id],\n      type: [this.produit.type, [Validators.required]],\n      description: [this.produit.description, [Validators.required]],\n      prixUnitaireHT: [this.produit.prixUnitaireHT, [Validators.required, Validators.min(0)]],\n      tva: [this.produit.tva, [Validators.required, Validators.min(0), Validators.max(100)]],\n      codeProd: [this.produit.codeProd]\n    });\n    // Calculer automatiquement le prix TTC quand le prix HT ou la TVA change\n    form.get('prixUnitaireHT')?.valueChanges.subscribe(() => {\n      this.calculateTTC();\n    });\n    form.get('tva')?.valueChanges.subscribe(() => {\n      this.calculateTTC();\n    });\n    return form;\n  }\n  calculateTTC() {\n    if (this.produitForm) {\n      const prixHT = this.produitForm.get('prixUnitaireHT')?.value || 0;\n      const tva = this.produitForm.get('tva')?.value || 0;\n      const prixTTC = this.produitService.calculateTTC(prixHT, tva);\n      this.produit.prixUnitaireTTC = prixTTC;\n    }\n  }\n  submit() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      if (this.action === 'add') {\n        this.confirmAdd();\n      } else if (this.action === 'edit') {\n        this.confirmEdit();\n      }\n    }\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmAdd() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      const createDto = {\n        type: formValue.type,\n        description: formValue.description,\n        prixUnitaireHT: formValue.prixUnitaireHT,\n        tva: formValue.tva,\n        codeProd: formValue.codeProd\n      };\n      this.produitService.createProduit(createDto).subscribe({\n        next: result => {\n          this.dialogRef.close(result);\n        },\n        error: error => {\n          console.error('Erreur lors de la création du produit:', error);\n        }\n      });\n    }\n  }\n  confirmEdit() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      const updateDto = {\n        type: formValue.type,\n        description: formValue.description,\n        prixUnitaireHT: formValue.prixUnitaireHT,\n        tva: formValue.tva,\n        codeProd: formValue.codeProd\n      };\n      this.produitService.updateProduit(this.produit.id, updateDto).subscribe({\n        next: () => {\n          this.dialogRef.close(true);\n        },\n        error: error => {\n          console.error('Erreur lors de la modification du produit:', error);\n        }\n      });\n    }\n  }\n};\nFormProduitComponent = __decorate([Component({\n  selector: 'app-form-produit',\n  templateUrl: './form-produit.component.html',\n  styleUrls: ['./form-produit.component.scss'],\n  standalone: true,\n  imports: [MatButtonModule, MatIconModule, MatDialogContent, FormsModule, ReactiveFormsModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatDialogClose, MatCardModule, CommonModule]\n}), __param(1, Inject(MAT_DIALOG_DATA))], FormProduitComponent);\nexport { FormProduitComponent };", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "Component", "Inject", "UntypedFormControl", "Validators", "FormsModule", "ReactiveFormsModule", "ProduitModel", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatIconModule", "MatButtonModule", "MatSelectModule", "CommonModule", "FormProduitComponent", "constructor", "dialogRef", "data", "produitService", "fb", "isDetails", "typesProduits", "value", "label", "formControl", "required", "action", "dialogTitle", "produit", "type", "produitForm", "createProduitForm", "blankObject", "getErrorMessage", "<PERSON><PERSON><PERSON><PERSON>", "form", "group", "id", "description", "prixUnitaireHT", "min", "tva", "max", "codeProd", "get", "valueChanges", "subscribe", "calculateTTC", "prixHT", "prixTTC", "prixUnitaireTTC", "submit", "valid", "formValue", "getRawValue", "confirmAdd", "confirmEdit", "onNoClick", "close", "createDto", "createProduit", "next", "result", "error", "console", "updateDto", "updateProduit", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogClose", "__param"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\form-produit\\form-produit.component.ts"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { Produit, ProduitModel, CreateProduitDTO, UpdateProduitDTO } from '../../Model/Produit';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface DialogData {\r\n  id: string;\r\n  action: string;\r\n  produit: Produit;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-form-produit',\r\n  templateUrl: './form-produit.component.html',\r\n  styleUrls: ['./form-produit.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatDialogContent,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatDialogClose,\r\n    MatCardModule,\r\n    CommonModule,\r\n  ],\r\n})\r\nexport class FormProduitComponent {\r\n  action: string;\r\n  dialogTitle?: string;\r\n  isDetails = false;\r\n  produitForm?: UntypedFormGroup;\r\n  produit: ProduitModel;\r\n\r\n  // Types de produits disponibles\r\n  typesProduits = [\r\n    { value: 'Service', label: 'Service' },\r\n    { value: 'Produit', label: 'Produit' },\r\n    { value: 'Matériel', label: 'Matériel' },\r\n    { value: 'Logiciel', label: 'Logiciel' },\r\n    { value: 'Formation', label: 'Formation' },\r\n    { value: 'Consultation', label: 'Consultation' }\r\n  ];\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<FormProduitComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public produitService: ProduitService,\r\n    private fb: UntypedFormBuilder\r\n  ) {\r\n    // Set the defaults\r\n    this.action = data.action;\r\n    if (this.action === 'edit') {\r\n      this.isDetails = false;\r\n      this.dialogTitle = `Modifier: ${data.produit.type}`;\r\n      this.produit = new ProduitModel(data.produit);\r\n      this.produitForm = this.createProduitForm();\r\n    } else if (this.action === 'details') {\r\n      this.produit = new ProduitModel(data.produit);\r\n      this.isDetails = true;\r\n    } else {\r\n      this.isDetails = false;\r\n      this.dialogTitle = 'Nouveau Produit';\r\n      const blankObject = {} as Produit;\r\n      this.produit = new ProduitModel(blankObject);\r\n      this.produitForm = this.createProduitForm();\r\n    }\r\n  }\r\n\r\n  formControl = new UntypedFormControl('', [\r\n    Validators.required,\r\n  ]);\r\n\r\n  getErrorMessage() {\r\n    return this.formControl.hasError('required')\r\n      ? 'Champ requis'\r\n      : this.formControl.hasError('min')\r\n      ? 'La valeur doit être positive'\r\n      : '';\r\n  }\r\n\r\n  createProduitForm(): UntypedFormGroup {\r\n    const form = this.fb.group({\r\n      id: [this.produit.id],\r\n      type: [this.produit.type, [Validators.required]],\r\n      description: [this.produit.description, [Validators.required]],\r\n      prixUnitaireHT: [this.produit.prixUnitaireHT, [Validators.required, Validators.min(0)]],\r\n      tva: [this.produit.tva, [Validators.required, Validators.min(0), Validators.max(100)]],\r\n      codeProd: [this.produit.codeProd],\r\n    });\r\n\r\n    // Calculer automatiquement le prix TTC quand le prix HT ou la TVA change\r\n    form.get('prixUnitaireHT')?.valueChanges.subscribe(() => {\r\n      this.calculateTTC();\r\n    });\r\n\r\n    form.get('tva')?.valueChanges.subscribe(() => {\r\n      this.calculateTTC();\r\n    });\r\n\r\n    return form;\r\n  }\r\n\r\n  calculateTTC() {\r\n    if (this.produitForm) {\r\n      const prixHT = this.produitForm.get('prixUnitaireHT')?.value || 0;\r\n      const tva = this.produitForm.get('tva')?.value || 0;\r\n      const prixTTC = this.produitService.calculateTTC(prixHT, tva);\r\n      this.produit.prixUnitaireTTC = prixTTC;\r\n    }\r\n  }\r\n\r\n  submit() {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n\r\n      if (this.action === 'add') {\r\n        this.confirmAdd();\r\n      } else if (this.action === 'edit') {\r\n        this.confirmEdit();\r\n      }\r\n    }\r\n  }\r\n\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  public confirmAdd(): void {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n      const createDto: CreateProduitDTO = {\r\n        type: formValue.type,\r\n        description: formValue.description,\r\n        prixUnitaireHT: formValue.prixUnitaireHT,\r\n        tva: formValue.tva,\r\n        codeProd: formValue.codeProd\r\n      };\r\n\r\n      this.produitService.createProduit(createDto).subscribe({\r\n        next: (result) => {\r\n          this.dialogRef.close(result);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la création du produit:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  public confirmEdit(): void {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n      const updateDto: UpdateProduitDTO = {\r\n        type: formValue.type,\r\n        description: formValue.description,\r\n        prixUnitaireHT: formValue.prixUnitaireHT,\r\n        tva: formValue.tva,\r\n        codeProd: formValue.codeProd\r\n      };\r\n\r\n      this.produitService.updateProduit(this.produit.id, updateDto).subscribe({\r\n        next: () => {\r\n          this.dialogRef.close(true);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la modification du produit:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n"], "mappings": ";AAAA,SAASA,eAAe,QAAsB,0BAA0B;AACxE,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAEjD,SAASC,kBAAkB,EAAEC,UAAU,EAAwCC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvI,SAAkBC,YAAY,QAA4C,qBAAqB;AAC/F,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;AA2BvC,IAAMC,oBAAoB,GAA1B,MAAMA,oBAAoB;EAiB/BC,YACSC,SAA6C,EAC3BC,IAAuB,EACzCC,cAA8B,EAC7BC,EAAsB;IAHvB,KAAAH,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,EAAE,GAAFA,EAAE;IAlBZ,KAAAC,SAAS,GAAG,KAAK;IAIjB;IACA,KAAAC,aAAa,GAAG,CACd;MAAEC,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAED,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACtC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,EACxC;MAAED,KAAK,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAED,KAAK,EAAE,cAAc;MAAEC,KAAK,EAAE;IAAc,CAAE,CACjD;IA2BD,KAAAC,WAAW,GAAG,IAAItB,kBAAkB,CAAC,EAAE,EAAE,CACvCC,UAAU,CAACsB,QAAQ,CACpB,CAAC;IArBA;IACA,IAAI,CAACC,MAAM,GAAGT,IAAI,CAACS,MAAM;IACzB,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAI,CAACN,SAAS,GAAG,KAAK;MACtB,IAAI,CAACO,WAAW,GAAG,aAAaV,IAAI,CAACW,OAAO,CAACC,IAAI,EAAE;MACnD,IAAI,CAACD,OAAO,GAAG,IAAItB,YAAY,CAACW,IAAI,CAACW,OAAO,CAAC;MAC7C,IAAI,CAACE,WAAW,GAAG,IAAI,CAACC,iBAAiB,EAAE;KAC5C,MAAM,IAAI,IAAI,CAACL,MAAM,KAAK,SAAS,EAAE;MACpC,IAAI,CAACE,OAAO,GAAG,IAAItB,YAAY,CAACW,IAAI,CAACW,OAAO,CAAC;MAC7C,IAAI,CAACR,SAAS,GAAG,IAAI;KACtB,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACO,WAAW,GAAG,iBAAiB;MACpC,MAAMK,WAAW,GAAG,EAAa;MACjC,IAAI,CAACJ,OAAO,GAAG,IAAItB,YAAY,CAAC0B,WAAW,CAAC;MAC5C,IAAI,CAACF,WAAW,GAAG,IAAI,CAACC,iBAAiB,EAAE;;EAE/C;EAMAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACT,WAAW,CAACU,QAAQ,CAAC,UAAU,CAAC,GACxC,cAAc,GACd,IAAI,CAACV,WAAW,CAACU,QAAQ,CAAC,KAAK,CAAC,GAChC,8BAA8B,GAC9B,EAAE;EACR;EAEAH,iBAAiBA,CAAA;IACf,MAAMI,IAAI,GAAG,IAAI,CAAChB,EAAE,CAACiB,KAAK,CAAC;MACzBC,EAAE,EAAE,CAAC,IAAI,CAACT,OAAO,CAACS,EAAE,CAAC;MACrBR,IAAI,EAAE,CAAC,IAAI,CAACD,OAAO,CAACC,IAAI,EAAE,CAAC1B,UAAU,CAACsB,QAAQ,CAAC,CAAC;MAChDa,WAAW,EAAE,CAAC,IAAI,CAACV,OAAO,CAACU,WAAW,EAAE,CAACnC,UAAU,CAACsB,QAAQ,CAAC,CAAC;MAC9Dc,cAAc,EAAE,CAAC,IAAI,CAACX,OAAO,CAACW,cAAc,EAAE,CAACpC,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACqC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvFC,GAAG,EAAE,CAAC,IAAI,CAACb,OAAO,CAACa,GAAG,EAAE,CAACtC,UAAU,CAACsB,QAAQ,EAAEtB,UAAU,CAACqC,GAAG,CAAC,CAAC,CAAC,EAAErC,UAAU,CAACuC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACtFC,QAAQ,EAAE,CAAC,IAAI,CAACf,OAAO,CAACe,QAAQ;KACjC,CAAC;IAEF;IACAR,IAAI,CAACS,GAAG,CAAC,gBAAgB,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAC,MAAK;MACtD,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IAEFZ,IAAI,CAACS,GAAG,CAAC,KAAK,CAAC,EAAEC,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3C,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IAEF,OAAOZ,IAAI;EACb;EAEAY,YAAYA,CAAA;IACV,IAAI,IAAI,CAACjB,WAAW,EAAE;MACpB,MAAMkB,MAAM,GAAG,IAAI,CAAClB,WAAW,CAACc,GAAG,CAAC,gBAAgB,CAAC,EAAEtB,KAAK,IAAI,CAAC;MACjE,MAAMmB,GAAG,GAAG,IAAI,CAACX,WAAW,CAACc,GAAG,CAAC,KAAK,CAAC,EAAEtB,KAAK,IAAI,CAAC;MACnD,MAAM2B,OAAO,GAAG,IAAI,CAAC/B,cAAc,CAAC6B,YAAY,CAACC,MAAM,EAAEP,GAAG,CAAC;MAC7D,IAAI,CAACb,OAAO,CAACsB,eAAe,GAAGD,OAAO;;EAE1C;EAEAE,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACrB,WAAW,EAAEsB,KAAK,EAAE;MAC3B,MAAMC,SAAS,GAAG,IAAI,CAACvB,WAAW,CAACwB,WAAW,EAAE;MAEhD,IAAI,IAAI,CAAC5B,MAAM,KAAK,KAAK,EAAE;QACzB,IAAI,CAAC6B,UAAU,EAAE;OAClB,MAAM,IAAI,IAAI,CAAC7B,MAAM,KAAK,MAAM,EAAE;QACjC,IAAI,CAAC8B,WAAW,EAAE;;;EAGxB;EAEAC,SAASA,CAAA;IACP,IAAI,CAACzC,SAAS,CAAC0C,KAAK,EAAE;EACxB;EAEOH,UAAUA,CAAA;IACf,IAAI,IAAI,CAACzB,WAAW,EAAEsB,KAAK,EAAE;MAC3B,MAAMC,SAAS,GAAG,IAAI,CAACvB,WAAW,CAACwB,WAAW,EAAE;MAChD,MAAMK,SAAS,GAAqB;QAClC9B,IAAI,EAAEwB,SAAS,CAACxB,IAAI;QACpBS,WAAW,EAAEe,SAAS,CAACf,WAAW;QAClCC,cAAc,EAAEc,SAAS,CAACd,cAAc;QACxCE,GAAG,EAAEY,SAAS,CAACZ,GAAG;QAClBE,QAAQ,EAAEU,SAAS,CAACV;OACrB;MAED,IAAI,CAACzB,cAAc,CAAC0C,aAAa,CAACD,SAAS,CAAC,CAACb,SAAS,CAAC;QACrDe,IAAI,EAAGC,MAAM,IAAI;UACf,IAAI,CAAC9C,SAAS,CAAC0C,KAAK,CAACI,MAAM,CAAC;QAC9B,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;OACD,CAAC;;EAEN;EAEOP,WAAWA,CAAA;IAChB,IAAI,IAAI,CAAC1B,WAAW,EAAEsB,KAAK,EAAE;MAC3B,MAAMC,SAAS,GAAG,IAAI,CAACvB,WAAW,CAACwB,WAAW,EAAE;MAChD,MAAMW,SAAS,GAAqB;QAClCpC,IAAI,EAAEwB,SAAS,CAACxB,IAAI;QACpBS,WAAW,EAAEe,SAAS,CAACf,WAAW;QAClCC,cAAc,EAAEc,SAAS,CAACd,cAAc;QACxCE,GAAG,EAAEY,SAAS,CAACZ,GAAG;QAClBE,QAAQ,EAAEU,SAAS,CAACV;OACrB;MAED,IAAI,CAACzB,cAAc,CAACgD,aAAa,CAAC,IAAI,CAACtC,OAAO,CAACS,EAAE,EAAE4B,SAAS,CAAC,CAACnB,SAAS,CAAC;QACtEe,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC7C,SAAS,CAAC0C,KAAK,CAAC,IAAI,CAAC;QAC5B,CAAC;QACDK,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QACpE;OACD,CAAC;;EAEN;CACD;AAhJYjD,oBAAoB,GAAAqD,UAAA,EAnBhCnE,SAAS,CAAC;EACToE,QAAQ,EAAE,kBAAkB;EAC5BC,WAAW,EAAE,+BAA+B;EAC5CC,SAAS,EAAE,CAAC,+BAA+B,CAAC;EAC5CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP7D,eAAe,EACfD,aAAa,EACb+D,gBAAgB,EAChBrE,WAAW,EACXC,mBAAmB,EACnBI,kBAAkB,EAClBD,cAAc,EACdI,eAAe,EACf8D,cAAc,EACdnE,aAAa,EACbM,YAAY;CAEf,CAAC,EAoBG8D,OAAA,IAAA1E,MAAM,CAACF,eAAe,CAAC,E,EAnBfe,oBAAoB,CAgJhC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}