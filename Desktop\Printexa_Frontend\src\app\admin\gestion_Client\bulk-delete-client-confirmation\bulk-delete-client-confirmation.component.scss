.container {
  padding: 20px;
  max-width: 600px;

  h3[mat-dialog-title] {
    display: flex;
    align-items: center;
    color: #d32f2f;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 20px;

    .warning-icon {
      margin-right: 12px;
      font-size: 28px;
      color: #ff9800;
    }
  }

  .warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
    color: #856404;

    p {
      margin: 0;
      font-weight: 500;
      line-height: 1.5;

      strong {
        color: #d32f2f;
      }
    }
  }

  .summary-section {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;

    h4 {
      color: #495057;
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      margin-top: 0;
    }

    .summary-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 12px;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        background-color: white;
        border-radius: 6px;
        border: 1px solid #dee2e6;

        mat-icon {
          color: #6c757d;
          font-size: 20px;
        }

        .stat-label {
          color: #495057;
          font-weight: 500;
          flex: 1;
        }

        .stat-value {
          color: #d32f2f;
          font-weight: 600;
          font-size: 16px;
        }
      }
    }
  }

  .clients-preview {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    h5 {
      color: #495057;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      margin-top: 0;
    }

    .client-list {
      max-height: 200px;
      overflow-y: auto;

      .client-item {
        padding: 8px 12px;
        margin-bottom: 8px;
        background-color: white;
        border: 1px solid #dee2e6;
        border-radius: 6px;

        &:last-child {
          margin-bottom: 0;
        }

        .client-info {
          display: flex;
          align-items: center;
          gap: 12px;

          .client-code {
            background-color: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
            min-width: 80px;
            text-align: center;
          }

          .client-syntax {
            flex: 1;
            color: #495057;
            font-weight: 500;
          }

          .client-contact {
            display: flex;
            gap: 4px;

            mat-icon {
              font-size: 16px;
              color: #28a745;
            }
          }
        }
      }
    }
  }

  .large-selection-message {
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;

    p {
      display: flex;
      align-items: center;
      margin: 0 0 12px 0;
      color: #1976d2;
      font-weight: 500;

      mat-icon {
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .codes-preview {
      .codes-list {
        color: #666;
        font-size: 14px;
        line-height: 1.4;
      }
    }
  }

  div[mat-dialog-actions] {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;

    button {
      min-width: 140px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      &.delete-btn {
        background-color: #d32f2f;
        color: white;

        &:hover {
          background-color: #b71c1c;
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);
        }
      }

      &.cancel-btn {
        background-color: #6c757d;
        color: white;

        &:hover {
          background-color: #5a6268;
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 16px;

    .summary-section {
      .summary-stats {
        grid-template-columns: 1fr;

        .stat-item {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;

          .stat-label {
            flex: none;
          }
        }
      }
    }

    div[mat-dialog-actions] {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
        min-width: auto;
      }
    }
  }
}

// Animation
.container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
