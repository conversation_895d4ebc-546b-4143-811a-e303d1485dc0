{"ast": null, "code": "import { ClientService } from '../../services/client.service';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { DataSource } from '@angular/cdk/collections';\nimport { FormClientComponent } from '../form-client/form-client.component';\nimport { ClientDeleteComponent } from '../client-delete/client-delete.component';\nimport { BulkDeleteClientConfirmationComponent } from '../bulk-delete-client-confirmation/';\nimport { BehaviorSubject, fromEvent, merge } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\nimport { TableExportUtil } from '@shared';\nimport { NgClass, CommonModule } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../../services/client.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/sort\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/paginator\";\nimport * as i12 from \"@angular/common\";\nimport * as i13 from \"@angular/material/form-field\";\nimport * as i14 from \"@angular/material/input\";\nconst _c0 = [\"filter\"];\nfunction ClientComponent_Conditional_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 47);\n    i0.ɵɵlistener(\"click\", function ClientComponent_Conditional_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const ctx_r20 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r20.removeSelectedRows());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Supprimer S\\u00E9lection \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_th_39_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"th\", 48)(1, \"mat-checkbox\", 49);\n    i0.ɵɵlistener(\"change\", function ClientComponent_th_39_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r22.masterToggle() : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r3.selection.hasValue() && ctx_r3.isAllSelected())(\"indeterminate\", ctx_r3.selection.hasValue() && !ctx_r3.isAllSelected());\n  }\n}\nfunction ClientComponent_td_40_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 50)(1, \"mat-checkbox\", 51);\n    i0.ɵɵlistener(\"click\", function ClientComponent_td_40_Template_mat_checkbox_click_1_listener($event) {\n      return $event.stopPropagation();\n    })(\"change\", function ClientComponent_td_40_Template_mat_checkbox_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const row_r24 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r26.selection.toggle(row_r24) : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r24 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r4.selection.isSelected(row_r24));\n  }\n}\nfunction ClientComponent_th_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Code Client\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 53)(1, \"strong\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r28 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(row_r28.code);\n  }\n}\nfunction ClientComponent_th_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Raison Sociale\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r29 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r29.syntax || \"Non d\\u00E9fini\", \" \");\n  }\n}\nfunction ClientComponent_th_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Matricule Fiscal\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 55);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r30 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r30.matFiscal || \"Non d\\u00E9fini\", \" \");\n  }\n}\nfunction ClientComponent_th_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"Email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_52_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 59);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r31 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", \"mailto:\" + row_r31.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r31.email, \" \");\n  }\n}\nfunction ClientComponent_td_52_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1, \"Non d\\u00E9fini\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 56);\n    i0.ɵɵtemplate(1, ClientComponent_td_52_a_1_Template, 2, 2, \"a\", 57)(2, ClientComponent_td_52_span_2_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r31 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", row_r31.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !row_r31.email);\n  }\n}\nfunction ClientComponent_th_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 52);\n    i0.ɵɵtext(1, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_55_a_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 63);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r35 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵproperty(\"href\", \"tel:\" + row_r35.telephone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", row_r35.telephone, \" \");\n  }\n}\nfunction ClientComponent_td_55_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 60);\n    i0.ɵɵtext(1, \"Non d\\u00E9fini\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 61);\n    i0.ɵɵtemplate(1, ClientComponent_td_55_a_1_Template, 2, 2, \"a\", 62)(2, ClientComponent_td_55_span_2_Template, 2, 0, \"span\", 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r35 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", row_r35.telephone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !row_r35.telephone);\n  }\n}\nfunction ClientComponent_th_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 64);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ClientComponent_td_58_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r41 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 65)(1, \"button\", 66);\n    i0.ɵɵlistener(\"click\", function ClientComponent_td_58_Template_button_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r41);\n      const row_r39 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.editCall(row_r39));\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"edit\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"button\", 67);\n    i0.ɵɵlistener(\"click\", function ClientComponent_td_58_Template_button_click_4_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r41);\n      const row_r39 = restoredCtx.$implicit;\n      const ctx_r42 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r42.deleteItem(row_r39));\n    });\n    i0.ɵɵelementStart(5, \"mat-icon\");\n    i0.ɵɵtext(6, \"delete\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ClientComponent_tr_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 68);\n  }\n}\nconst _c1 = a0 => ({\n  \"selected\": a0\n});\nfunction ClientComponent_tr_60_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r45 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 69);\n    i0.ɵɵlistener(\"click\", function ClientComponent_tr_60_Template_tr_click_0_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r45);\n      const row_r43 = restoredCtx.$implicit;\n      const ctx_r44 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r44.selection.toggle(row_r43));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r43 = ctx.$implicit;\n    const ctx_r18 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c1, ctx_r18.selection.isSelected(row_r43)));\n  }\n}\nconst _c2 = () => [5, 10, 25, 100];\nexport class ClientComponent extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient, dialog, clientService, snackBar) {\n    super();\n    this.httpClient = httpClient;\n    this.dialog = dialog;\n    this.clientService = clientService;\n    this.snackBar = snackBar;\n    this.displayedColumns = ['select', 'code', 'syntax', 'matFiscal', 'email', 'telephone', 'actions'];\n    this.selection = new SelectionModel(true, []);\n    this.breadscrums = [{\n      title: 'Gestion des Clients',\n      items: ['Admin', 'Gestion'],\n      active: 'Clients'\n    }];\n  }\n  ngOnInit() {\n    this.loadData();\n  }\n  refresh() {\n    this.loadData();\n  }\n  addNew() {\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormClientComponent, {\n      data: {\n        client: this.client,\n        action: 'add'\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result === 1) {\n        this.exampleDatabase?.dataChange.value.unshift(this.clientService.getDialogData());\n        this.refreshTable();\n        this.showNotification('snackbar-success', 'Client ajouté avec succès...!!!', 'bottom', 'center');\n      }\n    });\n  }\n  editCall(row) {\n    this.id = row.id;\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormClientComponent, {\n      data: {\n        client: row,\n        action: 'edit'\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result === 1) {\n        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(x => x.id === this.id);\n        if (foundIndex != null && this.exampleDatabase) {\n          this.exampleDatabase.dataChange.value[foundIndex] = this.clientService.getDialogData();\n          this.refreshTable();\n          this.showNotification('black', 'Client modifié avec succès...!!!', 'bottom', 'center');\n        }\n      }\n    });\n  }\n  deleteItem(row) {\n    this.id = row.id;\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(ClientDeleteComponent, {\n      width: '500px',\n      maxWidth: '90vw',\n      data: row,\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result === 1) {\n        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(x => x.id === this.id);\n        if (foundIndex != null && this.exampleDatabase) {\n          this.exampleDatabase.dataChange.value.splice(foundIndex, 1);\n          this.refreshTable();\n          this.showNotification('snackbar-danger', 'Client supprimé avec succès...!!!', 'bottom', 'center');\n        }\n      }\n    });\n  }\n  refreshTable() {\n    this.paginator._changePageSize(this.paginator.pageSize);\n  }\n  isAllSelected() {\n    const numSelected = this.selection.selected.length;\n    const numRows = this.dataSource.renderedData.length;\n    return numSelected === numRows;\n  }\n  masterToggle() {\n    this.isAllSelected() ? this.selection.clear() : this.dataSource.renderedData.forEach(row => this.selection.select(row));\n  }\n  removeSelectedRows() {\n    const totalSelect = this.selection.selected.length;\n    if (totalSelect === 0) {\n      this.showNotification('snackbar-warning', 'Aucun client sélectionné pour la suppression', 'bottom', 'center');\n      return;\n    }\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(BulkDeleteClientConfirmationComponent, {\n      width: '600px',\n      maxWidth: '90vw',\n      data: {\n        selectedClients: this.selection.selected,\n        totalCount: totalSelect\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(confirmed => {\n      if (confirmed) {\n        const selectedIds = this.selection.selected.map(item => item.id);\n        this.clientService.deleteSelectedClients(selectedIds).subscribe({\n          next: () => {\n            this.selection.selected.forEach(item => {\n              const index = this.dataSource.renderedData.findIndex(d => d === item);\n              if (index !== -1) {\n                this.exampleDatabase?.dataChange.value.splice(index, 1);\n              }\n            });\n            this.selection = new SelectionModel(true, []);\n            this.refreshTable();\n            this.clientService.getAllClients().subscribe();\n            this.showNotification('snackbar-danger', `${totalSelect} client(s) supprimé(s) avec succès !`, 'bottom', 'center');\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression des clients:', error);\n            this.showNotification('snackbar-danger', 'Erreur lors de la suppression des clients. Veuillez réessayer.', 'bottom', 'center');\n          }\n        });\n      }\n    });\n  }\n  loadData() {\n    this.exampleDatabase = new ClientService(this.httpClient);\n    this.dataSource = new ExampleDataSource(this.exampleDatabase, this.paginator, this.sort);\n    this.subs.sink = fromEvent(this.filter?.nativeElement, 'keyup').subscribe(() => {\n      if (!this.dataSource) {\n        return;\n      }\n      this.dataSource.filter = this.filter?.nativeElement.value;\n    });\n  }\n  exportExcel() {\n    const exportData = this.dataSource.filteredData.map(x => ({\n      'Code Client': x.code,\n      'Raison Sociale': x.syntax,\n      'Matricule Fiscal': x.matFiscal,\n      'Email': x.email,\n      'Téléphone': x.telephone\n    }));\n    TableExportUtil.exportToExcel(exportData, 'clients');\n  }\n  showNotification(colorName, text, placementFrom, placementAlign) {\n    this.snackBar.open(text, '', {\n      duration: 2000,\n      verticalPosition: placementFrom,\n      horizontalPosition: placementAlign,\n      panelClass: colorName\n    });\n  }\n  static #_ = this.ɵfac = function ClientComponent_Factory(t) {\n    return new (t || ClientComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.ClientService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClientComponent,\n    selectors: [[\"app-client\"]],\n    viewQuery: function ClientComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatPaginator, 7);\n        i0.ɵɵviewQuery(MatSort, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 63,\n    vars: 12,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-xs-12\", \"col-sm-12\", \"col-md-12\", \"col-lg-12\"], [1, \"card\"], [1, \"header\"], [1, \"header-dropdown\"], [\"mat-icon-button\", \"\", \"aria-label\", \"refresh\", 1, \"example-icon\", \"favorite-icon\", 3, \"click\"], [1, \"body\"], [1, \"responsive_table\"], [1, \"table-responsive\"], [1, \"table-header\"], [1, \"add-new\"], [\"mat-flat-button\", \"\", \"color\", \"primary\", 1, \"btn-space\", 3, \"click\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", \"class\", \"btn-space\"], [1, \"table-search\"], [\"floatLabel\", \"auto\", 1, \"search-form-field\"], [\"matInput\", \"\", \"placeholder\", \"Rechercher...\", \"autocomplete\", \"off\"], [\"filter\", \"\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"mat-flat-button\", \"\", \"aria-label\", \"search\", 1, \"search-icon\"], [1, \"table-actions\"], [\"mat-icon-button\", \"\", \"color\", \"primary\", \"matTooltip\", \"Exporter Excel\", 1, \"btn-space\", 3, \"click\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"mat-elevation-z8\", \"data-table\", 3, \"dataSource\"], [\"table\", \"\"], [\"matColumnDef\", \"select\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"code\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"client-code\", 4, \"matCellDef\"], [\"matColumnDef\", \"syntax\"], [\"mat-cell\", \"\", \"class\", \"client-syntax\", 4, \"matCellDef\"], [\"matColumnDef\", \"matFiscal\"], [\"mat-cell\", \"\", \"class\", \"client-matfiscal\", 4, \"matCellDef\"], [\"matColumnDef\", \"email\"], [\"mat-cell\", \"\", \"class\", \"client-email\", 4, \"matCellDef\"], [\"matColumnDef\", \"telephone\"], [\"mat-cell\", \"\", \"class\", \"client-telephone\", 4, \"matCellDef\"], [\"matColumnDef\", \"actions\"], [\"mat-header-cell\", \"\", \"class\", \"pr-0\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", \"class\", \"action-link\", 4, \"matCellDef\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 3, \"ngClass\", \"click\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"showFirstLastButtons\", \"\", 3, \"length\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [\"paginator\", \"\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"btn-space\", 3, \"click\"], [\"mat-header-cell\", \"\"], [3, \"checked\", \"indeterminate\", \"change\"], [\"mat-cell\", \"\"], [3, \"checked\", \"click\", \"change\"], [\"mat-header-cell\", \"\", \"mat-sort-header\", \"\"], [\"mat-cell\", \"\", 1, \"client-code\"], [\"mat-cell\", \"\", 1, \"client-syntax\"], [\"mat-cell\", \"\", 1, \"client-matfiscal\"], [\"mat-cell\", \"\", 1, \"client-email\"], [\"class\", \"email-link\", 3, \"href\", 4, \"ngIf\"], [\"class\", \"no-data\", 4, \"ngIf\"], [1, \"email-link\", 3, \"href\"], [1, \"no-data\"], [\"mat-cell\", \"\", 1, \"client-telephone\"], [\"class\", \"phone-link\", 3, \"href\", 4, \"ngIf\"], [1, \"phone-link\", 3, \"href\"], [\"mat-header-cell\", \"\", 1, \"pr-0\"], [\"mat-cell\", \"\", 1, \"action-link\"], [\"mat-icon-button\", \"\", \"color\", \"accent\", \"matTooltip\", \"Modifier\", 1, \"btn-tbl-edit\", 3, \"click\"], [\"mat-icon-button\", \"\", \"color\", \"warn\", \"matTooltip\", \"Supprimer\", 1, \"btn-tbl-delete\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\", 3, \"ngClass\", \"click\"]],\n    template: function ClientComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 7)(8, \"h2\")(9, \"strong\");\n        i0.ɵɵtext(10, \"Liste des Clients\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(11, \"div\", 8)(12, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ClientComponent_Template_button_click_12_listener() {\n          return ctx.refresh();\n        });\n        i0.ɵɵelementStart(13, \"mat-icon\");\n        i0.ɵɵtext(14, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(15, \"div\", 10)(16, \"div\", 11)(17, \"div\", 12)(18, \"div\", 13)(19, \"div\", 14)(20, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function ClientComponent_Template_button_click_20_listener() {\n          return ctx.addNew();\n        });\n        i0.ɵɵelementStart(21, \"mat-icon\");\n        i0.ɵɵtext(22, \"add\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(23, \" Ajouter Client \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(24, ClientComponent_Conditional_24_Template, 4, 0, \"button\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\", 17)(26, \"mat-form-field\", 18);\n        i0.ɵɵelement(27, \"input\", 19, 20);\n        i0.ɵɵelementStart(29, \"button\", 21)(30, \"mat-icon\");\n        i0.ɵɵtext(31, \"search\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(32, \"div\", 22)(33, \"button\", 23);\n        i0.ɵɵlistener(\"click\", function ClientComponent_Template_button_click_33_listener() {\n          return ctx.exportExcel();\n        });\n        i0.ɵɵelementStart(34, \"mat-icon\");\n        i0.ɵɵtext(35, \"get_app\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(36, \"table\", 24, 25);\n        i0.ɵɵelementContainerStart(38, 26);\n        i0.ɵɵtemplate(39, ClientComponent_th_39_Template, 2, 2, \"th\", 27)(40, ClientComponent_td_40_Template, 2, 1, \"td\", 28);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(41, 29);\n        i0.ɵɵtemplate(42, ClientComponent_th_42_Template, 2, 0, \"th\", 30)(43, ClientComponent_td_43_Template, 3, 1, \"td\", 31);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(44, 32);\n        i0.ɵɵtemplate(45, ClientComponent_th_45_Template, 2, 0, \"th\", 30)(46, ClientComponent_td_46_Template, 2, 1, \"td\", 33);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(47, 34);\n        i0.ɵɵtemplate(48, ClientComponent_th_48_Template, 2, 0, \"th\", 30)(49, ClientComponent_td_49_Template, 2, 1, \"td\", 35);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(50, 36);\n        i0.ɵɵtemplate(51, ClientComponent_th_51_Template, 2, 0, \"th\", 30)(52, ClientComponent_td_52_Template, 3, 2, \"td\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(53, 38);\n        i0.ɵɵtemplate(54, ClientComponent_th_54_Template, 2, 0, \"th\", 30)(55, ClientComponent_td_55_Template, 3, 2, \"td\", 39);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(56, 40);\n        i0.ɵɵtemplate(57, ClientComponent_th_57_Template, 2, 0, \"th\", 41)(58, ClientComponent_td_58_Template, 7, 0, \"td\", 42);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(59, ClientComponent_tr_59_Template, 1, 0, \"tr\", 43)(60, ClientComponent_tr_60_Template, 1, 3, \"tr\", 44);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelement(61, \"mat-paginator\", 45, 46);\n        i0.ɵɵelementEnd()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", ctx.breadscrums[0].title)(\"items\", ctx.breadscrums[0].items)(\"active_item\", ctx.breadscrums[0].active);\n        i0.ɵɵadvance(21);\n        i0.ɵɵconditional(24, ctx.selection.selected.length > 0 ? 24 : -1);\n        i0.ɵɵadvance(12);\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n        i0.ɵɵadvance(23);\n        i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"length\", ctx.dataSource.filteredData.length)(\"pageIndex\", 0)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(11, _c2));\n      }\n    },\n    dependencies: [BreadcrumbComponent, MatTooltipModule, i5.MatTooltip, MatButtonModule, i6.MatButton, i6.MatIconButton, MatIconModule, i7.MatIcon, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, MatSortModule, i9.MatSort, i9.MatSortHeader, NgClass, MatCheckboxModule, i10.MatCheckbox, MatRippleModule, MatProgressSpinnerModule, MatPaginatorModule, i11.MatPaginator, CommonModule, i12.NgIf, MatFormFieldModule, i13.MatFormField, i13.MatSuffix, MatInputModule, i14.MatInput],\n    styles: [\".responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .add-new[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  align-items: center;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .add-new[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .add-new[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%] {\\n  flex: 1;\\n  max-width: 300px;\\n  min-width: 200px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%]   .search-form-field[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%]   .search-form-field[_ngcontent-%COMP%]   .mat-form-field-wrapper[_ngcontent-%COMP%] {\\n  padding-bottom: 0;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%]   .search-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%] {\\n  padding: 12px 16px;\\n  border-radius: 4px;\\n  border: 1px solid #e0e0e0;\\n  font-size: 14px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%]   .search-form-field[_ngcontent-%COMP%]   input[_ngcontent-%COMP%]:focus {\\n  border-color: #1976d2;\\n  outline: none;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%]   .search-form-field[_ngcontent-%COMP%]   .search-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n  background: white;\\n  border-radius: 8px;\\n  overflow: hidden;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  color: #333;\\n  font-weight: 600;\\n  font-size: 14px;\\n  border-bottom: 2px solid #e0e0e0;\\n  padding: 16px 12px;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%] {\\n  padding: 16px 12px;\\n  border-bottom: 1px solid #f0f0f0;\\n  font-size: 14px;\\n  color: #333;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-code[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  font-weight: 600;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-syntax[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 500;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-matfiscal[_ngcontent-%COMP%] {\\n  font-family: \\\"Courier New\\\", monospace;\\n  color: #666;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-email[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: none;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-email[_ngcontent-%COMP%]   .email-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-email[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-style: italic;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-telephone[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n  text-decoration: none;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-telephone[_ngcontent-%COMP%]   .phone-link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell.client-telephone[_ngcontent-%COMP%]   .no-data[_ngcontent-%COMP%] {\\n  color: #999;\\n  font-style: italic;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%] {\\n  transition: background-color 0.2s ease;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f5f5f5;\\n  cursor: pointer;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-row.selected[_ngcontent-%COMP%] {\\n  background-color: rgba(25, 118, 210, 0.1);\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  justify-content: flex-start;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%]   .btn-tbl-edit[_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%]   .btn-tbl-edit[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 152, 0, 0.1);\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%]   .btn-tbl-delete[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n.responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%]   .btn-tbl-delete[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(244, 67, 54, 0.1);\\n}\\n.responsive_table[_ngcontent-%COMP%]   .mat-paginator[_ngcontent-%COMP%] {\\n  background: white;\\n  border-top: 1px solid #e0e0e0;\\n  border-radius: 0 0 8px 8px;\\n}\\n\\n.card[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  border: none;\\n}\\n.card[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  border-radius: 8px 8px 0 0;\\n  padding: 20px;\\n}\\n.card[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 20px;\\n  font-weight: 600;\\n}\\n.card[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  color: white;\\n}\\n.card[_ngcontent-%COMP%]   .header[_ngcontent-%COMP%]   .header-dropdown[_ngcontent-%COMP%]   button[_ngcontent-%COMP%]:hover {\\n  background-color: rgba(255, 255, 255, 0.1);\\n}\\n.card[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n@media (max-width: 768px) {\\n  .responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .add-new[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    flex-wrap: wrap;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-search[_ngcontent-%COMP%] {\\n    max-width: none;\\n    order: -1;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .table-actions[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%], .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%] {\\n    padding: 12px 8px;\\n    font-size: 12px;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .action-link[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n  .card[_ngcontent-%COMP%]   .body[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .responsive_table[_ngcontent-%COMP%]   .table-header[_ngcontent-%COMP%]   .add-new[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-right: 0;\\n    margin-bottom: 8px;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%] {\\n    font-size: 11px;\\n  }\\n  .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-header-cell[_ngcontent-%COMP%], .responsive_table[_ngcontent-%COMP%]   .data-table[_ngcontent-%COMP%]   .mat-cell[_ngcontent-%COMP%] {\\n    padding: 8px 4px;\\n  }\\n}\\n.mat-row[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.mat-checkbox[_ngcontent-%COMP%]   .mat-checkbox-frame[_ngcontent-%COMP%] {\\n  border-color: #e0e0e0;\\n}\\n.mat-checkbox.mat-checkbox-checked[_ngcontent-%COMP%]   .mat-checkbox-background[_ngcontent-%COMP%] {\\n  background-color: #1976d2;\\n}\\n\\nbutton[mat-flat-button][_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n  transition: all 0.2s ease;\\n}\\nbutton[mat-flat-button][_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n}\\n\\nbutton[mat-icon-button][_ngcontent-%COMP%] {\\n  transition: all 0.2s ease;\\n}\\nbutton[mat-icon-button][_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}\nexport class ExampleDataSource extends DataSource {\n  get filter() {\n    return this.filterChange.value;\n  }\n  set filter(filter) {\n    this.filterChange.next(filter);\n  }\n  constructor(exampleDatabase, paginator, _sort) {\n    super();\n    this.exampleDatabase = exampleDatabase;\n    this.paginator = paginator;\n    this._sort = _sort;\n    this.filterChange = new BehaviorSubject('');\n    this.filteredData = [];\n    this.renderedData = [];\n    this.filterChange.subscribe(() => this.filter = this.filter);\n  }\n  connect() {\n    const displayDataChanges = [this.exampleDatabase.dataChange, this._sort.sortChange, this.filterChange, this.paginator.page];\n    this.exampleDatabase.getAllClients();\n    return merge(...displayDataChanges).pipe(map(() => {\n      this.filteredData = this.exampleDatabase.data.slice().filter(client => {\n        const searchStr = (client.code + client.syntax + client.matFiscal + client.email + client.telephone).toLowerCase();\n        return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\n      });\n      const sortedData = this.sortData(this.filteredData.slice());\n      const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n      this.renderedData = sortedData.splice(startIndex, this.paginator.pageSize);\n      return this.renderedData;\n    }));\n  }\n  disconnect() {\n    // disconnect\n  }\n  sortData(data) {\n    if (!this._sort.active || this._sort.direction === '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let propertyA = '';\n      let propertyB = '';\n      switch (this._sort.active) {\n        case 'id':\n          [propertyA, propertyB] = [a.id, b.id];\n          break;\n        case 'code':\n          [propertyA, propertyB] = [a.code, b.code];\n          break;\n        case 'syntax':\n          [propertyA, propertyB] = [a.syntax || '', b.syntax || ''];\n          break;\n        case 'matFiscal':\n          [propertyA, propertyB] = [a.matFiscal || '', b.matFiscal || ''];\n          break;\n        case 'email':\n          [propertyA, propertyB] = [a.email || '', b.email || ''];\n          break;\n        case 'telephone':\n          [propertyA, propertyB] = [a.telephone || '', b.telephone || ''];\n          break;\n      }\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\n      return (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1);\n    });\n  }\n}", "map": {"version": 3, "names": ["ClientService", "MatPaginator", "MatPaginatorModule", "MatSort", "MatSortModule", "DataSource", "FormClientComponent", "ClientDeleteComponent", "BulkDeleteClientConfirmationComponent", "BehaviorSubject", "fromEvent", "merge", "map", "SelectionModel", "UnsubscribeOnDestroyAdapter", "TableExportUtil", "Ng<PERSON><PERSON>", "CommonModule", "MatProgressSpinnerModule", "MatRippleModule", "MatCheckboxModule", "MatTableModule", "MatIconModule", "MatButtonModule", "MatTooltipModule", "BreadcrumbComponent", "MatFormFieldModule", "MatInputModule", "i0", "ɵɵelementStart", "ɵɵlistener", "ClientComponent_Conditional_24_Template_button_click_0_listener", "ɵɵrestoreView", "_r21", "ctx_r20", "ɵɵnextContext", "ɵɵresetView", "removeSelectedRows", "ɵɵtext", "ɵɵelementEnd", "ClientComponent_th_39_Template_mat_checkbox_change_1_listener", "$event", "_r23", "ctx_r22", "masterToggle", "ɵɵadvance", "ɵɵproperty", "ctx_r3", "selection", "hasValue", "isAllSelected", "ClientComponent_td_40_Template_mat_checkbox_click_1_listener", "stopPropagation", "ClientComponent_td_40_Template_mat_checkbox_change_1_listener", "restoredCtx", "_r27", "row_r24", "$implicit", "ctx_r26", "toggle", "ctx_r4", "isSelected", "ɵɵtextInterpolate", "row_r28", "code", "ɵɵtextInterpolate1", "row_r29", "syntax", "row_r30", "mat<PERSON><PERSON><PERSON>", "row_r31", "email", "ɵɵsanitizeUrl", "ɵɵtemplate", "ClientComponent_td_52_a_1_Template", "ClientComponent_td_52_span_2_Template", "row_r35", "telephone", "ClientComponent_td_55_a_1_Template", "ClientComponent_td_55_span_2_Template", "ClientComponent_td_58_Template_button_click_1_listener", "_r41", "row_r39", "ctx_r40", "editCall", "ClientComponent_td_58_Template_button_click_4_listener", "ctx_r42", "deleteItem", "ɵɵelement", "ClientComponent_tr_60_Template_tr_click_0_listener", "_r45", "row_r43", "ctx_r44", "ɵɵpureFunction1", "_c1", "ctx_r18", "ClientComponent", "constructor", "httpClient", "dialog", "clientService", "snackBar", "displayedColumns", "breadscrums", "title", "items", "active", "ngOnInit", "loadData", "refresh", "addNew", "tempDirection", "localStorage", "getItem", "dialogRef", "open", "data", "client", "action", "direction", "subs", "sink", "afterClosed", "subscribe", "result", "exampleDatabase", "dataChange", "value", "unshift", "getDialogData", "refreshTable", "showNotification", "row", "id", "foundIndex", "findIndex", "x", "width", "max<PERSON><PERSON><PERSON>", "splice", "paginator", "_changePageSize", "pageSize", "numSelected", "selected", "length", "numRows", "dataSource", "renderedData", "clear", "for<PERSON>ach", "select", "totalSelect", "selectedClients", "totalCount", "confirmed", "selectedIds", "item", "deleteSelectedClients", "next", "index", "d", "getAllClients", "error", "console", "ExampleDataSource", "sort", "filter", "nativeElement", "exportExcel", "exportData", "filteredData", "exportToExcel", "colorName", "text", "placementFrom", "placementAlign", "duration", "verticalPosition", "horizontalPosition", "panelClass", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "MatDialog", "i3", "i4", "MatSnackBar", "_2", "selectors", "viewQuery", "ClientComponent_Query", "rf", "ctx", "ClientComponent_Template_button_click_12_listener", "ClientComponent_Template_button_click_20_listener", "ClientComponent_Conditional_24_Template", "ClientComponent_Template_button_click_33_listener", "ɵɵelementContainerStart", "ClientComponent_th_39_Template", "ClientComponent_td_40_Template", "ɵɵelementContainerEnd", "ClientComponent_th_42_Template", "ClientComponent_td_43_Template", "ClientComponent_th_45_Template", "ClientComponent_td_46_Template", "ClientComponent_th_48_Template", "ClientComponent_td_49_Template", "ClientComponent_th_51_Template", "ClientComponent_td_52_Template", "ClientComponent_th_54_Template", "ClientComponent_td_55_Template", "ClientComponent_th_57_Template", "ClientComponent_td_58_Template", "ClientComponent_tr_59_Template", "ClientComponent_tr_60_Template", "ɵɵconditional", "ɵɵpureFunction0", "_c2", "i5", "MatTooltip", "i6", "MatButton", "MatIconButton", "i7", "MatIcon", "i8", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i9", "Mat<PERSON>ort<PERSON><PERSON>er", "i10", "MatCheckbox", "i11", "i12", "NgIf", "i13", "MatFormField", "MatSuffix", "i14", "MatInput", "styles", "filterChange", "_sort", "connect", "displayDataChanges", "sortChange", "page", "pipe", "slice", "searchStr", "toLowerCase", "indexOf", "sortedData", "sortData", "startIndex", "pageIndex", "disconnect", "a", "b", "propertyA", "propertyB", "valueA", "isNaN", "valueB"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client\\client.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client\\client.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\nimport { ClientService } from '../../services/client.service';\nimport { HttpClient } from '@angular/common/http';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport { Client } from '../../Model/Client';\nimport { DataSource } from '@angular/cdk/collections';\nimport { FormClientComponent } from '../form-client/form-client.component';\nimport { ClientDeleteComponent } from '../client-delete/client-delete.component';\nimport { BulkDeleteClientConfirmationComponent } from '../bulk-delete-client-confirmation/';\nimport { BehaviorSubject, fromEvent, merge, Observable } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\nimport { Direction } from '@angular/cdk/bidi';\nimport { TableExportUtil, TableElement } from '@shared';\nimport { formatDate, NgClass, DatePipe, CommonModule } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport { MatSnackBar, MatSnackBarHorizontalPosition, MatSnackBarVerticalPosition } from '@angular/material/snack-bar';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\n\n@Component({\n  selector: 'app-client',\n  templateUrl: './client.component.html',\n  styleUrls: ['./client.component.scss'],\n  standalone: true,\n  imports: [\n    BreadcrumbComponent,\n    MatTooltipModule,\n    MatButtonModule,\n    MatIconModule,\n    MatTableModule,\n    MatSortModule,\n    NgClass,\n    MatCheckboxModule,\n    MatRippleModule,\n    MatProgressSpinnerModule,\n    MatPaginatorModule,\n    CommonModule,\n    MatFormFieldModule,\n    MatInputModule,\n  ],\n})\nexport class ClientComponent extends UnsubscribeOnDestroyAdapter implements OnInit {\n  displayedColumns = [\n    'select',\n    'code',\n    'syntax',\n    'matFiscal',\n    'email',\n    'telephone',\n    'actions',\n  ];\n  exampleDatabase?: ClientService;\n  dataSource!: ExampleDataSource;\n  selection = new SelectionModel<Client>(true, []);\n  id?: number;\n  client?: Client;\n\n  breadscrums = [\n    {\n      title: 'Gestion des Clients',\n      items: ['Admin', 'Gestion'],\n      active: 'Clients',\n    },\n  ];\n\n  constructor(\n    public httpClient: HttpClient,\n    public dialog: MatDialog,\n    public clientService: ClientService,\n    private snackBar: MatSnackBar\n  ) {\n    super();\n  }\n\n  @ViewChild(MatPaginator, { static: true })\n  paginator!: MatPaginator;\n  @ViewChild(MatSort, { static: true })\n  sort!: MatSort;\n  @ViewChild('filter', { static: true }) filter?: ElementRef;\n\n  ngOnInit() {\n    this.loadData();\n  }\n\n  refresh() {\n    this.loadData();\n  }\n\n  addNew() {\n    let tempDirection: Direction;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormClientComponent, {\n      data: {\n        client: this.client,\n        action: 'add',\n      },\n      direction: tempDirection,\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\n      if (result === 1) {\n        this.exampleDatabase?.dataChange.value.unshift(\n          this.clientService.getDialogData()\n        );\n        this.refreshTable();\n        this.showNotification(\n          'snackbar-success',\n          'Client ajouté avec succès...!!!',\n          'bottom',\n          'center'\n        );\n      }\n    });\n  }\n\n  editCall(row: Client) {\n    this.id = row.id;\n    let tempDirection: Direction;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormClientComponent, {\n      data: {\n        client: row,\n        action: 'edit',\n      },\n      direction: tempDirection,\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\n      if (result === 1) {\n        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(\n          (x) => x.id === this.id\n        );\n        if (foundIndex != null && this.exampleDatabase) {\n          this.exampleDatabase.dataChange.value[foundIndex] =\n            this.clientService.getDialogData();\n          this.refreshTable();\n          this.showNotification(\n            'black',\n            'Client modifié avec succès...!!!',\n            'bottom',\n            'center'\n          );\n        }\n      }\n    });\n  }\n\n  deleteItem(row: Client) {\n    this.id = row.id;\n    let tempDirection: Direction;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(ClientDeleteComponent, {\n      width: '500px',\n      maxWidth: '90vw',\n      data: row,\n      direction: tempDirection,\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\n      if (result === 1) {\n        const foundIndex = this.exampleDatabase?.dataChange.value.findIndex(\n          (x) => x.id === this.id\n        );\n        if (foundIndex != null && this.exampleDatabase) {\n          this.exampleDatabase.dataChange.value.splice(foundIndex, 1);\n          this.refreshTable();\n          this.showNotification(\n            'snackbar-danger',\n            'Client supprimé avec succès...!!!',\n            'bottom',\n            'center'\n          );\n        }\n      }\n    });\n  }\n\n  private refreshTable() {\n    this.paginator._changePageSize(this.paginator.pageSize);\n  }\n\n  isAllSelected() {\n    const numSelected = this.selection.selected.length;\n    const numRows = this.dataSource.renderedData.length;\n    return numSelected === numRows;\n  }\n\n  masterToggle() {\n    this.isAllSelected()\n      ? this.selection.clear()\n      : this.dataSource.renderedData.forEach((row) =>\n          this.selection.select(row)\n        );\n  }\n\n  removeSelectedRows() {\n    const totalSelect = this.selection.selected.length;\n    \n    if (totalSelect === 0) {\n      this.showNotification(\n        'snackbar-warning',\n        'Aucun client sélectionné pour la suppression',\n        'bottom',\n        'center'\n      );\n      return;\n    }\n\n    let tempDirection: Direction;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n\n    const dialogRef = this.dialog.open(BulkDeleteClientConfirmationComponent, {\n      width: '600px',\n      maxWidth: '90vw',\n      data: {\n        selectedClients: this.selection.selected,\n        totalCount: totalSelect\n      },\n      direction: tempDirection,\n    });\n\n    this.subs.sink = dialogRef.afterClosed().subscribe((confirmed) => {\n      if (confirmed) {\n        const selectedIds = this.selection.selected.map(item => item.id);\n        \n        this.clientService.deleteSelectedClients(selectedIds).subscribe({\n          next: () => {\n            this.selection.selected.forEach((item) => {\n              const index: number = this.dataSource.renderedData.findIndex(\n                (d) => d === item\n              );\n              if (index !== -1) {\n                this.exampleDatabase?.dataChange.value.splice(index, 1);\n              }\n            });\n            \n            this.selection = new SelectionModel<Client>(true, []);\n            this.refreshTable();\n            this.clientService.getAllClients().subscribe();\n            \n            this.showNotification(\n              'snackbar-danger',\n              `${totalSelect} client(s) supprimé(s) avec succès !`,\n              'bottom',\n              'center'\n            );\n          },\n          error: (error) => {\n            console.error('Erreur lors de la suppression des clients:', error);\n            this.showNotification(\n              'snackbar-danger',\n              'Erreur lors de la suppression des clients. Veuillez réessayer.',\n              'bottom',\n              'center'\n            );\n          }\n        });\n      }\n    });\n  }\n\n  public loadData() {\n    this.exampleDatabase = new ClientService(this.httpClient);\n    this.dataSource = new ExampleDataSource(\n      this.exampleDatabase,\n      this.paginator,\n      this.sort\n    );\n    this.subs.sink = fromEvent(this.filter?.nativeElement, 'keyup').subscribe(\n      () => {\n        if (!this.dataSource) {\n          return;\n        }\n        this.dataSource.filter = this.filter?.nativeElement.value;\n      }\n    );\n  }\n\n  exportExcel() {\n    const exportData: Partial<TableElement>[] =\n      this.dataSource.filteredData.map((x) => ({\n        'Code Client': x.code,\n        'Raison Sociale': x.syntax,\n        'Matricule Fiscal': x.matFiscal,\n        'Email': x.email,\n        'Téléphone': x.telephone,\n      }));\n\n    TableExportUtil.exportToExcel(exportData, 'clients');\n  }\n\n  showNotification(\n    colorName: string,\n    text: string,\n    placementFrom: MatSnackBarVerticalPosition,\n    placementAlign: MatSnackBarHorizontalPosition\n  ) {\n    this.snackBar.open(text, '', {\n      duration: 2000,\n      verticalPosition: placementFrom,\n      horizontalPosition: placementAlign,\n      panelClass: colorName,\n    });\n  }\n}\n\nexport class ExampleDataSource extends DataSource<Client> {\n  filterChange = new BehaviorSubject('');\n  get filter(): string {\n    return this.filterChange.value;\n  }\n  set filter(filter: string) {\n    this.filterChange.next(filter);\n  }\n  filteredData: Client[] = [];\n  renderedData: Client[] = [];\n  constructor(\n    public exampleDatabase: ClientService,\n    public paginator: MatPaginator,\n    public _sort: MatSort\n  ) {\n    super();\n    this.filterChange.subscribe(() => (this.filter = this.filter));\n  }\n\n  connect(): Observable<Client[]> {\n    const displayDataChanges = [\n      this.exampleDatabase.dataChange,\n      this._sort.sortChange,\n      this.filterChange,\n      this.paginator.page,\n    ];\n    this.exampleDatabase.getAllClients();\n    return merge(...displayDataChanges).pipe(\n      map(() => {\n        this.filteredData = this.exampleDatabase.data\n          .slice()\n          .filter((client: Client) => {\n            const searchStr = (\n              client.code +\n              client.syntax +\n              client.matFiscal +\n              client.email +\n              client.telephone\n            ).toLowerCase();\n            return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\n          });\n        const sortedData = this.sortData(this.filteredData.slice());\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n        this.renderedData = sortedData.splice(\n          startIndex,\n          this.paginator.pageSize\n        );\n        return this.renderedData;\n      })\n    );\n  }\n  disconnect() {\n    // disconnect\n  }\n  sortData(data: Client[]): Client[] {\n    if (!this._sort.active || this._sort.direction === '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let propertyA: number | string = '';\n      let propertyB: number | string = '';\n      switch (this._sort.active) {\n        case 'id':\n          [propertyA, propertyB] = [a.id, b.id];\n          break;\n        case 'code':\n          [propertyA, propertyB] = [a.code, b.code];\n          break;\n        case 'syntax':\n          [propertyA, propertyB] = [a.syntax || '', b.syntax || ''];\n          break;\n        case 'matFiscal':\n          [propertyA, propertyB] = [a.matFiscal || '', b.matFiscal || ''];\n          break;\n        case 'email':\n          [propertyA, propertyB] = [a.email || '', b.email || ''];\n          break;\n        case 'telephone':\n          [propertyA, propertyB] = [a.telephone || '', b.telephone || ''];\n          break;\n      }\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\n      return (\n        (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1)\n      );\n    });\n  }\n}\n", "<section class=\"content\">\n  <div class=\"content-block\">\n    <div class=\"block-header\">\n      <!-- breadcrumb -->\n      <app-breadcrumb [title]=\"breadscrums[0].title\" [items]=\"breadscrums[0].items\" [active_item]=\"breadscrums[0].active\"></app-breadcrumb>\n    </div>\n    <div class=\"row\">\n      <div class=\"col-xs-12 col-sm-12 col-md-12 col-lg-12\">\n        <div class=\"card\">\n          <div class=\"header\">\n            <h2>\n              <strong>Liste des Clients</strong>\n            </h2>\n            <div class=\"header-dropdown\">\n              <button mat-icon-button class=\"example-icon favorite-icon\" aria-label=\"refresh\" (click)=\"refresh()\">\n                <mat-icon>refresh</mat-icon>\n              </button>\n            </div>\n          </div>\n          <div class=\"body\">\n            <div class=\"responsive_table\">\n              <div class=\"table-responsive\">\n                <div class=\"table-header\">\n                  <div class=\"add-new\">\n                    <button mat-flat-button color=\"primary\" class=\"btn-space\" (click)=\"addNew()\">\n                      <mat-icon>add</mat-icon>\n                      Ajouter Client\n                    </button>\n                    @if (selection.selected.length > 0) {\n                    <button mat-flat-button color=\"warn\" class=\"btn-space\" (click)=\"removeSelectedRows()\">\n                      <mat-icon>delete</mat-icon>\n                      Supprimer Sélection\n                    </button>\n                    }\n                  </div>\n                  <div class=\"table-search\">\n                    <mat-form-field class=\"search-form-field\" floatLabel=\"auto\">\n                      <input matInput #filter placeholder=\"Rechercher...\" autocomplete=\"off\">\n                      <button mat-icon-button matSuffix mat-flat-button aria-label=\"search\" class=\"search-icon\">\n                        <mat-icon>search</mat-icon>\n                      </button>\n                    </mat-form-field>\n                  </div>\n                  <div class=\"table-actions\">\n                    <button mat-icon-button color=\"primary\" class=\"btn-space\" matTooltip=\"Exporter Excel\" (click)=\"exportExcel()\">\n                      <mat-icon>get_app</mat-icon>\n                    </button>\n                  </div>\n                </div>\n\n                <table mat-table [dataSource]=\"dataSource\" class=\"mat-elevation-z8 data-table\" matSort #table>\n                  <!-- Checkbox Column -->\n                  <ng-container matColumnDef=\"select\">\n                    <th mat-header-cell *matHeaderCellDef>\n                      <mat-checkbox (change)=\"$event ? masterToggle() : null\" [checked]=\"selection.hasValue() && isAllSelected()\" [indeterminate]=\"selection.hasValue() && !isAllSelected()\">\n                      </mat-checkbox>\n                    </th>\n                    <td mat-cell *matCellDef=\"let row\">\n                      <mat-checkbox (click)=\"$event.stopPropagation()\" (change)=\"$event ? selection.toggle(row) : null\" [checked]=\"selection.isSelected(row)\">\n                      </mat-checkbox>\n                    </td>\n                  </ng-container>\n\n                  <!-- Code Column -->\n                  <ng-container matColumnDef=\"code\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Code Client</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"client-code\">\n                      <strong>{{row.code}}</strong>\n                    </td>\n                  </ng-container>\n\n                  <!-- Syntax Column -->\n                  <ng-container matColumnDef=\"syntax\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Raison Sociale</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"client-syntax\">\n                      {{row.syntax || 'Non défini'}}\n                    </td>\n                  </ng-container>\n\n                  <!-- MatFiscal Column -->\n                  <ng-container matColumnDef=\"matFiscal\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Matricule Fiscal</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"client-matfiscal\">\n                      {{row.matFiscal || 'Non défini'}}\n                    </td>\n                  </ng-container>\n\n                  <!-- Email Column -->\n                  <ng-container matColumnDef=\"email\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"client-email\">\n                      <a *ngIf=\"row.email\" [href]=\"'mailto:' + row.email\" class=\"email-link\">\n                        {{row.email}}\n                      </a>\n                      <span *ngIf=\"!row.email\" class=\"no-data\">Non défini</span>\n                    </td>\n                  </ng-container>\n\n                  <!-- Telephone Column -->\n                  <ng-container matColumnDef=\"telephone\">\n                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Téléphone</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"client-telephone\">\n                      <a *ngIf=\"row.telephone\" [href]=\"'tel:' + row.telephone\" class=\"phone-link\">\n                        {{row.telephone}}\n                      </a>\n                      <span *ngIf=\"!row.telephone\" class=\"no-data\">Non défini</span>\n                    </td>\n                  </ng-container>\n\n                  <!-- Actions Column -->\n                  <ng-container matColumnDef=\"actions\">\n                    <th mat-header-cell *matHeaderCellDef class=\"pr-0\">Actions</th>\n                    <td mat-cell *matCellDef=\"let row\" class=\"action-link\">\n                      <button mat-icon-button color=\"accent\" class=\"btn-tbl-edit\" matTooltip=\"Modifier\" (click)=\"editCall(row)\">\n                        <mat-icon>edit</mat-icon>\n                      </button>\n                      <button mat-icon-button color=\"warn\" class=\"btn-tbl-delete\" matTooltip=\"Supprimer\" (click)=\"deleteItem(row)\">\n                        <mat-icon>delete</mat-icon>\n                      </button>\n                    </td>\n                  </ng-container>\n\n                  <tr mat-header-row *matHeaderRowDef=\"displayedColumns\"></tr>\n                  <tr mat-row *matRowDef=\"let row; columns: displayedColumns;\" [ngClass]=\"{'selected': selection.isSelected(row)}\" (click)=\"selection.toggle(row)\"></tr>\n                </table>\n\n                <mat-paginator #paginator [length]=\"dataSource.filteredData.length\" [pageIndex]=\"0\" [pageSize]=\"10\" [pageSizeOptions]=\"[5, 10, 25, 100]\" showFirstLastButtons>\n                </mat-paginator>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</section>\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,+BAA+B;AAG7D,SAASC,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAE/D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,qBAAqB,QAAQ,0CAA0C;AAChF,SAASC,qCAAqC,QAAQ,qCAAqC;AAC3F,SAASC,eAAe,EAAEC,SAAS,EAAEC,KAAK,QAAoB,MAAM;AACpE,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,2BAA2B,QAAQ,qCAAqC;AAEjF,SAASC,eAAe,QAAsB,SAAS;AACvD,SAAqBC,OAAO,EAAYC,YAAY,QAAQ,iBAAiB;AAC7E,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,oDAAoD;AAExF,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;;;ICCpCC,EAAA,CAAAC,cAAA,iBAAsF;IAA/BD,EAAA,CAAAE,UAAA,mBAAAC,gEAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAF,OAAA,CAAAG,kBAAA,EAAoB;IAAA,EAAC;IACnFT,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAC3BX,EAAA,CAAAU,MAAA,iCACF;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;;;IAqBTX,EAAA,CAAAC,cAAA,aAAsC;IACtBD,EAAA,CAAAE,UAAA,oBAAAU,8DAAAC,MAAA;MAAAb,EAAA,CAAAI,aAAA,CAAAU,IAAA;MAAA,MAAAC,OAAA,GAAAf,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAK,MAAA,GAASE,OAAA,CAAAC,YAAA,EAAc,GAAG,IAAI;IAAA,EAAC;IACvDhB,EAAA,CAAAW,YAAA,EAAe;;;;IADyCX,EAAA,CAAAiB,SAAA,EAAmD;IAAnDjB,EAAA,CAAAkB,UAAA,YAAAC,MAAA,CAAAC,SAAA,CAAAC,QAAA,MAAAF,MAAA,CAAAG,aAAA,GAAmD,kBAAAH,MAAA,CAAAC,SAAA,CAAAC,QAAA,OAAAF,MAAA,CAAAG,aAAA;;;;;;IAG7GtB,EAAA,CAAAC,cAAA,aAAmC;IACnBD,EAAA,CAAAE,UAAA,mBAAAqB,6DAAAV,MAAA;MAAA,OAASA,MAAA,CAAAW,eAAA,EAAwB;IAAA,EAAC,oBAAAC,8DAAAZ,MAAA;MAAA,MAAAa,WAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAuB,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAA9B,EAAA,CAAAO,aAAA;MAAA,OAAWP,EAAA,CAAAQ,WAAA,CAAAK,MAAA,GAASiB,OAAA,CAAAV,SAAA,CAAAW,MAAA,CAAAH,OAAA,CAAqB,GAAG,IAAI;IAAA,EAAhD;IAChD5B,EAAA,CAAAW,YAAA,EAAe;;;;;IADmFX,EAAA,CAAAiB,SAAA,EAAqC;IAArCjB,EAAA,CAAAkB,UAAA,YAAAc,MAAA,CAAAZ,SAAA,CAAAa,UAAA,CAAAL,OAAA,EAAqC;;;;;IAOzI5B,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAU,MAAA,kBAAW;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IACtEX,EAAA,CAAAC,cAAA,aAAuD;IAC7CD,EAAA,CAAAU,MAAA,GAAY;IAAAV,EAAA,CAAAW,YAAA,EAAS;;;;IAArBX,EAAA,CAAAiB,SAAA,GAAY;IAAZjB,EAAA,CAAAkC,iBAAA,CAAAC,OAAA,CAAAC,IAAA,CAAY;;;;;IAMtBpC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAU,MAAA,qBAAc;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IACzEX,EAAA,CAAAC,cAAA,aAAyD;IACvDD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAqC,kBAAA,MAAAC,OAAA,CAAAC,MAAA,2BACF;;;;;IAKAvC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAU,MAAA,uBAAgB;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAC3EX,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;IADHX,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAqC,kBAAA,MAAAG,OAAA,CAAAC,SAAA,2BACF;;;;;IAKAzC,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAU,MAAA,YAAK;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAE9DX,EAAA,CAAAC,cAAA,YAAuE;IACrED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;IAFiBX,EAAA,CAAAkB,UAAA,qBAAAwB,OAAA,CAAAC,KAAA,EAAA3C,EAAA,CAAA4C,aAAA,CAA8B;IACjD5C,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAqC,kBAAA,MAAAK,OAAA,CAAAC,KAAA,MACF;;;;;IACA3C,EAAA,CAAAC,cAAA,eAAyC;IAAAD,EAAA,CAAAU,MAAA,sBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAJ5DX,EAAA,CAAAC,cAAA,aAAwD;IACtDD,EAAA,CAAA6C,UAAA,IAAAC,kCAAA,gBAEI,IAAAC,qCAAA;IAEN/C,EAAA,CAAAW,YAAA,EAAK;;;;IAJCX,EAAA,CAAAiB,SAAA,EAAe;IAAfjB,EAAA,CAAAkB,UAAA,SAAAwB,OAAA,CAAAC,KAAA,CAAe;IAGZ3C,EAAA,CAAAiB,SAAA,EAAgB;IAAhBjB,EAAA,CAAAkB,UAAA,UAAAwB,OAAA,CAAAC,KAAA,CAAgB;;;;;IAMzB3C,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAAU,MAAA,0BAAS;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;IAElEX,EAAA,CAAAC,cAAA,YAA4E;IAC1ED,EAAA,CAAAU,MAAA,GACF;IAAAV,EAAA,CAAAW,YAAA,EAAI;;;;IAFqBX,EAAA,CAAAkB,UAAA,kBAAA8B,OAAA,CAAAC,SAAA,EAAAjD,EAAA,CAAA4C,aAAA,CAA+B;IACtD5C,EAAA,CAAAiB,SAAA,EACF;IADEjB,EAAA,CAAAqC,kBAAA,MAAAW,OAAA,CAAAC,SAAA,MACF;;;;;IACAjD,EAAA,CAAAC,cAAA,eAA6C;IAAAD,EAAA,CAAAU,MAAA,sBAAU;IAAAV,EAAA,CAAAW,YAAA,EAAO;;;;;IAJhEX,EAAA,CAAAC,cAAA,aAA4D;IAC1DD,EAAA,CAAA6C,UAAA,IAAAK,kCAAA,gBAEI,IAAAC,qCAAA;IAENnD,EAAA,CAAAW,YAAA,EAAK;;;;IAJCX,EAAA,CAAAiB,SAAA,EAAmB;IAAnBjB,EAAA,CAAAkB,UAAA,SAAA8B,OAAA,CAAAC,SAAA,CAAmB;IAGhBjD,EAAA,CAAAiB,SAAA,EAAoB;IAApBjB,EAAA,CAAAkB,UAAA,UAAA8B,OAAA,CAAAC,SAAA,CAAoB;;;;;IAM7BjD,EAAA,CAAAC,cAAA,aAAmD;IAAAD,EAAA,CAAAU,MAAA,cAAO;IAAAV,EAAA,CAAAW,YAAA,EAAK;;;;;;IAC/DX,EAAA,CAAAC,cAAA,aAAuD;IAC6BD,EAAA,CAAAE,UAAA,mBAAAkD,uDAAA;MAAA,MAAA1B,WAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAA0B,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IACvGtD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,WAAI;IAAAV,EAAA,CAAAW,YAAA,EAAW;IAE3BX,EAAA,CAAAC,cAAA,iBAA6G;IAA1BD,EAAA,CAAAE,UAAA,mBAAAuD,uDAAA;MAAA,MAAA/B,WAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAAiD,IAAA;MAAA,MAAAC,OAAA,GAAA5B,WAAA,CAAAG,SAAA;MAAA,MAAA6B,OAAA,GAAA1D,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAkD,OAAA,CAAAC,UAAA,CAAAL,OAAA,CAAe;IAAA,EAAC;IAC1GtD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAU,MAAA,aAAM;IAAAV,EAAA,CAAAW,YAAA,EAAW;;;;;IAKjCX,EAAA,CAAA4D,SAAA,aAA4D;;;;;;;;;IAC5D5D,EAAA,CAAAC,cAAA,aAAiJ;IAAhCD,EAAA,CAAAE,UAAA,mBAAA2D,mDAAA;MAAA,MAAAnC,WAAA,GAAA1B,EAAA,CAAAI,aAAA,CAAA0D,IAAA;MAAA,MAAAC,OAAA,GAAArC,WAAA,CAAAG,SAAA;MAAA,MAAAmC,OAAA,GAAAhE,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwD,OAAA,CAAA5C,SAAA,CAAAW,MAAA,CAAAgC,OAAA,CAAqB;IAAA,EAAC;IAAC/D,EAAA,CAAAW,YAAA,EAAK;;;;;IAAzFX,EAAA,CAAAkB,UAAA,YAAAlB,EAAA,CAAAiE,eAAA,IAAAC,GAAA,EAAAC,OAAA,CAAA/C,SAAA,CAAAa,UAAA,CAAA8B,OAAA,GAAmD;;;;ADvElI,OAAM,MAAOK,eAAgB,SAAQlF,2BAA2B;EAwB9DmF,YACSC,UAAsB,EACtBC,MAAiB,EACjBC,aAA4B,EAC3BC,QAAqB;IAE7B,KAAK,EAAE;IALA,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,QAAQ,GAARA,QAAQ;IA3BlB,KAAAC,gBAAgB,GAAG,CACjB,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,WAAW,EACX,OAAO,EACP,WAAW,EACX,SAAS,CACV;IAGD,KAAAtD,SAAS,GAAG,IAAInC,cAAc,CAAS,IAAI,EAAE,EAAE,CAAC;IAIhD,KAAA0F,WAAW,GAAG,CACZ;MACEC,KAAK,EAAE,qBAAqB;MAC5BC,KAAK,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;MAC3BC,MAAM,EAAE;KACT,CACF;EASD;EAQAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACD,QAAQ,EAAE;EACjB;EAEAE,MAAMA,CAAA;IACJ,IAAIC,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC7G,mBAAmB,EAAE;MACtD8G,IAAI,EAAE;QACJC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,MAAM,EAAE;OACT;MACDC,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,KAAK,CAAC,EAAE;QAChB,IAAI,CAACC,eAAe,EAAEC,UAAU,CAACC,KAAK,CAACC,OAAO,CAC5C,IAAI,CAAC5B,aAAa,CAAC6B,aAAa,EAAE,CACnC;QACD,IAAI,CAACC,YAAY,EAAE;QACnB,IAAI,CAACC,gBAAgB,CACnB,kBAAkB,EAClB,iCAAiC,EACjC,QAAQ,EACR,QAAQ,CACT;;IAEL,CAAC,CAAC;EACJ;EAEA/C,QAAQA,CAACgD,GAAW;IAClB,IAAI,CAACC,EAAE,GAAGD,GAAG,CAACC,EAAE;IAChB,IAAItB,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC7G,mBAAmB,EAAE;MACtD8G,IAAI,EAAE;QACJC,MAAM,EAAEe,GAAG;QACXd,MAAM,EAAE;OACT;MACDC,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,KAAK,CAAC,EAAE;QAChB,MAAMU,UAAU,GAAG,IAAI,CAACT,eAAe,EAAEC,UAAU,CAACC,KAAK,CAACQ,SAAS,CAChEC,CAAC,IAAKA,CAAC,CAACH,EAAE,KAAK,IAAI,CAACA,EAAE,CACxB;QACD,IAAIC,UAAU,IAAI,IAAI,IAAI,IAAI,CAACT,eAAe,EAAE;UAC9C,IAAI,CAACA,eAAe,CAACC,UAAU,CAACC,KAAK,CAACO,UAAU,CAAC,GAC/C,IAAI,CAAClC,aAAa,CAAC6B,aAAa,EAAE;UACpC,IAAI,CAACC,YAAY,EAAE;UACnB,IAAI,CAACC,gBAAgB,CACnB,OAAO,EACP,kCAAkC,EAClC,QAAQ,EACR,QAAQ,CACT;;;IAGP,CAAC,CAAC;EACJ;EAEA5C,UAAUA,CAAC6C,GAAW;IACpB,IAAI,CAACC,EAAE,GAAGD,GAAG,CAACC,EAAE;IAChB,IAAItB,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC5G,qBAAqB,EAAE;MACxDkI,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBtB,IAAI,EAAEgB,GAAG;MACTb,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,KAAK,CAAC,EAAE;QAChB,MAAMU,UAAU,GAAG,IAAI,CAACT,eAAe,EAAEC,UAAU,CAACC,KAAK,CAACQ,SAAS,CAChEC,CAAC,IAAKA,CAAC,CAACH,EAAE,KAAK,IAAI,CAACA,EAAE,CACxB;QACD,IAAIC,UAAU,IAAI,IAAI,IAAI,IAAI,CAACT,eAAe,EAAE;UAC9C,IAAI,CAACA,eAAe,CAACC,UAAU,CAACC,KAAK,CAACY,MAAM,CAACL,UAAU,EAAE,CAAC,CAAC;UAC3D,IAAI,CAACJ,YAAY,EAAE;UACnB,IAAI,CAACC,gBAAgB,CACnB,iBAAiB,EACjB,mCAAmC,EACnC,QAAQ,EACR,QAAQ,CACT;;;IAGP,CAAC,CAAC;EACJ;EAEQD,YAAYA,CAAA;IAClB,IAAI,CAACU,SAAS,CAACC,eAAe,CAAC,IAAI,CAACD,SAAS,CAACE,QAAQ,CAAC;EACzD;EAEA5F,aAAaA,CAAA;IACX,MAAM6F,WAAW,GAAG,IAAI,CAAC/F,SAAS,CAACgG,QAAQ,CAACC,MAAM;IAClD,MAAMC,OAAO,GAAG,IAAI,CAACC,UAAU,CAACC,YAAY,CAACH,MAAM;IACnD,OAAOF,WAAW,KAAKG,OAAO;EAChC;EAEAtG,YAAYA,CAAA;IACV,IAAI,CAACM,aAAa,EAAE,GAChB,IAAI,CAACF,SAAS,CAACqG,KAAK,EAAE,GACtB,IAAI,CAACF,UAAU,CAACC,YAAY,CAACE,OAAO,CAAElB,GAAG,IACvC,IAAI,CAACpF,SAAS,CAACuG,MAAM,CAACnB,GAAG,CAAC,CAC3B;EACP;EAEA/F,kBAAkBA,CAAA;IAChB,MAAMmH,WAAW,GAAG,IAAI,CAACxG,SAAS,CAACgG,QAAQ,CAACC,MAAM;IAElD,IAAIO,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAACrB,gBAAgB,CACnB,kBAAkB,EAClB,8CAA8C,EAC9C,QAAQ,EACR,QAAQ,CACT;MACD;;IAGF,IAAIpB,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAGvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC3G,qCAAqC,EAAE;MACxEiI,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBtB,IAAI,EAAE;QACJqC,eAAe,EAAE,IAAI,CAACzG,SAAS,CAACgG,QAAQ;QACxCU,UAAU,EAAEF;OACb;MACDjC,SAAS,EAAER;KACZ,CAAC;IAEF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEgC,SAAS,IAAI;MAC/D,IAAIA,SAAS,EAAE;QACb,MAAMC,WAAW,GAAG,IAAI,CAAC5G,SAAS,CAACgG,QAAQ,CAACpI,GAAG,CAACiJ,IAAI,IAAIA,IAAI,CAACxB,EAAE,CAAC;QAEhE,IAAI,CAACjC,aAAa,CAAC0D,qBAAqB,CAACF,WAAW,CAAC,CAACjC,SAAS,CAAC;UAC9DoC,IAAI,EAAEA,CAAA,KAAK;YACT,IAAI,CAAC/G,SAAS,CAACgG,QAAQ,CAACM,OAAO,CAAEO,IAAI,IAAI;cACvC,MAAMG,KAAK,GAAW,IAAI,CAACb,UAAU,CAACC,YAAY,CAACb,SAAS,CACzD0B,CAAC,IAAKA,CAAC,KAAKJ,IAAI,CAClB;cACD,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAACnC,eAAe,EAAEC,UAAU,CAACC,KAAK,CAACY,MAAM,CAACqB,KAAK,EAAE,CAAC,CAAC;;YAE3D,CAAC,CAAC;YAEF,IAAI,CAAChH,SAAS,GAAG,IAAInC,cAAc,CAAS,IAAI,EAAE,EAAE,CAAC;YACrD,IAAI,CAACqH,YAAY,EAAE;YACnB,IAAI,CAAC9B,aAAa,CAAC8D,aAAa,EAAE,CAACvC,SAAS,EAAE;YAE9C,IAAI,CAACQ,gBAAgB,CACnB,iBAAiB,EACjB,GAAGqB,WAAW,sCAAsC,EACpD,QAAQ,EACR,QAAQ,CACT;UACH,CAAC;UACDW,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;YAClE,IAAI,CAAChC,gBAAgB,CACnB,iBAAiB,EACjB,gEAAgE,EAChE,QAAQ,EACR,QAAQ,CACT;UACH;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEOvB,QAAQA,CAAA;IACb,IAAI,CAACiB,eAAe,GAAG,IAAI7H,aAAa,CAAC,IAAI,CAACkG,UAAU,CAAC;IACzD,IAAI,CAACiD,UAAU,GAAG,IAAIkB,iBAAiB,CACrC,IAAI,CAACxC,eAAe,EACpB,IAAI,CAACe,SAAS,EACd,IAAI,CAAC0B,IAAI,CACV;IACD,IAAI,CAAC9C,IAAI,CAACC,IAAI,GAAG/G,SAAS,CAAC,IAAI,CAAC6J,MAAM,EAAEC,aAAa,EAAE,OAAO,CAAC,CAAC7C,SAAS,CACvE,MAAK;MACH,IAAI,CAAC,IAAI,CAACwB,UAAU,EAAE;QACpB;;MAEF,IAAI,CAACA,UAAU,CAACoB,MAAM,GAAG,IAAI,CAACA,MAAM,EAAEC,aAAa,CAACzC,KAAK;IAC3D,CAAC,CACF;EACH;EAEA0C,WAAWA,CAAA;IACT,MAAMC,UAAU,GACd,IAAI,CAACvB,UAAU,CAACwB,YAAY,CAAC/J,GAAG,CAAE4H,CAAC,KAAM;MACvC,aAAa,EAAEA,CAAC,CAACxE,IAAI;MACrB,gBAAgB,EAAEwE,CAAC,CAACrE,MAAM;MAC1B,kBAAkB,EAAEqE,CAAC,CAACnE,SAAS;MAC/B,OAAO,EAAEmE,CAAC,CAACjE,KAAK;MAChB,WAAW,EAAEiE,CAAC,CAAC3D;KAChB,CAAC,CAAC;IAEL9D,eAAe,CAAC6J,aAAa,CAACF,UAAU,EAAE,SAAS,CAAC;EACtD;EAEAvC,gBAAgBA,CACd0C,SAAiB,EACjBC,IAAY,EACZC,aAA0C,EAC1CC,cAA6C;IAE7C,IAAI,CAAC3E,QAAQ,CAACc,IAAI,CAAC2D,IAAI,EAAE,EAAE,EAAE;MAC3BG,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAEH,aAAa;MAC/BI,kBAAkB,EAAEH,cAAc;MAClCI,UAAU,EAAEP;KACb,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBAnRUrF,eAAe,EAAApE,EAAA,CAAA0J,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA5J,EAAA,CAAA0J,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAA9J,EAAA,CAAA0J,iBAAA,CAAAK,EAAA,CAAA3L,aAAA,GAAA4B,EAAA,CAAA0J,iBAAA,CAAAM,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAf9F,eAAe;IAAA+F,SAAA;IAAAC,SAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAiCfjM,YAAY;uBAEZE,OAAO;;;;;;;;;;;;;;;;;QCvFpByB,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAA4D,SAAA,wBAAqI;QACvI5D,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,aAAiB;QAKCD,EAAA,CAAAU,MAAA,yBAAiB;QAAAV,EAAA,CAAAW,YAAA,EAAS;QAEpCX,EAAA,CAAAC,cAAA,cAA6B;QACqDD,EAAA,CAAAE,UAAA,mBAAAsK,kDAAA;UAAA,OAASD,GAAA,CAAAtF,OAAA,EAAS;QAAA,EAAC;QACjGjF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,eAAO;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAIlCX,EAAA,CAAAC,cAAA,eAAkB;QAKkDD,EAAA,CAAAE,UAAA,mBAAAuK,kDAAA;UAAA,OAASF,GAAA,CAAArF,MAAA,EAAQ;QAAA,EAAC;QAC1ElF,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,WAAG;QAAAV,EAAA,CAAAW,YAAA,EAAW;QACxBX,EAAA,CAAAU,MAAA,wBACF;QAAAV,EAAA,CAAAW,YAAA,EAAS;QACTX,EAAA,CAAA6C,UAAA,KAAA6H,uCAAA,qBAKC;QACH1K,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,eAA0B;QAEtBD,EAAA,CAAA4D,SAAA,qBAAuE;QACvE5D,EAAA,CAAAC,cAAA,kBAA0F;QAC9ED,EAAA,CAAAU,MAAA,cAAM;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAIjCX,EAAA,CAAAC,cAAA,eAA2B;QAC6DD,EAAA,CAAAE,UAAA,mBAAAyK,kDAAA;UAAA,OAASJ,GAAA,CAAA1B,WAAA,EAAa;QAAA,EAAC;QAC3G7I,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAU,MAAA,eAAO;QAAAV,EAAA,CAAAW,YAAA,EAAW;QAKlCX,EAAA,CAAAC,cAAA,qBAA8F;QAE5FD,EAAA,CAAA4K,uBAAA,QAAoC;QAClC5K,EAAA,CAAA6C,UAAA,KAAAgI,8BAAA,iBAGK,KAAAC,8BAAA;QAKP9K,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAkC;QAChC5K,EAAA,CAAA6C,UAAA,KAAAmI,8BAAA,iBAAsE,KAAAC,8BAAA;QAIxEjL,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAoC;QAClC5K,EAAA,CAAA6C,UAAA,KAAAqI,8BAAA,iBAAyE,KAAAC,8BAAA;QAI3EnL,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAuC;QACrC5K,EAAA,CAAA6C,UAAA,KAAAuI,8BAAA,iBAA2E,KAAAC,8BAAA;QAI7ErL,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAmC;QACjC5K,EAAA,CAAA6C,UAAA,KAAAyI,8BAAA,iBAAgE,KAAAC,8BAAA;QAOlEvL,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAuC;QACrC5K,EAAA,CAAA6C,UAAA,KAAA2I,8BAAA,iBAAoE,KAAAC,8BAAA;QAOtEzL,EAAA,CAAA+K,qBAAA,EAAe;QAGf/K,EAAA,CAAA4K,uBAAA,QAAqC;QACnC5K,EAAA,CAAA6C,UAAA,KAAA6I,8BAAA,iBAA+D,KAAAC,8BAAA;QASjE3L,EAAA,CAAA+K,qBAAA,EAAe;QAEf/K,EAAA,CAAA6C,UAAA,KAAA+I,8BAAA,iBAA4D,KAAAC,8BAAA;QAE9D7L,EAAA,CAAAW,YAAA,EAAQ;QAERX,EAAA,CAAA4D,SAAA,6BACgB;QAClB5D,EAAA,CAAAW,YAAA,EAAM;;;QA5HEX,EAAA,CAAAiB,SAAA,GAA8B;QAA9BjB,EAAA,CAAAkB,UAAA,UAAAqJ,GAAA,CAAA5F,WAAA,IAAAC,KAAA,CAA8B,UAAA2F,GAAA,CAAA5F,WAAA,IAAAE,KAAA,iBAAA0F,GAAA,CAAA5F,WAAA,IAAAG,MAAA;QAwBhC9E,EAAA,CAAAiB,SAAA,IAKC;QALDjB,EAAA,CAAA8L,aAAA,KAAAvB,GAAA,CAAAnJ,SAAA,CAAAgG,QAAA,CAAAC,MAAA,eAKC;QAiBYrH,EAAA,CAAAiB,SAAA,IAAyB;QAAzBjB,EAAA,CAAAkB,UAAA,eAAAqJ,GAAA,CAAAhD,UAAA,CAAyB;QAwEpBvH,EAAA,CAAAiB,SAAA,IAAiC;QAAjCjB,EAAA,CAAAkB,UAAA,oBAAAqJ,GAAA,CAAA7F,gBAAA,CAAiC;QACpB1E,EAAA,CAAAiB,SAAA,EAA0B;QAA1BjB,EAAA,CAAAkB,UAAA,qBAAAqJ,GAAA,CAAA7F,gBAAA,CAA0B;QAGnC1E,EAAA,CAAAiB,SAAA,EAAyC;QAAzCjB,EAAA,CAAAkB,UAAA,WAAAqJ,GAAA,CAAAhD,UAAA,CAAAwB,YAAA,CAAA1B,MAAA,CAAyC,oDAAArH,EAAA,CAAA+L,eAAA,KAAAC,GAAA;;;mBD1F/EnM,mBAAmB,EACnBD,gBAAgB,EAAAqM,EAAA,CAAAC,UAAA,EAChBvM,eAAe,EAAAwM,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf3M,aAAa,EAAA4M,EAAA,CAAAC,OAAA,EACb9M,cAAc,EAAA+M,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACd1O,aAAa,EAAA2O,EAAA,CAAA5O,OAAA,EAAA4O,EAAA,CAAAC,aAAA,EACbhO,OAAO,EACPI,iBAAiB,EAAA6N,GAAA,CAAAC,WAAA,EACjB/N,eAAe,EACfD,wBAAwB,EACxBhB,kBAAkB,EAAAiP,GAAA,CAAAlP,YAAA,EAClBgB,YAAY,EAAAmO,GAAA,CAAAC,IAAA,EACZ3N,kBAAkB,EAAA4N,GAAA,CAAAC,YAAA,EAAAD,GAAA,CAAAE,SAAA,EAClB7N,cAAc,EAAA8N,GAAA,CAAAC,QAAA;IAAAC,MAAA;EAAA;;AAyRlB,OAAM,MAAOtF,iBAAkB,SAAQhK,UAAkB;EAEvD,IAAIkK,MAAMA,CAAA;IACR,OAAO,IAAI,CAACqF,YAAY,CAAC7H,KAAK;EAChC;EACA,IAAIwC,MAAMA,CAACA,MAAc;IACvB,IAAI,CAACqF,YAAY,CAAC7F,IAAI,CAACQ,MAAM,CAAC;EAChC;EAGAtE,YACS4B,eAA8B,EAC9Be,SAAuB,EACvBiH,KAAc;IAErB,KAAK,EAAE;IAJA,KAAAhI,eAAe,GAAfA,eAAe;IACf,KAAAe,SAAS,GAATA,SAAS;IACT,KAAAiH,KAAK,GAALA,KAAK;IAZd,KAAAD,YAAY,GAAG,IAAInP,eAAe,CAAC,EAAE,CAAC;IAOtC,KAAAkK,YAAY,GAAa,EAAE;IAC3B,KAAAvB,YAAY,GAAa,EAAE;IAOzB,IAAI,CAACwG,YAAY,CAACjI,SAAS,CAAC,MAAO,IAAI,CAAC4C,MAAM,GAAG,IAAI,CAACA,MAAO,CAAC;EAChE;EAEAuF,OAAOA,CAAA;IACL,MAAMC,kBAAkB,GAAG,CACzB,IAAI,CAAClI,eAAe,CAACC,UAAU,EAC/B,IAAI,CAAC+H,KAAK,CAACG,UAAU,EACrB,IAAI,CAACJ,YAAY,EACjB,IAAI,CAAChH,SAAS,CAACqH,IAAI,CACpB;IACD,IAAI,CAACpI,eAAe,CAACqC,aAAa,EAAE;IACpC,OAAOvJ,KAAK,CAAC,GAAGoP,kBAAkB,CAAC,CAACG,IAAI,CACtCtP,GAAG,CAAC,MAAK;MACP,IAAI,CAAC+J,YAAY,GAAG,IAAI,CAAC9C,eAAe,CAACT,IAAI,CAC1C+I,KAAK,EAAE,CACP5F,MAAM,CAAElD,MAAc,IAAI;QACzB,MAAM+I,SAAS,GAAG,CAChB/I,MAAM,CAACrD,IAAI,GACXqD,MAAM,CAAClD,MAAM,GACbkD,MAAM,CAAChD,SAAS,GAChBgD,MAAM,CAAC9C,KAAK,GACZ8C,MAAM,CAACxC,SAAS,EAChBwL,WAAW,EAAE;QACf,OAAOD,SAAS,CAACE,OAAO,CAAC,IAAI,CAAC/F,MAAM,CAAC8F,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;MACJ,MAAME,UAAU,GAAG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC7F,YAAY,CAACwF,KAAK,EAAE,CAAC;MAC3D,MAAMM,UAAU,GAAG,IAAI,CAAC7H,SAAS,CAAC8H,SAAS,GAAG,IAAI,CAAC9H,SAAS,CAACE,QAAQ;MACrE,IAAI,CAACM,YAAY,GAAGmH,UAAU,CAAC5H,MAAM,CACnC8H,UAAU,EACV,IAAI,CAAC7H,SAAS,CAACE,QAAQ,CACxB;MACD,OAAO,IAAI,CAACM,YAAY;IAC1B,CAAC,CAAC,CACH;EACH;EACAuH,UAAUA,CAAA;IACR;EAAA;EAEFH,QAAQA,CAACpJ,IAAc;IACrB,IAAI,CAAC,IAAI,CAACyI,KAAK,CAACnJ,MAAM,IAAI,IAAI,CAACmJ,KAAK,CAACtI,SAAS,KAAK,EAAE,EAAE;MACrD,OAAOH,IAAI;;IAEb,OAAOA,IAAI,CAACkD,IAAI,CAAC,CAACsG,CAAC,EAAEC,CAAC,KAAI;MACxB,IAAIC,SAAS,GAAoB,EAAE;MACnC,IAAIC,SAAS,GAAoB,EAAE;MACnC,QAAQ,IAAI,CAAClB,KAAK,CAACnJ,MAAM;QACvB,KAAK,IAAI;UACP,CAACoK,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACvI,EAAE,EAAEwI,CAAC,CAACxI,EAAE,CAAC;UACrC;QACF,KAAK,MAAM;UACT,CAACyI,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAAC5M,IAAI,EAAE6M,CAAC,CAAC7M,IAAI,CAAC;UACzC;QACF,KAAK,QAAQ;UACX,CAAC8M,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACzM,MAAM,IAAI,EAAE,EAAE0M,CAAC,CAAC1M,MAAM,IAAI,EAAE,CAAC;UACzD;QACF,KAAK,WAAW;UACd,CAAC2M,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACvM,SAAS,IAAI,EAAE,EAAEwM,CAAC,CAACxM,SAAS,IAAI,EAAE,CAAC;UAC/D;QACF,KAAK,OAAO;UACV,CAACyM,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACrM,KAAK,IAAI,EAAE,EAAEsM,CAAC,CAACtM,KAAK,IAAI,EAAE,CAAC;UACvD;QACF,KAAK,WAAW;UACd,CAACuM,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAAC/L,SAAS,IAAI,EAAE,EAAEgM,CAAC,CAAChM,SAAS,IAAI,EAAE,CAAC;UAC/D;;MAEJ,MAAMmM,MAAM,GAAGC,KAAK,CAAC,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS;MACzD,MAAMI,MAAM,GAAGD,KAAK,CAAC,CAACF,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS;MACzD,OACE,CAACC,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAACrB,KAAK,CAACtI,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE1E,CAAC,CAAC;EACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}