{"ast": null, "code": "import { ClientComponent } from './client/client.component';\nimport { FormClientComponent } from \"./form-client/form-client.component\";\nimport { ClientDeleteComponent } from \"./client-delete/client-delete.component\";\nexport const CLIENT_ROUTES = [{\n  path: '',\n  children: [{\n    path: 'list',\n    component: ClientComponent\n  }, {\n    path: 'add',\n    component: FormClientComponent\n  }, {\n    path: 'edit/:id',\n    component: FormClientComponent\n  }, {\n    path: 'delete/:id',\n    component: ClientDeleteComponent\n  }, {\n    path: '**',\n    redirectTo: 'list'\n  }]\n}];", "map": {"version": 3, "names": ["ClientComponent", "FormClientComponent", "ClientDeleteComponent", "CLIENT_ROUTES", "path", "children", "component", "redirectTo"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client.routes.ts"], "sourcesContent": ["import { Routes } from \"@angular/router\";\nimport { ClientComponent } from './client/client.component';\nimport { FormClientComponent } from \"./form-client/form-client.component\";\nimport { ClientDeleteComponent } from \"./client-delete/client-delete.component\";\n\nexport const CLIENT_ROUTES: Routes = [\n  {\n    path: '',\n    children: [\n      { path: 'list', component: ClientComponent },\n      { path: 'add', component: FormClientComponent },\n      { path: 'edit/:id', component: FormClientComponent },\n      { path: 'delete/:id', component: ClientDeleteComponent },\n      { path: '**', redirectTo: 'list' }\n    ]\n  }\n];\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,mBAAmB,QAAQ,qCAAqC;AACzE,SAASC,qBAAqB,QAAQ,yCAAyC;AAE/E,OAAO,MAAMC,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,QAAQ,EAAE,CACR;IAAED,IAAI,EAAE,MAAM;IAAEE,SAAS,EAAEN;EAAe,CAAE,EAC5C;IAAEI,IAAI,EAAE,KAAK;IAAEE,SAAS,EAAEL;EAAmB,CAAE,EAC/C;IAAEG,IAAI,EAAE,UAAU;IAAEE,SAAS,EAAEL;EAAmB,CAAE,EACpD;IAAEG,IAAI,EAAE,YAAY;IAAEE,SAAS,EAAEJ;EAAqB,CAAE,EACxD;IAAEE,IAAI,EAAE,IAAI;IAAEG,UAAU,EAAE;EAAM,CAAE;CAErC,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}