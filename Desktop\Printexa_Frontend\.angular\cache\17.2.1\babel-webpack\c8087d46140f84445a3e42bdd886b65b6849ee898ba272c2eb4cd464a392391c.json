{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { tap, catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    console.log('=== SERVICE CREATE CLIENT ===');\n    console.log('URL:', this.baseUrl);\n    console.log('Données reçues:', client);\n    console.log('Headers:', this.getHeaders());\n    const clientData = {\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    // Nettoyer les valeurs undefined\n    Object.keys(clientData).forEach(key => {\n      if (clientData[key] === undefined) {\n        delete clientData[key];\n      }\n    });\n    console.log('Données nettoyées à envoyer:', clientData);\n    console.log('==============================');\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    const cleanedClient = {};\n    Object.keys(client).forEach(key => {\n      if (key !== 'id' && client[key]) {\n        cleanedClient[key] = client[key];\n      }\n    });\n    return this.http.put(`${this.baseUrl}${id}`, cleanedClient, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === id);\n      if (index !== -1) {\n        currentData[index] = {\n          ...currentData[index],\n          ...cleanedClient\n        };\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(this.handleError));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(this.handleError));\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Gestionnaire d'erreur spécialisé pour la création\n  handleCreateError(error) {\n    console.error('=== ERREUR SERVICE ===');\n    console.error('Status:', error.status);\n    console.error('Status Text:', error.statusText);\n    console.error('Error object:', error.error);\n    console.error('URL:', error.url);\n    // Log détaillé des erreurs de validation\n    if (error.error?.errors) {\n      console.error('Erreurs de validation détaillées:');\n      Object.entries(error.error.errors).forEach(([field, messages]) => {\n        console.error(`  ${field}:`, messages);\n      });\n    }\n    console.error('======================');\n    let errorMessage = 'Erreur lors de la création du client';\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez que l\\'API est démarrée.';\n    } else if (error.status === 400) {\n      if (error.error?.errors) {\n        // Extraire et formater les erreurs de validation spécifiques\n        const validationDetails = [];\n        Object.entries(error.error.errors).forEach(([field, messages]) => {\n          const messageArray = Array.isArray(messages) ? messages : [messages];\n          validationDetails.push(`${field}: ${messageArray.join(', ')}`);\n        });\n        errorMessage = `Erreurs de validation:\\n${validationDetails.join('\\n')}`;\n      } else if (error.error?.title) {\n        errorMessage = error.error.title;\n      } else {\n        errorMessage = 'Données invalides';\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ce code existe déjà';\n    } else if (error.status === 500) {\n      errorMessage = 'Erreur interne du serveur';\n    } else {\n      const apiError = error.error?.message || error.error?.title || error.error?.error;\n      if (apiError) {\n        errorMessage = `Erreur de création: ${apiError}`;\n      }\n    }\n    // Créer une erreur avec plus de détails\n    const detailedError = new Error(errorMessage);\n    detailedError.originalError = error;\n    detailedError.validationErrors = error.error?.errors;\n    return throwError(() => detailedError);\n  }\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Non autorisé';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - le client existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "console", "log", "code", "trim", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "Object", "keys", "for<PERSON>ach", "key", "undefined", "post", "observe", "response", "newClient", "body", "Error", "currentData", "value", "handleCreateError", "updateClient", "cleanedClient", "put", "index", "findIndex", "c", "deleteClient", "delete", "filter", "deleteSelectedClients", "ids", "length", "includes", "clearCurrentClient", "removeItem", "data", "getDialogData", "dialogData", "status", "statusText", "url", "errors", "entries", "field", "messages", "errorMessage", "validationDetails", "messageArray", "Array", "isArray", "push", "join", "title", "navigate", "apiError", "message", "detailedError", "originalError", "validationErrors", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { tap, catchError, map } from 'rxjs/operators';\r\nimport { Client, CreateClientDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientDto): Observable<Client> {\r\n    console.log('=== SERVICE CREATE CLIENT ===');\r\n    console.log('URL:', this.baseUrl);\r\n    console.log('Données reçues:', client);\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const clientData: CreateClientDto = {\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    // Nettoyer les valeurs undefined\r\n    Object.keys(clientData).forEach(key => {\r\n      if (clientData[key as keyof CreateClientDto] === undefined) {\r\n        delete clientData[key as keyof CreateClientDto];\r\n      }\r\n    });\r\n\r\n    console.log('Données nettoyées à envoyer:', clientData);\r\n    console.log('==============================');\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\r\n    const cleanedClient: UpdateClientDto = {};\r\n    Object.keys(client).forEach(key => {\r\n      if (key !== 'id' && client[key as keyof UpdateClientDto]) {\r\n        cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];\r\n      }\r\n    });\r\n\r\n    return this.http.put<void>(`${this.baseUrl}${id}`, cleanedClient, { \r\n      headers: this.getHeaders()\r\n    }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === id);\r\n        if (index !== -1) {\r\n          currentData[index] = { ...currentData[index], ...cleanedClient };\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next(currentData.filter(client => client.id !== id));\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Gestionnaire d'erreur spécialisé pour la création\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    console.error('=== ERREUR SERVICE ===');\r\n    console.error('Status:', error.status);\r\n    console.error('Status Text:', error.statusText);\r\n    console.error('Error object:', error.error);\r\n    console.error('URL:', error.url);\r\n\r\n    // Log détaillé des erreurs de validation\r\n    if (error.error?.errors) {\r\n      console.error('Erreurs de validation détaillées:');\r\n      Object.entries(error.error.errors).forEach(([field, messages]) => {\r\n        console.error(`  ${field}:`, messages);\r\n      });\r\n    }\r\n    console.error('======================');\r\n\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez que l\\'API est démarrée.';\r\n    } else if (error.status === 400) {\r\n      if (error.error?.errors) {\r\n        // Extraire et formater les erreurs de validation spécifiques\r\n        const validationDetails: string[] = [];\r\n        Object.entries(error.error.errors).forEach(([field, messages]: [string, any]) => {\r\n          const messageArray = Array.isArray(messages) ? messages : [messages];\r\n          validationDetails.push(`${field}: ${messageArray.join(', ')}`);\r\n        });\r\n        errorMessage = `Erreurs de validation:\\n${validationDetails.join('\\n')}`;\r\n      } else if (error.error?.title) {\r\n        errorMessage = error.error.title;\r\n      } else {\r\n        errorMessage = 'Données invalides';\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec ce code existe déjà';\r\n    } else if (error.status === 500) {\r\n      errorMessage = 'Erreur interne du serveur';\r\n    } else {\r\n      const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n      if (apiError) {\r\n        errorMessage = `Erreur de création: ${apiError}`;\r\n      }\r\n    }\r\n\r\n    // Créer une erreur avec plus de détails\r\n    const detailedError = new Error(errorMessage);\r\n    (detailedError as any).originalError = error;\r\n    (detailedError as any).validationErrors = error.error?.errors;\r\n    return throwError(() => detailedError);\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Une erreur est survenue';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Non autorisé';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - le client existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAMrD,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIV,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAW,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIZ,eAAe,CAAgB,IAAI,CAACa,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAInB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIuB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAAuB;IAClCI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC7B,OAAO,CAAC;IACjC4B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEL,MAAM,CAAC;IACtCI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACjB,UAAU,EAAE,CAAC;IAE1C,MAAML,UAAU,GAAoB;MAClCuB,IAAI,EAAEN,MAAM,CAACM,IAAI,CAACC,IAAI,EAAE;MACxBC,MAAM,EAAER,MAAM,CAACQ,MAAM,EAAED,IAAI,EAAE;MAC7BE,SAAS,EAAET,MAAM,CAACS,SAAS,EAAEF,IAAI,EAAE;MACnCG,KAAK,EAAEV,MAAM,CAACU,KAAK,EAAEH,IAAI,EAAE;MAC3BI,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEJ,IAAI;KAClC;IAED;IACAK,MAAM,CAACC,IAAI,CAAC9B,UAAU,CAAC,CAAC+B,OAAO,CAACC,GAAG,IAAG;MACpC,IAAIhC,UAAU,CAACgC,GAA4B,CAAC,KAAKC,SAAS,EAAE;QAC1D,OAAOjC,UAAU,CAACgC,GAA4B,CAAC;;IAEnD,CAAC,CAAC;IAEFX,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEtB,UAAU,CAAC;IACvDqB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAAC/B,IAAI,CAAC2C,IAAI,CAAS,IAAI,CAACzC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B8B,OAAO,EAAE;KACV,CAAC,CAACzB,IAAI,CACLtB,GAAG,CAAEgD,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIE,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOF,SAAS;IAClB,CAAC,CAAC,EACFnD,GAAG,CAAEmD,SAAiB,IAAI;MACxB,MAAMG,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG4B,WAAW,EAAEH,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFlD,UAAU,CAAC,IAAI,CAACuD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAAC3B,EAAU,EAAEC,MAAuB;IAC9C,MAAM2B,aAAa,GAAoB,EAAE;IACzCf,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,OAAO,CAACC,GAAG,IAAG;MAChC,IAAIA,GAAG,KAAK,IAAI,IAAIf,MAAM,CAACe,GAA4B,CAAC,EAAE;QACxDY,aAAa,CAACZ,GAA4B,CAAC,GAAGf,MAAM,CAACe,GAA4B,CAAC;;IAEtF,CAAC,CAAC;IAEF,OAAO,IAAI,CAACzC,IAAI,CAACsD,GAAG,CAAO,GAAG,IAAI,CAACpD,OAAO,GAAGuB,EAAE,EAAE,EAAE4B,aAAa,EAAE;MAChEnC,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACK,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,MAAMK,KAAK,GAAGN,WAAW,CAACO,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKA,EAAE,CAAC;MACrD,IAAI8B,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,WAAW,CAACM,KAAK,CAAC,GAAG;UAAE,GAAGN,WAAW,CAACM,KAAK,CAAC;UAAE,GAAGF;QAAa,CAAE;QAChE,IAAI,CAAClD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG4B,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACFrD,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmC,YAAYA,CAACjC,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAAC2D,MAAM,CAAO,GAAG,IAAI,CAACzD,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC4B,WAAW,CAACW,MAAM,CAAClC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAsC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOrE,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF,OAAO,IAAI,CAAChD,IAAI,CAAC2D,MAAM,CAAC,GAAG,IAAI,CAACzD,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,IAAI,EAAEe;KACP,CAAC,CAAC3C,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC4B,WAAW,CAACW,MAAM,CAAClC,MAAM,IAAI,CAACoC,GAAG,CAACE,QAAQ,CAACtC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA0C,kBAAkBA,CAAA;IAChBvD,YAAY,CAACwD,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC7D,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI8C,IAAIA,CAAA;IACN,OAAO,IAAI,CAAChE,UAAU,CAAC+C,KAAK;EAC9B;EAEAkB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQlB,iBAAiBA,CAAC7B,KAAwB;IAChDQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;IACvCQ,OAAO,CAACR,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACgD,MAAM,CAAC;IACtCxC,OAAO,CAACR,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACiD,UAAU,CAAC;IAC/CzC,OAAO,CAACR,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CQ,OAAO,CAACR,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACkD,GAAG,CAAC;IAEhC;IACA,IAAIlD,KAAK,CAACA,KAAK,EAAEmD,MAAM,EAAE;MACvB3C,OAAO,CAACR,KAAK,CAAC,mCAAmC,CAAC;MAClDgB,MAAM,CAACoC,OAAO,CAACpD,KAAK,CAACA,KAAK,CAACmD,MAAM,CAAC,CAACjC,OAAO,CAAC,CAAC,CAACmC,KAAK,EAAEC,QAAQ,CAAC,KAAI;QAC/D9C,OAAO,CAACR,KAAK,CAAC,KAAKqD,KAAK,GAAG,EAAEC,QAAQ,CAAC;MACxC,CAAC,CAAC;;IAEJ9C,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;IAEvC,IAAIuD,YAAY,GAAG,sCAAsC;IAEzD,IAAIvD,KAAK,CAACgD,MAAM,KAAK,CAAC,EAAE;MACtBO,YAAY,GAAG,0EAA0E;KAC1F,MAAM,IAAIvD,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/B,IAAIhD,KAAK,CAACA,KAAK,EAAEmD,MAAM,EAAE;QACvB;QACA,MAAMK,iBAAiB,GAAa,EAAE;QACtCxC,MAAM,CAACoC,OAAO,CAACpD,KAAK,CAACA,KAAK,CAACmD,MAAM,CAAC,CAACjC,OAAO,CAAC,CAAC,CAACmC,KAAK,EAAEC,QAAQ,CAAgB,KAAI;UAC9E,MAAMG,YAAY,GAAGC,KAAK,CAACC,OAAO,CAACL,QAAQ,CAAC,GAAGA,QAAQ,GAAG,CAACA,QAAQ,CAAC;UACpEE,iBAAiB,CAACI,IAAI,CAAC,GAAGP,KAAK,KAAKI,YAAY,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;QAChE,CAAC,CAAC;QACFN,YAAY,GAAG,2BAA2BC,iBAAiB,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE;OACzE,MAAM,IAAI7D,KAAK,CAACA,KAAK,EAAE8D,KAAK,EAAE;QAC7BP,YAAY,GAAGvD,KAAK,CAACA,KAAK,CAAC8D,KAAK;OACjC,MAAM;QACLP,YAAY,GAAG,mBAAmB;;KAErC,MAAM,IAAIvD,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC5E,MAAM,CAACoF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI/D,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,oCAAoC;KACpD,MAAM,IAAIvD,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,2BAA2B;KAC3C,MAAM;MACL,MAAMS,QAAQ,GAAGhE,KAAK,CAACA,KAAK,EAAEiE,OAAO,IAAIjE,KAAK,CAACA,KAAK,EAAE8D,KAAK,IAAI9D,KAAK,CAACA,KAAK,EAAEA,KAAK;MACjF,IAAIgE,QAAQ,EAAE;QACZT,YAAY,GAAG,uBAAuBS,QAAQ,EAAE;;;IAIpD;IACA,MAAME,aAAa,GAAG,IAAIxC,KAAK,CAAC6B,YAAY,CAAC;IAC5CW,aAAqB,CAACC,aAAa,GAAGnE,KAAK;IAC3CkE,aAAqB,CAACE,gBAAgB,GAAGpE,KAAK,CAACA,KAAK,EAAEmD,MAAM;IAC7D,OAAO/E,UAAU,CAAC,MAAM8F,aAAa,CAAC;EACxC;EAEQjE,WAAWA,CAACD,KAAwB;IAC1C,IAAIuD,YAAY,GAAG,yBAAyB;IAC5C,MAAMS,QAAQ,GAAGhE,KAAK,CAACA,KAAK,EAAEiE,OAAO,IAAIjE,KAAK,CAACA,KAAK,EAAE8D,KAAK;IAE3D,IAAIE,QAAQ,EAAE;MACZT,YAAY,GAAGS,QAAQ;KACxB,MAAM,IAAIhE,KAAK,CAACgD,MAAM,KAAK,CAAC,EAAE;MAC7BO,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIvD,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,cAAc;MAC7B,IAAI,CAAC5E,MAAM,CAACoF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI/D,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAIvD,KAAK,CAACgD,MAAM,KAAK,GAAG,EAAE;MAC/BO,YAAY,GAAG,iCAAiC;;IAGlD,OAAOnF,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC6B,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAc,CAAA,G;qBA7OU7F,aAAa,EAAA8F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbpG,aAAa;IAAAqG,OAAA,EAAbrG,aAAa,CAAAsG,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}