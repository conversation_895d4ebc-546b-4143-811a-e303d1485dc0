import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { BulkDeleteConfirmationComponent } from './bulk-delete-confirmation.component';

describe('BulkDeleteConfirmationComponent', () => {
  let component: BulkDeleteConfirmationComponent;
  let fixture: ComponentFixture<BulkDeleteConfirmationComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<BulkDeleteConfirmationComponent>>;

  const mockData = {
    selectedProducts: [
      {
        id: '1',
        type: 'Service',
        description: 'Test service 1',
        prixUnitaireHT: 100,
        prixUnitaireTTC: 120,
        tva: 20,
        codeProd: 'TEST001'
      },
      {
        id: '2',
        type: 'Produit',
        description: 'Test produit 1',
        prixUnitaireHT: 200,
        prixUnitaireTTC: 240,
        tva: 20,
        codeProd: 'TEST002'
      }
    ],
    totalCount: 2
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [BulkDeleteConfirmationComponent],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockData }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(BulkDeleteConfirmationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display correct total count', () => {
    expect(component.data.totalCount).toBe(2);
  });

  it('should calculate total value correctly', () => {
    const totalValue = component.getTotalValue();
    expect(totalValue).toBe(360); // 120 + 240
  });

  it('should get product types summary correctly', () => {
    const summary = component.getProductTypesSummary();
    expect(summary['Service']).toBe(1);
    expect(summary['Produit']).toBe(1);
  });

  it('should close dialog with false on cancel', () => {
    component.onNoClick();
    expect(mockDialogRef.close).toHaveBeenCalledWith(false);
  });

  it('should close dialog with true on confirm', () => {
    component.confirmDelete();
    expect(mockDialogRef.close).toHaveBeenCalledWith(true);
  });
});
