{"ast": null, "code": "export class ClientModel {\n  constructor(client = {}) {\n    this.id = client.id || this.getRandomID();\n    this.code = client.code || '';\n    this.syntax = client.syntax || '';\n    this.matFiscal = client.matFiscal || '';\n    this.email = client.email || '';\n    this.telephone = client.telephone || '';\n    this.devis = client.devis || [];\n  }\n  getRandomID() {\n    const S4 = () => {\n      return (1 + Math.random()) * 0x10000 | 0;\n    };\n    return (S4() + S4()).toString();\n  }\n  // Méthode pour créer un client à partir d'un DTO\n  static fromCreateDto(dto) {\n    return new ClientModel({\n      id: this.generateId(),\n      code: dto.code,\n      syntax: dto.syntax || '',\n      matFiscal: dto.matFiscal || '',\n      email: dto.email || '',\n      telephone: dto.telephone || ''\n    });\n  }\n  // Méthode pour générer un ID (à adapter selon vos besoins)\n  static generateId() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0;\n      const v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  // Méthodes utilitaires\n  get fullIdentifier() {\n    return `${this.code} - ${this.syntax}`;\n  }\n  isValid() {\n    return !!this.code.trim();\n  }\n}\n// Interface de base pour Devis (à compléter selon votre modèle)", "map": {"version": 3, "names": ["ClientModel", "constructor", "client", "id", "getRandomID", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "devis", "S4", "Math", "random", "toString", "fromCreateDto", "dto", "generateId", "replace", "c", "r", "v", "fullIdentifier", "<PERSON><PERSON><PERSON><PERSON>", "trim"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\Model\\Client.ts"], "sourcesContent": ["export interface Client {\r\n  id: string;\r\n  code: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n  devis?: any[]; // Si vous avez un modèle <PERSON>\r\n}\r\n\r\nexport interface CreateClientDto {\r\n  code?: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n}\r\n\r\nexport interface UpdateClientDto {\r\n  code?: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n}\r\n\r\nexport class ClientModel implements Client {\r\n  id: string;\r\n  code: string;\r\n  syntax: string;\r\n  matFiscal: string;\r\n  email: string;\r\n  telephone: string;\r\n  devis: any[];\r\n\r\n  constructor(client: Partial<Client> = {}) {\r\n    this.id = client.id || this.getRandomID();\r\n    this.code = client.code || '';\r\n    this.syntax = client.syntax || '';\r\n    this.matFiscal = client.matFiscal || '';\r\n    this.email = client.email || '';\r\n    this.telephone = client.telephone || '';\r\n    this.devis = client.devis || [];\r\n  }\r\n\r\n  public getRandomID(): string {\r\n    const S4 = () => {\r\n      return ((1 + Math.random()) * 0x10000) | 0;\r\n    };\r\n    return (S4() + S4()).toString();\r\n  }\r\n\r\n  // Méthode pour créer un client à partir d'un DTO\r\n  static fromCreateDto(dto: CreateClientDto): ClientModel {\r\n    return new ClientModel({\r\n      id: this.generateId(), // Génère un nouvel ID\r\n      code: dto.code,\r\n      syntax: dto.syntax || '',\r\n      matFiscal: dto.matFiscal || '',\r\n      email: dto.email || '',\r\n      telephone: dto.telephone || ''\r\n    });\r\n  }\r\n\r\n  // Méthode pour générer un ID (à adapter selon vos besoins)\r\n  private static generateId(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0;\r\n      const v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  public get fullIdentifier(): string {\r\n    return `${this.code} - ${this.syntax}`;\r\n  }\r\n\r\n  public isValid(): boolean {\r\n    return !!this.code.trim();\r\n  }\r\n}\r\n\r\n// Interface de base pour Devis (à compléter selon votre modèle)\r\n"], "mappings": "AA0BA,OAAM,MAAOA,WAAW;EAStBC,YAAYC,MAAA,GAA0B,EAAE;IACtC,IAAI,CAACC,EAAE,GAAGD,MAAM,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;IACzC,IAAI,CAACC,IAAI,GAAGH,MAAM,CAACG,IAAI,IAAI,EAAE;IAC7B,IAAI,CAACC,MAAM,GAAGJ,MAAM,CAACI,MAAM,IAAI,EAAE;IACjC,IAAI,CAACC,SAAS,GAAGL,MAAM,CAACK,SAAS,IAAI,EAAE;IACvC,IAAI,CAACC,KAAK,GAAGN,MAAM,CAACM,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,SAAS,GAAGP,MAAM,CAACO,SAAS,IAAI,EAAE;IACvC,IAAI,CAACC,KAAK,GAAGR,MAAM,CAACQ,KAAK,IAAI,EAAE;EACjC;EAEON,WAAWA,CAAA;IAChB,MAAMO,EAAE,GAAGA,CAAA,KAAK;MACd,OAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,IAAI,OAAO,GAAI,CAAC;IAC5C,CAAC;IACD,OAAO,CAACF,EAAE,EAAE,GAAGA,EAAE,EAAE,EAAEG,QAAQ,EAAE;EACjC;EAEA;EACA,OAAOC,aAAaA,CAACC,GAAoB;IACvC,OAAO,IAAIhB,WAAW,CAAC;MACrBG,EAAE,EAAE,IAAI,CAACc,UAAU,EAAE;MACrBZ,IAAI,EAAEW,GAAG,CAACX,IAAI;MACdC,MAAM,EAAEU,GAAG,CAACV,MAAM,IAAI,EAAE;MACxBC,SAAS,EAAES,GAAG,CAACT,SAAS,IAAI,EAAE;MAC9BC,KAAK,EAAEQ,GAAG,CAACR,KAAK,IAAI,EAAE;MACtBC,SAAS,EAAEO,GAAG,CAACP,SAAS,IAAI;KAC7B,CAAC;EACJ;EAEA;EACQ,OAAOQ,UAAUA,CAAA;IACvB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC;MACvE,MAAMC,CAAC,GAAGR,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;MAChC,MAAMQ,CAAC,GAAGF,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACzC,OAAOC,CAAC,CAACP,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEA;EACA,IAAWQ,cAAcA,CAAA;IACvB,OAAO,GAAG,IAAI,CAACjB,IAAI,MAAM,IAAI,CAACC,MAAM,EAAE;EACxC;EAEOiB,OAAOA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAAClB,IAAI,CAACmB,IAAI,EAAE;EAC3B;;AAGF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}