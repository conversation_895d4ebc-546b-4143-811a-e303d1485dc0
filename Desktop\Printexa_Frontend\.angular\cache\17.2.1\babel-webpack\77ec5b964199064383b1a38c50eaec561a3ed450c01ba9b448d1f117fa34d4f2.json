{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      code: client.code.trim(),\n      syntax: client.syntax ? client.syntax.trim() : undefined,\n      matFiscal: client.matFiscal ? client.matFiscal.trim() : undefined,\n      email: client.email ? client.email.trim() : undefined,\n      telephone: client.telephone ? client.telephone.trim() : undefined\n    };\n    console.log('Tentative de création du client:', clientData);\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      console.log('Réponse complète du serveur:', response);\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      console.log('Client créé avec succès:', newClient);\n      // Mettre à jour le cache local\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(error => {\n      console.error('Erreur détaillée lors de la création:', {\n        status: error.status,\n        statusText: error.statusText,\n        error: error.error,\n        message: error.message,\n        url: error.url,\n        headers: error.headers\n      });\n      return this.handleCreateError(error);\n    }));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n      } else {\n        return throwError(() => new Error(`Client avec le code ${id} non trouvé`));\n      }\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}. L'ID doit être un GUID valide.`));\n    }\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('ID original:', id);\n    console.log('ID résolu:', actualId);\n    console.log('Données à envoyer:', client);\n    console.log('==============================');\n    return this.http.put(`${this.baseUrl}${actualId}`, client, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Client mis à jour avec succès');\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Client supprimé avec succès');\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Méthode pour supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log(`${ids.length} clients supprimés avec succès`);\n    }), catchError(this.handleError));\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthode de debug pour tester la connexion\n  testConnection() {\n    console.log('Test de connexion vers:', this.baseUrl);\n    console.log('Headers utilisés:', this.getHeaders());\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('Test de connexion réussi:', response);\n    }), catchError(error => {\n      console.error('Test de connexion échoué:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Gestionnaire d'erreur spécialisé pour la création\n  handleCreateError(error) {\n    console.error('Erreur lors de la création du client:', error);\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un client.';\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ces caractéristiques existe déjà.';\n    } else if (error.status === 422) {\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    console.error('ClientService error:', error);\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "syntax", "undefined", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "console", "log", "post", "observe", "response", "newClient", "body", "currentData", "value", "status", "statusText", "message", "url", "handleCreateError", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "put", "clearCurrentClient", "deleteClient", "delete", "deleteSelectedClients", "ids", "length", "removeItem", "data", "getDialogData", "dialogData", "testConnection", "errorMessage", "apiError", "title", "errors", "validationErrors", "Object", "values", "flat", "join", "navigate", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\n  private currentClientSubject: BehaviorSubject<Client | null>;\n  public currentClient$: Observable<Client | null>;\n  dataChange = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n\n  constructor(private http: HttpClient, private router: Router) {\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n\n  private getClientFromStorage(): Client | null {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\n    });\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false; // Désactivation du loading\n        this.dataChange.next(clients);\n      }),\n      catchError(error => {\n        this.isTblLoading = false; // Désactivation en cas d'erreur\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n\n    // Préparer les données à envoyer\n    const clientData: CreateClientSimpleDto = {\n      code: client.code.trim(),\n      syntax: client.syntax ? client.syntax.trim() : undefined,\n      matFiscal: client.matFiscal ? client.matFiscal.trim() : undefined,\n      email: client.email ? client.email.trim() : undefined,\n      telephone: client.telephone ? client.telephone.trim() : undefined\n    };\n\n    console.log('Tentative de création du client:', clientData);\n\n    return this.http.post<Client>(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(\n      map((response: any) => {\n        console.log('Réponse complète du serveur:', response);\n        const newClient: Client = response.body;\n        if (!newClient) {\n          throw new Error('Aucune donnée reçue du serveur');\n        }\n        return newClient;\n      }),\n      tap((newClient: Client) => {\n        console.log('Client créé avec succès:', newClient);\n        // Mettre à jour le cache local\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError((error: HttpErrorResponse) => {\n        console.error('Erreur détaillée lors de la création:', {\n          status: error.status,\n          statusText: error.statusText,\n          error: error.error,\n          message: error.message,\n          url: error.url,\n          headers: error.headers\n        });\n        return this.handleCreateError(error);\n      })\n    );\n  }\n\n  // Mettre à jour un client\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n      } else {\n        return throwError(() => new Error(`Client avec le code ${id} non trouvé`));\n      }\n    }\n\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}. L'ID doit être un GUID valide.`));\n    }\n\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('ID original:', id);\n    console.log('ID résolu:', actualId);\n    console.log('Données à envoyer:', client);\n    console.log('==============================');\n\n    return this.http.put<void>(`${this.baseUrl}${actualId}`, client, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        console.log('Client mis à jour avec succès');\n        this.clearCurrentClient();\n      }),\n      catchError(this.handleError)\n    );\n  }\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        console.log('Client supprimé avec succès');\n        this.clearCurrentClient();\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Méthode pour supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        console.log(`${ids.length} clients supprimés avec succès`);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthode de debug pour tester la connexion\n  testConnection(): Observable<any> {\n    console.log('Test de connexion vers:', this.baseUrl);\n    console.log('Headers utilisés:', this.getHeaders());\n\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(\n      tap(response => {\n        console.log('Test de connexion réussi:', response);\n      }),\n      catchError(error => {\n        console.error('Test de connexion échoué:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  // Gestionnaire d'erreur spécialisé pour la création\n  private handleCreateError(error: HttpErrorResponse) {\n    console.error('Erreur lors de la création du client:', error);\n\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un client.';\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ces caractéristiques existe déjà.';\n    } else if (error.status === 422) {\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n\n  private handleError(error: HttpErrorResponse) {\n    console.error('ClientService error:', error);\n\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIV,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAW,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIZ,eAAe,CAAgB,IAAI,CAACa,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAInB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIuB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE;IACA,MAAMrB,UAAU,GAA0B;MACxCsB,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBC,MAAM,EAAEP,MAAM,CAACO,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACD,IAAI,EAAE,GAAGE,SAAS;MACxDC,SAAS,EAAET,MAAM,CAACS,SAAS,GAAGT,MAAM,CAACS,SAAS,CAACH,IAAI,EAAE,GAAGE,SAAS;MACjEE,KAAK,EAAEV,MAAM,CAACU,KAAK,GAAGV,MAAM,CAACU,KAAK,CAACJ,IAAI,EAAE,GAAGE,SAAS;MACrDG,SAAS,EAAEX,MAAM,CAACW,SAAS,GAAGX,MAAM,CAACW,SAAS,CAACL,IAAI,EAAE,GAAGE;KACzD;IAEDI,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE9B,UAAU,CAAC;IAE3D,OAAO,IAAI,CAACT,IAAI,CAACwC,IAAI,CAAS,IAAI,CAACtC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B2B,OAAO,EAAE;KACV,CAAC,CAACtB,IAAI,CACLtB,GAAG,CAAE6C,QAAa,IAAI;MACpBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,QAAQ,CAAC;MACrD,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIb,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOa,SAAS;IAClB,CAAC,CAAC,EACFhD,GAAG,CAAEgD,SAAiB,IAAI;MACxBL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEI,SAAS,CAAC;MAClD;MACA,MAAME,WAAW,GAAG,IAAI,CAAC1C,UAAU,CAAC2C,KAAK;MACzC,IAAI,CAAC3C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGwB,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF/C,UAAU,CAAE0B,KAAwB,IAAI;MACtCgB,OAAO,CAAChB,KAAK,CAAC,uCAAuC,EAAE;QACrDyB,MAAM,EAAEzB,KAAK,CAACyB,MAAM;QACpBC,UAAU,EAAE1B,KAAK,CAAC0B,UAAU;QAC5B1B,KAAK,EAAEA,KAAK,CAACA,KAAK;QAClB2B,OAAO,EAAE3B,KAAK,CAAC2B,OAAO;QACtBC,GAAG,EAAE5B,KAAK,CAAC4B,GAAG;QACdhC,OAAO,EAAEI,KAAK,CAACJ;OAChB,CAAC;MACF,OAAO,IAAI,CAACiC,iBAAiB,CAAC7B,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA8B,YAAYA,CAAC3B,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEO,IAAI,EAAE,IAAI,CAACN,MAAM,EAAE;MAC1B,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIuB,QAAQ,GAAG5B,EAAE;IACjB,MAAM6B,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAAC9B,EAAE,CAAC;IAC1C,IAAI6B,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAACrD,UAAU,CAAC2C,KAAK,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC3B,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAI+B,YAAY,EAAE;QAChBH,QAAQ,GAAGG,YAAY,CAAC/B,EAAE;OAC3B,MAAM;QACL,OAAO/B,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uBAAuBL,EAAE,aAAa,CAAC,CAAC;;;IAI9E;IACA,MAAMkC,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAOjE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,gBAAgBuB,QAAQ,kCAAkC,CAAC,CAAC;;IAGhGf,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEd,EAAE,CAAC;IAC/Ba,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEc,QAAQ,CAAC;IACnCf,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEb,MAAM,CAAC;IACzCY,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAACvC,IAAI,CAAC4D,GAAG,CAAO,GAAG,IAAI,CAAC1D,OAAO,GAAGmD,QAAQ,EAAE,EAAE3B,MAAM,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACnGxB,GAAG,CAAC,MAAK;MACP2C,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACsB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFjE,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EACA;EACAuC,YAAYA,CAACrC,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAAC+D,MAAM,CAAO,GAAG,IAAI,CAAC7D,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP2C,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACsB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFjE,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAyC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOxE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF,OAAO,IAAI,CAAC9B,IAAI,CAAC+D,MAAM,CAAC,GAAG,IAAI,CAAC7D,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B8B,IAAI,EAAEqB;KACP,CAAC,CAAC9C,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP2C,OAAO,CAACC,GAAG,CAAC,GAAG0B,GAAG,CAACC,MAAM,gCAAgC,CAAC;IAC5D,CAAC,CAAC,EACFtE,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAsC,kBAAkBA,CAAA;IAChBnD,YAAY,CAACyD,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC9D,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI+C,IAAIA,CAAA;IACN,OAAO,IAAI,CAACjE,UAAU,CAAC2C,KAAK;EAC9B;EAEAuB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACAC,cAAcA,CAAA;IACZjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACrC,OAAO,CAAC;IACpDoC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACzB,UAAU,EAAE,CAAC;IAEnD,OAAO,IAAI,CAACd,IAAI,CAACiB,GAAG,CAAC,IAAI,CAACf,OAAO,EAAE;MACjCgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B2B,OAAO,EAAE;KACV,CAAC,CAACtB,IAAI,CACLxB,GAAG,CAAC+C,QAAQ,IAAG;MACbJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,QAAQ,CAAC;IACpD,CAAC,CAAC,EACF9C,UAAU,CAAC0B,KAAK,IAAG;MACjBgB,OAAO,CAAChB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQ6B,iBAAiBA,CAAC7B,KAAwB;IAChDgB,OAAO,CAAChB,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAE7D,IAAIkD,YAAY,GAAG,sCAAsC;IACzD,MAAMC,QAAQ,GAAGnD,KAAK,CAACA,KAAK,EAAE2B,OAAO,IAAI3B,KAAK,CAACA,KAAK,EAAEoD,KAAK,IAAIpD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAImD,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAInD,KAAK,CAACyB,MAAM,KAAK,CAAC,EAAE;MAC7ByB,YAAY,GAAG,2EAA2E;KAC3F,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,uDAAuD;MACtE,IAAIlD,KAAK,CAACA,KAAK,EAAEqD,MAAM,EAAE;QACvB,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAACxD,KAAK,CAACA,KAAK,CAACqD,MAAM,CAAC,CAACI,IAAI,EAAE;QACjEP,YAAY,IAAI,aAAaI,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAI1D,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,6CAA6C;MAC5D,IAAI,CAACvE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3D,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,kDAAkD;KAClE,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,6DAA6D;KAC7E,MAAM,IAAIlD,KAAK,CAACyB,MAAM,IAAI,GAAG,EAAE;MAC9ByB,YAAY,GAAG,kDAAkD;;IAGnE,OAAO9E,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC0C,YAAY,CAAC,CAAC;EAClD;EAEQjD,WAAWA,CAACD,KAAwB;IAC1CgB,OAAO,CAAChB,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAE5C,IAAIkD,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAGnD,KAAK,CAACA,KAAK,EAAE2B,OAAO,IAAI3B,KAAK,CAACA,KAAK,EAAEoD,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAInD,KAAK,CAACyB,MAAM,KAAK,CAAC,EAAE;MAC7ByB,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACvE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3D,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,WAAW;KAC3B,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAIlD,KAAK,CAACyB,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,kCAAkC;KAClD,MAAM,IAAIlD,KAAK,CAACyB,MAAM,IAAI,GAAG,EAAE;MAC9ByB,YAAY,GAAG,cAAc;;IAG/B,OAAO9E,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC0C,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAU,CAAA,G;qBAxQUpF,aAAa,EAAAqF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb3F,aAAa;IAAA4F,OAAA,EAAb5F,aAAa,CAAA6F,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}