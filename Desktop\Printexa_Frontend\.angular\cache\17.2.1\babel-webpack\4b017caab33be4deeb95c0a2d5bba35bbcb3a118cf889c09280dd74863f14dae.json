{"ast": null, "code": "import { Mat<PERSON>aginator, MatPaginatorModule } from '@angular/material/paginator';\nimport { MatSort, MatSortModule } from '@angular/material/sort';\nimport * as collections from '@angular/cdk/collections';\nimport { BehaviorSubject, fromEvent, merge } from 'rxjs';\nimport { map } from 'rxjs/operators';\nimport { MatMenuTrigger, MatMenuModule } from '@angular/material/menu';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { FormProduitComponent } from '../form-produit/form-produit.component';\nimport { ProduitDeleteComponent } from '../produit-delete/produit-delete.component';\nimport { BulkDeleteConfirmationComponent } from '../bulk-delete-confirmation/bulk-delete-confirmation.component';\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\nimport { TableExportUtil } from '@shared';\nimport { NgClass } from '@angular/common';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatRippleModule } from '@angular/material/core';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatTableModule } from '@angular/material/table';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/material/dialog\";\nimport * as i3 from \"../../services/produit.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/tooltip\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/table\";\nimport * as i9 from \"@angular/material/sort\";\nimport * as i10 from \"@angular/material/checkbox\";\nimport * as i11 from \"@angular/material/menu\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nimport * as i14 from \"@angular/material/paginator\";\nconst _c0 = [\"filter\"];\nfunction ProduitComponent_mat_header_cell_46_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r23 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 53)(1, \"mat-checkbox\", 54);\n    i0.ɵɵlistener(\"change\", function ProduitComponent_mat_header_cell_46_Template_mat_checkbox_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r23);\n      const ctx_r22 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r22.masterToggle() : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"tbl-col-width-per-6\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r1.selection.hasValue() && ctx_r1.isAllSelected())(\"indeterminate\", ctx_r1.selection.hasValue() && !ctx_r1.isAllSelected())(\"ngClass\", \"tbl-checkbox\");\n  }\n}\nfunction ProduitComponent_mat_cell_47_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r27 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 53)(1, \"mat-checkbox\", 55);\n    i0.ɵɵlistener(\"click\", function ProduitComponent_mat_cell_47_Template_mat_checkbox_click_1_listener($event) {\n      return $event.stopPropagation();\n    })(\"change\", function ProduitComponent_mat_cell_47_Template_mat_checkbox_change_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r27);\n      const row_r24 = restoredCtx.$implicit;\n      const ctx_r26 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView($event ? ctx_r26.selection.toggle(row_r24) : null);\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r24 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", \"tbl-col-width-per-6\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"checked\", ctx_r2.selection.isSelected(row_r24))(\"ngClass\", \"tbl-checkbox\");\n  }\n}\nfunction ProduitComponent_mat_header_cell_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"Code Produit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Code:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r28 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r28.codeProd || \"-\", \" \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"Type\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r29 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r29.type, \" \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"Description\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\", 58)(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r30 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r30.description, \" \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"Prix HT\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Prix HT:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r31 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r31.prixUnitaireHT, \" \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"Prix TTC\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 57);\n    i0.ɵɵtext(2, \"Prix TTC:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r32 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r32.prixUnitaireTTC, \" \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 56);\n    i0.ɵɵtext(1, \"TVA (%)\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-cell\")(1, \"span\", 57);\n    i0.ɵɵtext(2, \"TVA:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const row_r33 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", row_r33.tva, \"% \");\n  }\n}\nfunction ProduitComponent_mat_header_cell_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-header-cell\", 59);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProduitComponent_mat_cell_68_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r39 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-cell\", 59)(1, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function ProduitComponent_mat_cell_68_Template_button_click_1_listener($event) {\n      return $event.stopPropagation();\n    });\n    i0.ɵɵelementStart(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 61)(6, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ProduitComponent_mat_cell_68_Template_button_click_6_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const row_r34 = restoredCtx.$implicit;\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.editCall(row_r34));\n    });\n    i0.ɵɵelementStart(7, \"i\", 63);\n    i0.ɵɵtext(8, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10, \"Modifier\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"button\", 62);\n    i0.ɵɵlistener(\"click\", function ProduitComponent_mat_cell_68_Template_button_click_11_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r39);\n      const row_r34 = restoredCtx.$implicit;\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.deleteItem(row_r34));\n    });\n    i0.ɵɵelementStart(12, \"i\", 63);\n    i0.ɵɵtext(13, \"delete_forever\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\");\n    i0.ɵɵtext(15, \"Supprimer\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const _r36 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", _r36);\n  }\n}\nfunction ProduitComponent_mat_header_row_69_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-header-row\");\n  }\n}\nfunction ProduitComponent_mat_row_70_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-row\", 64);\n  }\n}\nfunction ProduitComponent_Conditional_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 65);\n    i0.ɵɵelement(1, \"mat-progress-spinner\", 66);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"diameter\", 40);\n  }\n}\nfunction ProduitComponent_Conditional_72_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 67);\n    i0.ɵɵtext(1, \" Aucun r\\u00E9sultat trouv\\u00E9 \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r20 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"display\", ctx_r20.dataSource.renderedData.length === 0 ? \"\" : \"none\");\n  }\n}\nconst _c1 = () => [\"Home\"];\nconst _c2 = () => [5, 10, 25, 100];\nexport class ProduitComponent extends UnsubscribeOnDestroyAdapter {\n  constructor(httpClient, dialog, produitService, snackBar) {\n    super();\n    this.httpClient = httpClient;\n    this.dialog = dialog;\n    this.produitService = produitService;\n    this.snackBar = snackBar;\n    this.filterToggle = false;\n    this.displayedColumns = ['select', 'codeProd', 'type', 'description', 'prixUnitaireHT', 'prixUnitaireTTC', 'tva', 'actions'];\n    this.selection = new SelectionModel(true, []);\n    this.contextMenuPosition = {\n      x: '0px',\n      y: '0px'\n    };\n  }\n  ngOnInit() {\n    this.loadData();\n  }\n  refresh() {\n    this.loadData();\n  }\n  addNew() {\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormProduitComponent, {\n      data: {\n        produit: this.produit,\n        action: 'add'\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\n        this.showNotification('snackbar-success', 'Produit ajouté avec succès...!!!', 'bottom', 'center');\n      }\n    });\n  }\n  editCall(row) {\n    this.id = row.id;\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(FormProduitComponent, {\n      data: {\n        produit: row,\n        action: 'edit'\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\n        this.showNotification('black', 'Produit modifié avec succès...!!!', 'bottom', 'center');\n      }\n    });\n  }\n  deleteItem(row) {\n    this.id = row.id;\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(ProduitDeleteComponent, {\n      width: '500px',\n      maxWidth: '90vw',\n      data: row,\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\n        this.showNotification('snackbar-danger', 'Produit supprimé avec succès...!!!', 'bottom', 'center');\n      }\n    });\n  }\n  refreshTable() {\n    this.paginator._changePageSize(this.paginator.pageSize);\n  }\n  isAllSelected() {\n    const numSelected = this.selection.selected.length;\n    const numRows = this.dataSource.renderedData.length;\n    return numSelected === numRows;\n  }\n  masterToggle() {\n    this.isAllSelected() ? this.selection.clear() : this.dataSource.renderedData.forEach(row => this.selection.select(row));\n  }\n  removeSelectedRows() {\n    const totalSelect = this.selection.selected.length;\n    if (totalSelect === 0) {\n      this.showNotification('snackbar-warning', 'Aucun produit sélectionné pour la suppression', 'bottom', 'center');\n      return;\n    }\n    // Ouvrir le dialogue de confirmation personnalisé\n    let tempDirection;\n    if (localStorage.getItem('isRtl') === 'true') {\n      tempDirection = 'rtl';\n    } else {\n      tempDirection = 'ltr';\n    }\n    const dialogRef = this.dialog.open(BulkDeleteConfirmationComponent, {\n      width: '600px',\n      maxWidth: '90vw',\n      data: {\n        selectedProducts: this.selection.selected,\n        totalCount: totalSelect\n      },\n      direction: tempDirection\n    });\n    this.subs.sink = dialogRef.afterClosed().subscribe(confirmed => {\n      if (confirmed) {\n        // Récupérer les IDs des produits sélectionnés\n        const selectedIds = this.selection.selected.map(item => item.id);\n        // Appeler l'API pour supprimer les produits sélectionnés\n        this.produitService.deleteSelectedProduits(selectedIds).subscribe({\n          next: () => {\n            // Supprimer les éléments de la vue locale après succès de l'API\n            this.selection.selected.forEach(item => {\n              const index = this.dataSource.renderedData.findIndex(d => d === item);\n              if (index !== -1) {\n                this.exampleDatabase?.dataChange.value.splice(index, 1);\n              }\n            });\n            // Réinitialiser la sélection\n            this.selection = new SelectionModel(true, []);\n            // Rafraîchir le tableau\n            this.refreshTable();\n            // Recharger les données depuis l'API pour s'assurer de la cohérence\n            this.produitService.getAllProduits().subscribe();\n            // Afficher la notification de succès\n            this.showNotification('snackbar-danger', `${totalSelect} produit(s) supprimé(s) avec succès !`, 'bottom', 'center');\n          },\n          error: error => {\n            console.error('Erreur lors de la suppression des produits:', error);\n            this.showNotification('snackbar-danger', 'Erreur lors de la suppression des produits. Veuillez réessayer.', 'bottom', 'center');\n          }\n        });\n      }\n    });\n  }\n  loadData() {\n    this.dataSource = new ProduitDataSource(this.produitService, this.paginator, this.sort);\n    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(() => {\n      if (!this.dataSource) {\n        return;\n      }\n      this.dataSource.filter = this.filter.nativeElement.value;\n    });\n    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(() => {\n      if (!this.dataSource) {\n        return;\n      }\n      this.dataSource.filter = this.filter.nativeElement.value;\n    });\n  }\n  exportExcel() {\n    const exportData = this.dataSource.filteredData.map(x => ({\n      'Code Produit': x.codeProd,\n      Type: x.type,\n      Description: x.description,\n      'Prix HT': x.prixUnitaireHT,\n      'Prix TTC': x.prixUnitaireTTC,\n      'TVA (%)': x.tva\n    }));\n    TableExportUtil.exportToExcel(exportData, 'produits');\n  }\n  showNotification(colorName, text, placementFrom, placementAlign) {\n    this.snackBar.open(text, '', {\n      duration: 2000,\n      verticalPosition: placementFrom,\n      horizontalPosition: placementAlign,\n      panelClass: colorName\n    });\n  }\n  static #_ = this.ɵfac = function ProduitComponent_Factory(t) {\n    return new (t || ProduitComponent)(i0.ɵɵdirectiveInject(i1.HttpClient), i0.ɵɵdirectiveInject(i2.MatDialog), i0.ɵɵdirectiveInject(i3.ProduitService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProduitComponent,\n    selectors: [[\"app-produit\"]],\n    viewQuery: function ProduitComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(MatPaginator, 7);\n        i0.ɵɵviewQuery(MatSort, 7);\n        i0.ɵɵviewQuery(_c0, 7);\n        i0.ɵɵviewQuery(MatMenuTrigger, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.paginator = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.sort = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.filter = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.contextMenu = _t.first);\n      }\n    },\n    standalone: true,\n    features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n    decls: 75,\n    vars: 15,\n    consts: [[1, \"content\"], [1, \"content-block\"], [1, \"block-header\"], [3, \"title\", \"items\", \"active_item\"], [1, \"row\"], [1, \"col-md-12\"], [1, \"tabbable-line\"], [1, \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"col-xs-12\"], [1, \"card\"], [1, \"materialTableHeader\"], [1, \"left\"], [1, \"header-buttons-left\", \"ms-0\"], [1, \"tbl-title\"], [1, \"tbl-search-box\"], [\"for\", \"search-input\"], [1, \"material-icons\", \"search-icon\"], [\"placeholder\", \"Rechercher\", \"type\", \"text\", \"aria-label\", \"Search box\", 1, \"browser-default\", \"search-field\"], [\"filter\", \"\"], [1, \"right\"], [1, \"tbl-export-btn\"], [1, \"tbl-header-btn\"], [\"matTooltip\", \"AJOUTER\", 1, \"m-l-10\"], [\"mat-mini-fab\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"col-white\"], [\"matTooltip\", \"RAFRA\\u00CECHIR\", 1, \"m-l-10\"], [\"matTooltip\", \"SUPPRIMER\", 1, \"m-l-10\", 3, \"hidden\"], [\"mat-mini-fab\", \"\", \"color\", \"warn\", 3, \"click\"], [\"matTooltip\", \"EXPORT XLSX\", 1, \"export-button\", \"m-l-10\"], [\"src\", \"assets/images/icons/xlsx.png\", \"alt\", \"\", 3, \"click\"], [1, \"body\", \"overflow-auto\"], [1, \"responsive_table\"], [\"mat-table\", \"\", \"matSort\", \"\", 1, \"mat-cell\", \"advance-table\", 3, \"dataSource\"], [\"matColumnDef\", \"select\"], [3, \"ngClass\", 4, \"matHeaderCellDef\"], [3, \"ngClass\", 4, \"matCellDef\"], [\"matColumnDef\", \"codeProd\"], [\"mat-sort-header\", \"\", 4, \"matHeaderCellDef\"], [4, \"matCellDef\"], [\"matColumnDef\", \"type\"], [\"matColumnDef\", \"description\"], [\"class\", \"column-nowrap\", 4, \"matCellDef\"], [\"matColumnDef\", \"prixUnitaireHT\"], [\"matColumnDef\", \"prixUnitaireTTC\"], [\"matColumnDef\", \"tva\"], [\"matColumnDef\", \"actions\"], [\"class\", \"psl-3 tbl-col-width-per-10\", 4, \"matHeaderCellDef\"], [\"class\", \"psl-3 tbl-col-width-per-10\", 4, \"matCellDef\"], [4, \"matHeaderRowDef\"], [\"matRipple\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [\"class\", \"tbl-spinner\"], [\"class\", \"no-results\", 3, \"display\"], [3, \"length\", \"pageIndex\", \"pageSize\", \"pageSizeOptions\"], [\"paginator\", \"\"], [3, \"ngClass\"], [3, \"checked\", \"indeterminate\", \"ngClass\", \"change\"], [3, \"checked\", \"ngClass\", \"click\", \"change\"], [\"mat-sort-header\", \"\"], [1, \"mobile-label\"], [1, \"column-nowrap\"], [1, \"psl-3\", \"tbl-col-width-per-10\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\", \"click\"], [\"menu\", \"matMenu\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"material-icons-outlined\", \"align-middle\", \"msr-2\"], [\"matRipple\", \"\"], [1, \"tbl-spinner\"], [\"color\", \"primary\", \"mode\", \"indeterminate\", 3, \"diameter\"], [1, \"no-results\"]],\n    template: function ProduitComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelement(3, \"app-breadcrumb\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6)(7, \"div\", 4)(8, \"div\", 7)(9, \"div\", 8)(10, \"div\", 9)(11, \"div\", 10)(12, \"ul\", 11)(13, \"li\", 12)(14, \"h2\");\n        i0.ɵɵtext(15, \"Produits\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 13)(17, \"label\", 14)(18, \"i\", 15);\n        i0.ɵɵtext(19, \"search\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(20, \"input\", 16, 17);\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(22, \"div\", 18)(23, \"ul\", 19)(24, \"li\", 20)(25, \"div\", 21)(26, \"button\", 22);\n        i0.ɵɵlistener(\"click\", function ProduitComponent_Template_button_click_26_listener() {\n          return ctx.addNew();\n        });\n        i0.ɵɵelementStart(27, \"mat-icon\", 23);\n        i0.ɵɵtext(28, \"add\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(29, \"li\", 20)(30, \"div\", 24)(31, \"button\", 22);\n        i0.ɵɵlistener(\"click\", function ProduitComponent_Template_button_click_31_listener() {\n          return ctx.refresh();\n        });\n        i0.ɵɵelementStart(32, \"mat-icon\", 23);\n        i0.ɵɵtext(33, \"refresh\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(34, \"li\", 20)(35, \"div\", 25)(36, \"button\", 26);\n        i0.ɵɵlistener(\"click\", function ProduitComponent_Template_button_click_36_listener() {\n          return ctx.removeSelectedRows();\n        });\n        i0.ɵɵelementStart(37, \"mat-icon\", 23);\n        i0.ɵɵtext(38, \"delete\");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(39, \"li\")(40, \"div\", 27)(41, \"img\", 28);\n        i0.ɵɵlistener(\"click\", function ProduitComponent_Template_img_click_41_listener() {\n          return ctx.exportExcel();\n        });\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(42, \"div\", 29)(43, \"div\", 30)(44, \"table\", 31);\n        i0.ɵɵelementContainerStart(45, 32);\n        i0.ɵɵtemplate(46, ProduitComponent_mat_header_cell_46_Template, 2, 4, \"mat-header-cell\", 33)(47, ProduitComponent_mat_cell_47_Template, 2, 3, \"mat-cell\", 34);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(48, 35);\n        i0.ɵɵtemplate(49, ProduitComponent_mat_header_cell_49_Template, 2, 0, \"mat-header-cell\", 36)(50, ProduitComponent_mat_cell_50_Template, 4, 1, \"mat-cell\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(51, 38);\n        i0.ɵɵtemplate(52, ProduitComponent_mat_header_cell_52_Template, 2, 0, \"mat-header-cell\", 36)(53, ProduitComponent_mat_cell_53_Template, 4, 1, \"mat-cell\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(54, 39);\n        i0.ɵɵtemplate(55, ProduitComponent_mat_header_cell_55_Template, 2, 0, \"mat-header-cell\", 36)(56, ProduitComponent_mat_cell_56_Template, 4, 1, \"mat-cell\", 40);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(57, 41);\n        i0.ɵɵtemplate(58, ProduitComponent_mat_header_cell_58_Template, 2, 0, \"mat-header-cell\", 36)(59, ProduitComponent_mat_cell_59_Template, 4, 1, \"mat-cell\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(60, 42);\n        i0.ɵɵtemplate(61, ProduitComponent_mat_header_cell_61_Template, 2, 0, \"mat-header-cell\", 36)(62, ProduitComponent_mat_cell_62_Template, 4, 1, \"mat-cell\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(63, 43);\n        i0.ɵɵtemplate(64, ProduitComponent_mat_header_cell_64_Template, 2, 0, \"mat-header-cell\", 36)(65, ProduitComponent_mat_cell_65_Template, 4, 1, \"mat-cell\", 37);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementContainerStart(66, 44);\n        i0.ɵɵtemplate(67, ProduitComponent_mat_header_cell_67_Template, 2, 0, \"mat-header-cell\", 45)(68, ProduitComponent_mat_cell_68_Template, 16, 1, \"mat-cell\", 46);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵtemplate(69, ProduitComponent_mat_header_row_69_Template, 1, 0, \"mat-header-row\", 47)(70, ProduitComponent_mat_row_70_Template, 1, 0, \"mat-row\", 48);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(71, ProduitComponent_Conditional_71_Template, 2, 1, \"div\", 49)(72, ProduitComponent_Conditional_72_Template, 2, 2, \"div\", 50);\n        i0.ɵɵelement(73, \"mat-paginator\", 51, 52);\n        i0.ɵɵelementEnd()()()()()()()()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"title\", \"Produits\")(\"items\", i0.ɵɵpureFunction0(13, _c1))(\"active_item\", \"Produits\");\n        i0.ɵɵadvance(32);\n        i0.ɵɵproperty(\"hidden\", !ctx.selection.hasValue());\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"dataSource\", ctx.dataSource);\n        i0.ɵɵadvance(25);\n        i0.ɵɵproperty(\"matHeaderRowDef\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"matRowDefColumns\", ctx.displayedColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(71, (ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 71 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(72, !(ctx.exampleDatabase == null ? null : ctx.exampleDatabase.isTblLoading) ? 72 : -1);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"length\", ctx.dataSource.filteredData.length)(\"pageIndex\", 0)(\"pageSize\", 10)(\"pageSizeOptions\", i0.ɵɵpureFunction0(14, _c2));\n      }\n    },\n    dependencies: [BreadcrumbComponent, MatTooltipModule, i5.MatTooltip, MatButtonModule, i6.MatIconButton, i6.MatMiniFabButton, MatIconModule, i7.MatIcon, MatTableModule, i8.MatTable, i8.MatHeaderCellDef, i8.MatHeaderRowDef, i8.MatColumnDef, i8.MatCellDef, i8.MatRowDef, i8.MatHeaderCell, i8.MatCell, i8.MatHeaderRow, i8.MatRow, MatSortModule, i9.MatSort, i9.MatSortHeader, NgClass, MatCheckboxModule, i10.MatCheckbox, MatMenuModule, i11.MatMenu, i11.MatMenuItem, i11.MatMenuTrigger, MatRippleModule, i12.MatRipple, MatProgressSpinnerModule, i13.MatProgressSpinner, MatPaginatorModule, i14.MatPaginator],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}\nexport class ProduitDataSource extends collections.DataSource {\n  get filter() {\n    return this.filterChange.value;\n  }\n  set filter(filter) {\n    this.filterChange.next(filter);\n  }\n  constructor(exampleDatabase, paginator, _sort) {\n    super();\n    this.exampleDatabase = exampleDatabase;\n    this.paginator = paginator;\n    this._sort = _sort;\n    this.filterChange = new BehaviorSubject('');\n    this.filteredData = [];\n    this.renderedData = [];\n    this.filterChange.subscribe(() => this.paginator.pageIndex = 0);\n  }\n  connect() {\n    const displayDataChanges = [this.exampleDatabase.dataChange, this._sort.sortChange, this.filterChange, this.paginator.page];\n    // Chargez les produits via l'API\n    this.exampleDatabase.getAllProduits().subscribe({\n      next: produits => {\n        this.exampleDatabase.dataChange.next(produits);\n      },\n      error: err => console.error('Error loading produits', err)\n    });\n    return merge(...displayDataChanges).pipe(map(() => {\n      this.filteredData = this.exampleDatabase.data.slice().filter(produit => {\n        const searchStr = ((produit.codeProd || '') + produit.type + produit.description + produit.prixUnitaireHT + produit.prixUnitaireTTC + produit.tva).toLowerCase();\n        return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\n      });\n      const sortedData = this.sortData(this.filteredData.slice());\n      const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\n      this.renderedData = sortedData.splice(startIndex, this.paginator.pageSize);\n      return this.renderedData;\n    }));\n  }\n  disconnect() {}\n  sortData(data) {\n    if (!this._sort.active || this._sort.direction === '') {\n      return data;\n    }\n    return data.sort((a, b) => {\n      let propertyA = '';\n      let propertyB = '';\n      switch (this._sort.active) {\n        case 'codeProd':\n          [propertyA, propertyB] = [a.codeProd || '', b.codeProd || '']; // Gestion du undefined\n          break;\n        case 'type':\n          [propertyA, propertyB] = [a.type, b.type];\n          break;\n        case 'prixUnitaireHT':\n          [propertyA, propertyB] = [a.prixUnitaireHT, b.prixUnitaireHT];\n          break;\n        case 'prixUnitaireTTC':\n          [propertyA, propertyB] = [a.prixUnitaireTTC, b.prixUnitaireTTC];\n          break;\n        case 'tva':\n          [propertyA, propertyB] = [a.tva, b.tva];\n          break;\n      }\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\n      return (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1);\n    });\n  }\n}", "map": {"version": 3, "names": ["MatPaginator", "MatPaginatorModule", "MatSort", "MatSortModule", "collections", "BehaviorSubject", "fromEvent", "merge", "map", "MatMenuTrigger", "MatMenuModule", "SelectionModel", "FormProduitComponent", "ProduitDeleteComponent", "BulkDeleteConfirmationComponent", "UnsubscribeOnDestroyAdapter", "TableExportUtil", "Ng<PERSON><PERSON>", "MatProgressSpinnerModule", "MatRippleModule", "MatCheckboxModule", "MatTableModule", "MatIconModule", "MatButtonModule", "MatTooltipModule", "BreadcrumbComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ProduitComponent_mat_header_cell_46_Template_mat_checkbox_change_1_listener", "$event", "ɵɵrestoreView", "_r23", "ctx_r22", "ɵɵnextContext", "ɵɵresetView", "masterToggle", "ɵɵelementEnd", "ɵɵproperty", "ɵɵadvance", "ctx_r1", "selection", "hasValue", "isAllSelected", "ProduitComponent_mat_cell_47_Template_mat_checkbox_click_1_listener", "stopPropagation", "ProduitComponent_mat_cell_47_Template_mat_checkbox_change_1_listener", "restoredCtx", "_r27", "row_r24", "$implicit", "ctx_r26", "toggle", "ctx_r2", "isSelected", "ɵɵtext", "ɵɵtextInterpolate1", "row_r28", "codeProd", "row_r29", "type", "row_r30", "description", "row_r31", "prixUnitaireHT", "row_r32", "prixUnitaireTTC", "row_r33", "tva", "ProduitComponent_mat_cell_68_Template_button_click_1_listener", "ProduitComponent_mat_cell_68_Template_button_click_6_listener", "_r39", "row_r34", "ctx_r38", "editCall", "ProduitComponent_mat_cell_68_Template_button_click_11_listener", "ctx_r40", "deleteItem", "_r36", "ɵɵelement", "ɵɵstyleProp", "ctx_r20", "dataSource", "renderedData", "length", "ProduitComponent", "constructor", "httpClient", "dialog", "produitService", "snackBar", "filterToggle", "displayedColumns", "contextMenuPosition", "x", "y", "ngOnInit", "loadData", "refresh", "addNew", "tempDirection", "localStorage", "getItem", "dialogRef", "open", "data", "produit", "action", "direction", "subs", "sink", "afterClosed", "subscribe", "result", "getAllProduits", "showNotification", "row", "id", "width", "max<PERSON><PERSON><PERSON>", "refreshTable", "paginator", "_changePageSize", "pageSize", "numSelected", "selected", "numRows", "clear", "for<PERSON>ach", "select", "removeSelectedRows", "totalSelect", "selectedProducts", "totalCount", "confirmed", "selectedIds", "item", "deleteSelectedProduits", "next", "index", "findIndex", "d", "exampleDatabase", "dataChange", "value", "splice", "error", "console", "ProduitDataSource", "sort", "filter", "nativeElement", "exportExcel", "exportData", "filteredData", "Type", "Description", "exportToExcel", "colorName", "text", "placementFrom", "placementAlign", "duration", "verticalPosition", "horizontalPosition", "panelClass", "_", "ɵɵdirectiveInject", "i1", "HttpClient", "i2", "MatDialog", "i3", "ProduitService", "i4", "MatSnackBar", "_2", "selectors", "viewQuery", "ProduitComponent_Query", "rf", "ctx", "ProduitComponent_Template_button_click_26_listener", "ProduitComponent_Template_button_click_31_listener", "ProduitComponent_Template_button_click_36_listener", "ProduitComponent_Template_img_click_41_listener", "ɵɵelementContainerStart", "ɵɵtemplate", "ProduitComponent_mat_header_cell_46_Template", "ProduitComponent_mat_cell_47_Template", "ɵɵelementContainerEnd", "ProduitComponent_mat_header_cell_49_Template", "ProduitComponent_mat_cell_50_Template", "ProduitComponent_mat_header_cell_52_Template", "ProduitComponent_mat_cell_53_Template", "ProduitComponent_mat_header_cell_55_Template", "ProduitComponent_mat_cell_56_Template", "ProduitComponent_mat_header_cell_58_Template", "ProduitComponent_mat_cell_59_Template", "ProduitComponent_mat_header_cell_61_Template", "ProduitComponent_mat_cell_62_Template", "ProduitComponent_mat_header_cell_64_Template", "ProduitComponent_mat_cell_65_Template", "ProduitComponent_mat_header_cell_67_Template", "ProduitComponent_mat_cell_68_Template", "ProduitComponent_mat_header_row_69_Template", "ProduitComponent_mat_row_70_Template", "ProduitComponent_Conditional_71_Template", "ProduitComponent_Conditional_72_Template", "ɵɵpureFunction0", "_c1", "ɵɵconditional", "isTblLoading", "_c2", "i5", "MatTooltip", "i6", "MatIconButton", "MatMiniFabButton", "i7", "MatIcon", "i8", "MatTable", "MatHeaderCellDef", "MatHeaderRowDef", "MatColumnDef", "MatCellDef", "MatRowDef", "MatHeaderCell", "Mat<PERSON>ell", "MatHeaderRow", "MatRow", "i9", "Mat<PERSON>ort<PERSON><PERSON>er", "i10", "MatCheckbox", "i11", "MatMenu", "MatMenuItem", "i12", "<PERSON><PERSON><PERSON><PERSON>", "i13", "MatProgressSpinner", "i14", "styles", "DataSource", "filterChange", "_sort", "pageIndex", "connect", "displayDataChanges", "sortChange", "page", "produits", "err", "pipe", "slice", "searchStr", "toLowerCase", "indexOf", "sortedData", "sortData", "startIndex", "disconnect", "active", "a", "b", "propertyA", "propertyB", "valueA", "isNaN", "valueB"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit\\produit.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit\\produit.component.html"], "sourcesContent": ["import { Component, ElementRef, OnInit, ViewChild } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { HttpClient } from '@angular/common/http';\r\nimport { MatDialog } from '@angular/material/dialog';\r\nimport { MatPaginator, MatPaginatorModule } from '@angular/material/paginator';\r\nimport { MatSort, MatSortModule } from '@angular/material/sort';\r\nimport { Produit } from '../../Model/Produit';\r\n\r\nimport * as collections from '@angular/cdk/collections';\r\nimport {\r\n  MatSnackBar,\r\n  MatSnackBarHorizontalPosition,\r\n  MatSnackBarVerticalPosition,\r\n} from '@angular/material/snack-bar';\r\nimport { BehaviorSubject, fromEvent, merge, Observable } from 'rxjs';\r\nimport { map } from 'rxjs/operators';\r\nimport { MatMenuTrigger, MatMenuModule } from '@angular/material/menu';\r\nimport { SelectionModel } from '@angular/cdk/collections';\r\nimport { FormProduitComponent } from '../form-produit/form-produit.component';\r\nimport { ProduitDeleteComponent } from '../produit-delete/produit-delete.component';\r\nimport { BulkDeleteConfirmationComponent } from '../bulk-delete-confirmation/bulk-delete-confirmation.component';\r\nimport { UnsubscribeOnDestroyAdapter } from '@shared/UnsubscribeOnDestroyAdapter';\r\nimport { Direction } from '@angular/cdk/bidi';\r\nimport { TableExportUtil, TableElement } from '@shared';\r\nimport { formatDate, NgClass, DatePipe } from '@angular/common';\r\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\r\nimport { MatRippleModule } from '@angular/material/core';\r\nimport { MatCheckboxModule } from '@angular/material/checkbox';\r\nimport { MatTableModule } from '@angular/material/table';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatTooltipModule } from '@angular/material/tooltip';\r\nimport { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';\r\n\r\n@Component({\r\n  selector: 'app-produit',\r\n  templateUrl: './produit.component.html',\r\n  styleUrls: ['./produit.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    BreadcrumbComponent,\r\n    MatTooltipModule,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatTableModule,\r\n    MatSortModule,\r\n    NgClass,\r\n    MatCheckboxModule,\r\n    MatMenuModule,\r\n    MatRippleModule,\r\n    MatProgressSpinnerModule,\r\n    MatPaginatorModule,\r\n    // DatePipe,\r\n  ],\r\n})\r\nexport class ProduitComponent\r\n  extends UnsubscribeOnDestroyAdapter\r\n  implements OnInit {\r\n  filterToggle = false;\r\n  displayedColumns = [\r\n    'select',\r\n    'codeProd',\r\n    'type',\r\n    'description',\r\n    'prixUnitaireHT',\r\n    'prixUnitaireTTC',\r\n    'tva',\r\n    'actions',\r\n  ];\r\n  exampleDatabase?: ProduitService;\r\n  dataSource!: ProduitDataSource;\r\n  selection = new SelectionModel<Produit>(true, []);\r\n  id?: string;\r\n  produit?: Produit;\r\n\r\n  constructor(\r\n    public httpClient: HttpClient,\r\n    public dialog: MatDialog,\r\n    public produitService: ProduitService,\r\n    private snackBar: MatSnackBar\r\n  ) {\r\n    super();\r\n  }\r\n\r\n  @ViewChild(MatPaginator, { static: true }) paginator!: MatPaginator;\r\n  @ViewChild(MatSort, { static: true }) sort!: MatSort;\r\n  @ViewChild('filter', { static: true }) filter!: ElementRef;\r\n  @ViewChild(MatMenuTrigger)\r\n  contextMenu?: MatMenuTrigger;\r\n  contextMenuPosition = { x: '0px', y: '0px' };\r\n\r\n  ngOnInit() {\r\n    this.loadData();\r\n  }\r\n\r\n  refresh() {\r\n    this.loadData();\r\n  }\r\n\r\n  addNew() {\r\n  let tempDirection: Direction;\r\n  if (localStorage.getItem('isRtl') === 'true') {\r\n    tempDirection = 'rtl';\r\n  } else {\r\n    tempDirection = 'ltr';\r\n  }\r\n  const dialogRef = this.dialog.open(FormProduitComponent, {\r\n    data: {\r\n      produit: this.produit,\r\n      action: 'add',\r\n    },\r\n    direction: tempDirection,\r\n  });\r\n  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\r\n    if (result) {\r\n      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\r\n      this.showNotification(\r\n        'snackbar-success',\r\n        'Produit ajouté avec succès...!!!',\r\n        'bottom',\r\n        'center'\r\n      );\r\n    }\r\n  });\r\n}\r\n\r\neditCall(row: Produit) {\r\n  this.id = row.id;\r\n  let tempDirection: Direction;\r\n  if (localStorage.getItem('isRtl') === 'true') {\r\n    tempDirection = 'rtl';\r\n  } else {\r\n    tempDirection = 'ltr';\r\n  }\r\n  const dialogRef = this.dialog.open(FormProduitComponent, {\r\n    data: {\r\n      produit: row,\r\n      action: 'edit',\r\n    },\r\n    direction: tempDirection,\r\n  });\r\n  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\r\n    if (result) {\r\n      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\r\n      this.showNotification(\r\n        'black',\r\n        'Produit modifié avec succès...!!!',\r\n        'bottom',\r\n        'center'\r\n      );\r\n    }\r\n  });\r\n}\r\n\r\ndeleteItem(row: Produit) {\r\n  this.id = row.id;\r\n  let tempDirection: Direction;\r\n  if (localStorage.getItem('isRtl') === 'true') {\r\n    tempDirection = 'rtl';\r\n  } else {\r\n    tempDirection = 'ltr';\r\n  }\r\n  const dialogRef = this.dialog.open(ProduitDeleteComponent, {\r\n    width: '500px',\r\n    maxWidth: '90vw',\r\n    data: row,\r\n    direction: tempDirection,\r\n  });\r\n  this.subs.sink = dialogRef.afterClosed().subscribe((result) => {\r\n    if (result) {\r\n      this.produitService.getAllProduits().subscribe(); // Rafraîchir les données\r\n      this.showNotification(\r\n        'snackbar-danger',\r\n        'Produit supprimé avec succès...!!!',\r\n        'bottom',\r\n        'center'\r\n      );\r\n    }\r\n  });\r\n}\r\n\r\n  private refreshTable() {\r\n    this.paginator._changePageSize(this.paginator.pageSize);\r\n  }\r\n\r\n  isAllSelected() {\r\n    const numSelected = this.selection.selected.length;\r\n    const numRows = this.dataSource.renderedData.length;\r\n    return numSelected === numRows;\r\n  }\r\n\r\n  masterToggle() {\r\n    this.isAllSelected()\r\n      ? this.selection.clear()\r\n      : this.dataSource.renderedData.forEach((row) =>\r\n          this.selection.select(row)\r\n        );\r\n  }\r\n\r\n  removeSelectedRows() {\r\n    const totalSelect = this.selection.selected.length;\r\n\r\n    if (totalSelect === 0) {\r\n      this.showNotification(\r\n        'snackbar-warning',\r\n        'Aucun produit sélectionné pour la suppression',\r\n        'bottom',\r\n        'center'\r\n      );\r\n      return;\r\n    }\r\n\r\n    // Ouvrir le dialogue de confirmation personnalisé\r\n    let tempDirection: Direction;\r\n    if (localStorage.getItem('isRtl') === 'true') {\r\n      tempDirection = 'rtl';\r\n    } else {\r\n      tempDirection = 'ltr';\r\n    }\r\n\r\n    const dialogRef = this.dialog.open(BulkDeleteConfirmationComponent, {\r\n      width: '600px',\r\n      maxWidth: '90vw',\r\n      data: {\r\n        selectedProducts: this.selection.selected,\r\n        totalCount: totalSelect\r\n      },\r\n      direction: tempDirection,\r\n    });\r\n\r\n    this.subs.sink = dialogRef.afterClosed().subscribe((confirmed) => {\r\n      if (confirmed) {\r\n        // Récupérer les IDs des produits sélectionnés\r\n        const selectedIds = this.selection.selected.map(item => item.id);\r\n\r\n        // Appeler l'API pour supprimer les produits sélectionnés\r\n        this.produitService.deleteSelectedProduits(selectedIds).subscribe({\r\n          next: () => {\r\n            // Supprimer les éléments de la vue locale après succès de l'API\r\n            this.selection.selected.forEach((item) => {\r\n              const index: number = this.dataSource.renderedData.findIndex(\r\n                (d) => d === item\r\n              );\r\n              if (index !== -1) {\r\n                this.exampleDatabase?.dataChange.value.splice(index, 1);\r\n              }\r\n            });\r\n\r\n            // Réinitialiser la sélection\r\n            this.selection = new SelectionModel<Produit>(true, []);\r\n\r\n            // Rafraîchir le tableau\r\n            this.refreshTable();\r\n\r\n            // Recharger les données depuis l'API pour s'assurer de la cohérence\r\n            this.produitService.getAllProduits().subscribe();\r\n\r\n            // Afficher la notification de succès\r\n            this.showNotification(\r\n              'snackbar-danger',\r\n              `${totalSelect} produit(s) supprimé(s) avec succès !`,\r\n              'bottom',\r\n              'center'\r\n            );\r\n          },\r\n          error: (error) => {\r\n            console.error('Erreur lors de la suppression des produits:', error);\r\n            this.showNotification(\r\n              'snackbar-danger',\r\n              'Erreur lors de la suppression des produits. Veuillez réessayer.',\r\n              'bottom',\r\n              'center'\r\n            );\r\n          }\r\n        });\r\n      }\r\n    });\r\n  }\r\n\r\n  public loadData() {\r\nthis.dataSource = new ProduitDataSource(\r\n      this.produitService,\r\n      this.paginator,\r\n      this.sort\r\n    );\r\n    \r\n    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(\r\n      () => {\r\n        if (!this.dataSource) {\r\n          return;\r\n        }\r\n        this.dataSource.filter = this.filter.nativeElement.value;\r\n      }\r\n    );\r\n  \r\n    this.subs.sink = fromEvent(this.filter.nativeElement, 'keyup').subscribe(\r\n      () => {\r\n        if (!this.dataSource) {\r\n          return;\r\n        }\r\n        this.dataSource.filter = this.filter.nativeElement.value;\r\n      }\r\n    );\r\n  }\r\n\r\n  exportExcel() {\r\n    const exportData: Partial<TableElement>[] =\r\n      this.dataSource.filteredData.map((x) => ({\r\n        'Code Produit': x.codeProd,\r\n        Type: x.type,\r\n        Description: x.description,\r\n        'Prix HT': x.prixUnitaireHT,\r\n        'Prix TTC': x.prixUnitaireTTC,\r\n        'TVA (%)': x.tva,\r\n      }));\r\n\r\n    TableExportUtil.exportToExcel(exportData, 'produits');\r\n  }\r\n\r\n  showNotification(\r\n    colorName: string,\r\n    text: string,\r\n    placementFrom: MatSnackBarVerticalPosition,\r\n    placementAlign: MatSnackBarHorizontalPosition\r\n  ) {\r\n    this.snackBar.open(text, '', {\r\n      duration: 2000,\r\n      verticalPosition: placementFrom,\r\n      horizontalPosition: placementAlign,\r\n      panelClass: colorName,\r\n    });\r\n  }\r\n}\r\n\r\nexport class ProduitDataSource extends collections.DataSource<Produit> {\r\n  filterChange = new BehaviorSubject('');\r\n  get filter(): string {\r\n    return this.filterChange.value;\r\n  }\r\n  set filter(filter: string) {\r\n    this.filterChange.next(filter);\r\n  }\r\n  filteredData: Produit[] = [];\r\n  renderedData: Produit[] = [];\r\n\r\n  constructor(\r\n    public exampleDatabase: ProduitService,\r\n    public paginator: MatPaginator,\r\n    public _sort: MatSort\r\n  ) {\r\n    super();\r\n    this.filterChange.subscribe(() => (this.paginator.pageIndex = 0));\r\n  }\r\n\r\n    connect(): Observable<Produit[]> {\r\n    const displayDataChanges = [\r\n      this.exampleDatabase.dataChange,\r\n      this._sort.sortChange,\r\n      this.filterChange,\r\n      this.paginator.page,\r\n    ];\r\n    \r\n    // Chargez les produits via l'API\r\n    this.exampleDatabase.getAllProduits().subscribe({\r\n      next: (produits) => {\r\n        this.exampleDatabase.dataChange.next(produits);\r\n      },\r\n      error: (err) => console.error('Error loading produits', err)\r\n    });\r\n    \r\n    return merge(...displayDataChanges).pipe(\r\n      map(() => {\r\n        this.filteredData = this.exampleDatabase.data\r\n          .slice()\r\n          .filter((produit: Produit) => {\r\n            const searchStr = (\r\n              (produit.codeProd || '') +\r\n              produit.type +\r\n              produit.description +\r\n              produit.prixUnitaireHT +\r\n              produit.prixUnitaireTTC +\r\n              produit.tva\r\n            ).toLowerCase();\r\n            return searchStr.indexOf(this.filter.toLowerCase()) !== -1;\r\n          });\r\n        \r\n        const sortedData = this.sortData(this.filteredData.slice());\r\n        const startIndex = this.paginator.pageIndex * this.paginator.pageSize;\r\n        this.renderedData = sortedData.splice(\r\n          startIndex,\r\n          this.paginator.pageSize\r\n        );\r\n        return this.renderedData;\r\n      })\r\n    );\r\n  }\r\n\r\n\r\n\r\n  disconnect() {}\r\n\r\n  sortData(data: Produit[]): Produit[] {\r\n    if (!this._sort.active || this._sort.direction === '') {\r\n      return data;\r\n    }\r\n    return data.sort((a, b) => {\r\n      let propertyA: number | string = '';\r\n      let propertyB: number | string = '';\r\n      switch (this._sort.active) {\r\n        case 'codeProd':\r\n          [propertyA, propertyB] = [a.codeProd || '', b.codeProd || '']; // Gestion du undefined\r\n          break;\r\n        case 'type':\r\n          [propertyA, propertyB] = [a.type, b.type];\r\n          break;\r\n        case 'prixUnitaireHT':\r\n          [propertyA, propertyB] = [a.prixUnitaireHT, b.prixUnitaireHT];\r\n          break;\r\n        case 'prixUnitaireTTC':\r\n          [propertyA, propertyB] = [a.prixUnitaireTTC, b.prixUnitaireTTC];\r\n          break;\r\n        case 'tva':\r\n          [propertyA, propertyB] = [a.tva, b.tva];\r\n          break;\r\n      }\r\n      const valueA = isNaN(+propertyA) ? propertyA : +propertyA;\r\n      const valueB = isNaN(+propertyB) ? propertyB : +propertyB;\r\n      return (\r\n        (valueA < valueB ? -1 : 1) * (this._sort.direction === 'asc' ? 1 : -1)\r\n      );\r\n    });\r\n}\r\n}", "<section class=\"content\">\r\n  <div class=\"content-block\">\r\n    <div class=\"block-header\">\r\n      <!-- breadcrumb -->\r\n      <app-breadcrumb [title]=\"'Produits'\" [items]=\"['Home']\" [active_item]=\"'Produits'\"></app-breadcrumb>\r\n    </div>\r\n    <div class=\"row\">\r\n      <div class=\"col-md-12\">\r\n        <div class=\"tabbable-line\">\r\n          <div class=\"row\">\r\n            <div class=\"col-lg-12 col-md-12 col-sm-12 col-xs-12\">\r\n              <div class=\"card\">\r\n                <div class=\"materialTableHeader\">\r\n                  <div class=\"left\">\r\n                    <ul class=\"header-buttons-left ms-0\">\r\n                      <li class=\"tbl-title\">\r\n                        <h2>Produits</h2>\r\n                      </li>\r\n                      <li class=\"tbl-search-box\">\r\n                        <label for=\"search-input\"><i class=\"material-icons search-icon\">search</i></label>\r\n                        <input placeholder=\"Rechercher\" type=\"text\" #filter class=\"browser-default search-field\"\r\n                          aria-label=\"Search box\">\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                  <div class=\"right\">\r\n                    <ul class=\"tbl-export-btn\">\r\n                      <li class=\"tbl-header-btn\">\r\n                        <div class=\"m-l-10\" matTooltip=\"AJOUTER\">\r\n                          <button mat-mini-fab color=\"primary\" (click)=\"addNew()\">\r\n                            <mat-icon class=\"col-white\">add</mat-icon>\r\n                          </button>\r\n                        </div>\r\n                      </li>\r\n                      <li class=\"tbl-header-btn\">\r\n                        <div class=\"m-l-10\" matTooltip=\"RAFRAÎCHIR\">\r\n                          <button mat-mini-fab color=\"primary\" (click)=\"refresh()\">\r\n                            <mat-icon class=\"col-white\">refresh</mat-icon>\r\n                          </button>\r\n                        </div>\r\n                      </li>\r\n                      <li class=\"tbl-header-btn\">\r\n                        <div class=\"m-l-10\" [hidden]=!selection.hasValue() matTooltip=\"SUPPRIMER\">\r\n                          <button mat-mini-fab color=\"warn\" (click)=\"removeSelectedRows()\">\r\n                            <mat-icon class=\"col-white\">delete</mat-icon>\r\n                          </button>\r\n                        </div>\r\n                      </li>\r\n                      <li>\r\n                        <div class=\"export-button m-l-10\" matTooltip=\"EXPORT XLSX\">\r\n                          <img src=\"assets/images/icons/xlsx.png\" alt=\"\" (click)=\"exportExcel()\" />\r\n                        </div>\r\n                      </li>\r\n                    </ul>\r\n                  </div>\r\n                </div>\r\n                <div class=\"body overflow-auto\">\r\n                  <div class=\"responsive_table\">\r\n                    <table mat-table [dataSource]=\"dataSource\" matSort class=\"mat-cell advance-table\">\r\n                      <!-- Checkbox Column -->\r\n                      <ng-container matColumnDef=\"select\">\r\n                        <mat-header-cell *matHeaderCellDef [ngClass]=\"'tbl-col-width-per-6'\">\r\n                          <mat-checkbox (change)=\"$event ? masterToggle() : null\"\r\n                            [checked]=\"selection.hasValue() && isAllSelected()\"\r\n                            [indeterminate]=\"selection.hasValue() && !isAllSelected()\" [ngClass]=\"'tbl-checkbox'\">\r\n                          </mat-checkbox>\r\n                        </mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\" [ngClass]=\"'tbl-col-width-per-6'\">\r\n                          <mat-checkbox (click)=\"$event.stopPropagation()\"\r\n                            (change)=\"$event ? selection.toggle(row) : null\" [checked]=\"selection.isSelected(row)\"\r\n                            [ngClass]=\"'tbl-checkbox'\">\r\n                          </mat-checkbox>\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Code Produit Column -->\r\n                      <ng-container matColumnDef=\"codeProd\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>Code Produit</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\">\r\n                          <span class=\"mobile-label\">Code:</span> {{row.codeProd || '-'}}\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Type Column -->\r\n                      <ng-container matColumnDef=\"type\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>Type</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\">\r\n                          <span class=\"mobile-label\">Type:</span> {{row.type}}\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Description Column -->\r\n                      <ng-container matColumnDef=\"description\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>Description</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\" class=\"column-nowrap\">\r\n                          <span class=\"mobile-label\">Description:</span> {{row.description}}\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Prix HT Column -->\r\n                      <ng-container matColumnDef=\"prixUnitaireHT\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>Prix HT</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\">\r\n                          <span class=\"mobile-label\">Prix HT:</span> {{row.prixUnitaireHT }}\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Prix TTC Column -->\r\n                      <ng-container matColumnDef=\"prixUnitaireTTC\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>Prix TTC</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\">\r\n                          <span class=\"mobile-label\">Prix TTC:</span> {{row.prixUnitaireTTC }}\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- TVA Column -->\r\n                      <ng-container matColumnDef=\"tva\">\r\n                        <mat-header-cell *matHeaderCellDef mat-sort-header>TVA (%)</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row\">\r\n                          <span class=\"mobile-label\">TVA:</span> {{row.tva }}%\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <!-- Actions Column -->\r\n                      <ng-container matColumnDef=\"actions\">\r\n                        <mat-header-cell class=\"psl-3 tbl-col-width-per-10\" *matHeaderCellDef>Actions</mat-header-cell>\r\n                        <mat-cell *matCellDef=\"let row; let i=index;\" class=\"psl-3 tbl-col-width-per-10\">\r\n                          <button mat-icon-button [matMenuTriggerFor]=\"menu\" (click)=\"$event.stopPropagation()\">\r\n                            <mat-icon>more_vert</mat-icon>\r\n                          </button>\r\n                          <mat-menu #menu=\"matMenu\">\r\n                            <button mat-menu-item (click)=\"editCall(row)\">\r\n                              <i class=\"material-icons-outlined align-middle msr-2\">edit</i>\r\n                              <span>Modifier</span>\r\n                            </button>\r\n                            <button mat-menu-item (click)=\"deleteItem(row)\">\r\n                              <i class=\"material-icons-outlined align-middle msr-2\">delete_forever</i>\r\n                              <span>Supprimer</span>\r\n                            </button>\r\n                          </mat-menu>\r\n                        </mat-cell>\r\n                      </ng-container>\r\n\r\n                      <mat-header-row *matHeaderRowDef=\"displayedColumns\"></mat-header-row>\r\n                      <mat-row *matRowDef=\"let row; columns: displayedColumns;\" matRipple>\r\n                      </mat-row>\r\n                    </table>\r\n\r\n                    <!-- Loading spinner -->\r\n                    @if (exampleDatabase?.isTblLoading) {\r\n                    <div class=\"tbl-spinner\">\r\n                      <mat-progress-spinner color=\"primary\" [diameter]=\"40\" mode=\"indeterminate\">\r\n                      </mat-progress-spinner>\r\n                    </div>\r\n                    }\r\n                    @if (!exampleDatabase?.isTblLoading) {\r\n                    <div class=\"no-results\" [style.display]=\"dataSource.renderedData.length === 0 ? '' : 'none'\">\r\n                      Aucun résultat trouvé\r\n                    </div>\r\n                    }\r\n                    <mat-paginator #paginator [length]=\"dataSource.filteredData.length\" [pageIndex]=\"0\" [pageSize]=\"10\"\r\n                      [pageSizeOptions]=\"[5, 10, 25, 100]\">\r\n                    </mat-paginator>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</section>"], "mappings": "AAIA,SAASA,YAAY,EAAEC,kBAAkB,QAAQ,6BAA6B;AAC9E,SAASC,OAAO,EAAEC,aAAa,QAAQ,wBAAwB;AAG/D,OAAO,KAAKC,WAAW,MAAM,0BAA0B;AAMvD,SAASC,eAAe,EAAEC,SAAS,EAAEC,KAAK,QAAoB,MAAM;AACpE,SAASC,GAAG,QAAQ,gBAAgB;AACpC,SAASC,cAAc,EAAEC,aAAa,QAAQ,wBAAwB;AACtE,SAASC,cAAc,QAAQ,0BAA0B;AACzD,SAASC,oBAAoB,QAAQ,wCAAwC;AAC7E,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,+BAA+B,QAAQ,gEAAgE;AAChH,SAASC,2BAA2B,QAAQ,qCAAqC;AAEjF,SAASC,eAAe,QAAsB,SAAS;AACvD,SAAqBC,OAAO,QAAkB,iBAAiB;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,mBAAmB,QAAQ,oDAAoD;;;;;;;;;;;;;;;;;;;;IC6BhEC,EAAA,CAAAC,cAAA,0BAAqE;IACrDD,EAAA,CAAAE,UAAA,oBAAAC,4EAAAC,MAAA;MAAAJ,EAAA,CAAAK,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAP,EAAA,CAAAQ,aAAA;MAAA,OAAUR,EAAA,CAAAS,WAAA,CAAAL,MAAA,GAASG,OAAA,CAAAG,YAAA,EAAc,GAAG,IAAI;IAAA,EAAC;IAGvDV,EAAA,CAAAW,YAAA,EAAe;;;;IAJkBX,EAAA,CAAAY,UAAA,kCAAiC;IAEhEZ,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAAY,UAAA,YAAAE,MAAA,CAAAC,SAAA,CAAAC,QAAA,MAAAF,MAAA,CAAAG,aAAA,GAAmD,kBAAAH,MAAA,CAAAC,SAAA,CAAAC,QAAA,OAAAF,MAAA,CAAAG,aAAA;;;;;;IAIvDjB,EAAA,CAAAC,cAAA,mBAAkE;IAClDD,EAAA,CAAAE,UAAA,mBAAAgB,oEAAAd,MAAA;MAAA,OAASA,MAAA,CAAAe,eAAA,EAAwB;IAAA,EAAC,oBAAAC,qEAAAhB,MAAA;MAAA,MAAAiB,WAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAiB,IAAA;MAAA,MAAAC,OAAA,GAAAF,WAAA,CAAAG,SAAA;MAAA,MAAAC,OAAA,GAAAzB,EAAA,CAAAQ,aAAA;MAAA,OACpCR,EAAA,CAAAS,WAAA,CAAAL,MAAA,GAASqB,OAAA,CAAAV,SAAA,CAAAW,MAAA,CAAAH,OAAA,CAAqB,GAAG,IAAI;IAAA,EADD;IAGhDvB,EAAA,CAAAW,YAAA,EAAe;;;;;IAJeX,EAAA,CAAAY,UAAA,kCAAiC;IAEZZ,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAY,UAAA,YAAAe,MAAA,CAAAZ,SAAA,CAAAa,UAAA,CAAAL,OAAA,EAAqC;;;;;IAQ1FvB,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,mBAAY;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IACjFX,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAA6B,MAAA,YAAK;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GAC1C;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IAD+BX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAA8B,kBAAA,MAAAC,OAAA,CAAAC,QAAA,aAC1C;;;;;IAKAhC,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,WAAI;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IACzEX,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAA6B,MAAA,YAAK;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GAC1C;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IAD+BX,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAA8B,kBAAA,MAAAG,OAAA,CAAAC,IAAA,MAC1C;;;;;IAKAlC,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,kBAAW;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IAChFX,EAAA,CAAAC,cAAA,mBAAsD;IACzBD,EAAA,CAAA6B,MAAA,mBAAY;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GACjD;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IADsCX,EAAA,CAAAa,SAAA,GACjD;IADiDb,EAAA,CAAA8B,kBAAA,MAAAK,OAAA,CAAAC,WAAA,MACjD;;;;;IAKApC,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,cAAO;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC5EX,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAA6B,MAAA,eAAQ;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GAC7C;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IADkCX,EAAA,CAAAa,SAAA,GAC7C;IAD6Cb,EAAA,CAAA8B,kBAAA,MAAAO,OAAA,CAAAC,cAAA,MAC7C;;;;;IAKAtC,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,eAAQ;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC7EX,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAA6B,MAAA,gBAAS;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GAC9C;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IADmCX,EAAA,CAAAa,SAAA,GAC9C;IAD8Cb,EAAA,CAAA8B,kBAAA,MAAAS,OAAA,CAAAC,eAAA,MAC9C;;;;;IAKAxC,EAAA,CAAAC,cAAA,0BAAmD;IAAAD,EAAA,CAAA6B,MAAA,cAAO;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;IAC5EX,EAAA,CAAAC,cAAA,eAAgC;IACHD,EAAA,CAAA6B,MAAA,WAAI;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAACX,EAAA,CAAA6B,MAAA,GACzC;IAAA7B,EAAA,CAAAW,YAAA,EAAW;;;;IAD8BX,EAAA,CAAAa,SAAA,GACzC;IADyCb,EAAA,CAAA8B,kBAAA,MAAAW,OAAA,CAAAC,GAAA,OACzC;;;;;IAKA1C,EAAA,CAAAC,cAAA,0BAAsE;IAAAD,EAAA,CAAA6B,MAAA,cAAO;IAAA7B,EAAA,CAAAW,YAAA,EAAkB;;;;;;IAC/FX,EAAA,CAAAC,cAAA,mBAAiF;IAC5BD,EAAA,CAAAE,UAAA,mBAAAyC,8DAAAvC,MAAA;MAAA,OAASA,MAAA,CAAAe,eAAA,EAAwB;IAAA,EAAC;IACnFnB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAA6B,MAAA,gBAAS;IAAA7B,EAAA,CAAAW,YAAA,EAAW;IAEhCX,EAAA,CAAAC,cAAA,yBAA0B;IACFD,EAAA,CAAAE,UAAA,mBAAA0C,8DAAA;MAAA,MAAAvB,WAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAzB,WAAA,CAAAG,SAAA;MAAA,MAAAuB,OAAA,GAAA/C,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAsC,OAAA,CAAAC,QAAA,CAAAF,OAAA,CAAa;IAAA,EAAC;IAC3C9C,EAAA,CAAAC,cAAA,YAAsD;IAAAD,EAAA,CAAA6B,MAAA,WAAI;IAAA7B,EAAA,CAAAW,YAAA,EAAI;IAC9DX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAA6B,MAAA,gBAAQ;IAAA7B,EAAA,CAAAW,YAAA,EAAO;IAEvBX,EAAA,CAAAC,cAAA,kBAAgD;IAA1BD,EAAA,CAAAE,UAAA,mBAAA+C,+DAAA;MAAA,MAAA5B,WAAA,GAAArB,EAAA,CAAAK,aAAA,CAAAwC,IAAA;MAAA,MAAAC,OAAA,GAAAzB,WAAA,CAAAG,SAAA;MAAA,MAAA0B,OAAA,GAAAlD,EAAA,CAAAQ,aAAA;MAAA,OAASR,EAAA,CAAAS,WAAA,CAAAyC,OAAA,CAAAC,UAAA,CAAAL,OAAA,CAAe;IAAA,EAAC;IAC7C9C,EAAA,CAAAC,cAAA,aAAsD;IAAAD,EAAA,CAAA6B,MAAA,sBAAc;IAAA7B,EAAA,CAAAW,YAAA,EAAI;IACxEX,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAA6B,MAAA,iBAAS;IAAA7B,EAAA,CAAAW,YAAA,EAAO;;;;IAVFX,EAAA,CAAAa,SAAA,EAA0B;IAA1Bb,EAAA,CAAAY,UAAA,sBAAAwC,IAAA,CAA0B;;;;;IAgBtDpD,EAAA,CAAAqD,SAAA,qBAAqE;;;;;IACrErD,EAAA,CAAAqD,SAAA,kBACU;;;;;IAKZrD,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAqD,SAAA,+BACuB;IACzBrD,EAAA,CAAAW,YAAA,EAAM;;;IAFkCX,EAAA,CAAAa,SAAA,EAAe;IAAfb,EAAA,CAAAY,UAAA,gBAAe;;;;;IAKvDZ,EAAA,CAAAC,cAAA,cAA6F;IAC3FD,EAAA,CAAA6B,MAAA,wCACF;IAAA7B,EAAA,CAAAW,YAAA,EAAM;;;;IAFkBX,EAAA,CAAAsD,WAAA,YAAAC,OAAA,CAAAC,UAAA,CAAAC,YAAA,CAAAC,MAAA,qBAAoE;;;;;ADrGhH,OAAM,MAAOC,gBACX,SAAQtE,2BAA2B;EAmBnCuE,YACSC,UAAsB,EACtBC,MAAiB,EACjBC,cAA8B,EAC7BC,QAAqB;IAE7B,KAAK,EAAE;IALA,KAAAH,UAAU,GAAVA,UAAU;IACV,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,QAAQ,GAARA,QAAQ;IArBlB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,gBAAgB,GAAG,CACjB,QAAQ,EACR,UAAU,EACV,MAAM,EACN,aAAa,EACb,gBAAgB,EAChB,iBAAiB,EACjB,KAAK,EACL,SAAS,CACV;IAGD,KAAAnD,SAAS,GAAG,IAAI9B,cAAc,CAAU,IAAI,EAAE,EAAE,CAAC;IAkBjD,KAAAkF,mBAAmB,GAAG;MAAEC,CAAC,EAAE,KAAK;MAAEC,CAAC,EAAE;IAAK,CAAE;EAP5C;EASAC,QAAQA,CAAA;IACN,IAAI,CAACC,QAAQ,EAAE;EACjB;EAEAC,OAAOA,CAAA;IACL,IAAI,CAACD,QAAQ,EAAE;EACjB;EAEAE,MAAMA,CAAA;IACN,IAAIC,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC5F,oBAAoB,EAAE;MACvD6F,IAAI,EAAE;QACJC,OAAO,EAAE,IAAI,CAACA,OAAO;QACrBC,MAAM,EAAE;OACT;MACDC,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,EAAE;QACV,IAAI,CAACxB,cAAc,CAACyB,cAAc,EAAE,CAACF,SAAS,EAAE,CAAC,CAAC;QAClD,IAAI,CAACG,gBAAgB,CACnB,kBAAkB,EAClB,kCAAkC,EAClC,QAAQ,EACR,QAAQ,CACT;;IAEL,CAAC,CAAC;EACJ;EAEAzC,QAAQA,CAAC0C,GAAY;IACnB,IAAI,CAACC,EAAE,GAAGD,GAAG,CAACC,EAAE;IAChB,IAAIjB,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC5F,oBAAoB,EAAE;MACvD6F,IAAI,EAAE;QACJC,OAAO,EAAEU,GAAG;QACZT,MAAM,EAAE;OACT;MACDC,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,EAAE;QACV,IAAI,CAACxB,cAAc,CAACyB,cAAc,EAAE,CAACF,SAAS,EAAE,CAAC,CAAC;QAClD,IAAI,CAACG,gBAAgB,CACnB,OAAO,EACP,mCAAmC,EACnC,QAAQ,EACR,QAAQ,CACT;;IAEL,CAAC,CAAC;EACJ;EAEAtC,UAAUA,CAACuC,GAAY;IACrB,IAAI,CAACC,EAAE,GAAGD,GAAG,CAACC,EAAE;IAChB,IAAIjB,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAEvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC3F,sBAAsB,EAAE;MACzDyG,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBd,IAAI,EAAEW,GAAG;MACTR,SAAS,EAAER;KACZ,CAAC;IACF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEC,MAAM,IAAI;MAC5D,IAAIA,MAAM,EAAE;QACV,IAAI,CAACxB,cAAc,CAACyB,cAAc,EAAE,CAACF,SAAS,EAAE,CAAC,CAAC;QAClD,IAAI,CAACG,gBAAgB,CACnB,iBAAiB,EACjB,oCAAoC,EACpC,QAAQ,EACR,QAAQ,CACT;;IAEL,CAAC,CAAC;EACJ;EAEUK,YAAYA,CAAA;IAClB,IAAI,CAACC,SAAS,CAACC,eAAe,CAAC,IAAI,CAACD,SAAS,CAACE,QAAQ,CAAC;EACzD;EAEAhF,aAAaA,CAAA;IACX,MAAMiF,WAAW,GAAG,IAAI,CAACnF,SAAS,CAACoF,QAAQ,CAACzC,MAAM;IAClD,MAAM0C,OAAO,GAAG,IAAI,CAAC5C,UAAU,CAACC,YAAY,CAACC,MAAM;IACnD,OAAOwC,WAAW,KAAKE,OAAO;EAChC;EAEA1F,YAAYA,CAAA;IACV,IAAI,CAACO,aAAa,EAAE,GAChB,IAAI,CAACF,SAAS,CAACsF,KAAK,EAAE,GACtB,IAAI,CAAC7C,UAAU,CAACC,YAAY,CAAC6C,OAAO,CAAEZ,GAAG,IACvC,IAAI,CAAC3E,SAAS,CAACwF,MAAM,CAACb,GAAG,CAAC,CAC3B;EACP;EAEAc,kBAAkBA,CAAA;IAChB,MAAMC,WAAW,GAAG,IAAI,CAAC1F,SAAS,CAACoF,QAAQ,CAACzC,MAAM;IAElD,IAAI+C,WAAW,KAAK,CAAC,EAAE;MACrB,IAAI,CAAChB,gBAAgB,CACnB,kBAAkB,EAClB,+CAA+C,EAC/C,QAAQ,EACR,QAAQ,CACT;MACD;;IAGF;IACA,IAAIf,aAAwB;IAC5B,IAAIC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MAC5CF,aAAa,GAAG,KAAK;KACtB,MAAM;MACLA,aAAa,GAAG,KAAK;;IAGvB,MAAMG,SAAS,GAAG,IAAI,CAACf,MAAM,CAACgB,IAAI,CAAC1F,+BAA+B,EAAE;MAClEwG,KAAK,EAAE,OAAO;MACdC,QAAQ,EAAE,MAAM;MAChBd,IAAI,EAAE;QACJ2B,gBAAgB,EAAE,IAAI,CAAC3F,SAAS,CAACoF,QAAQ;QACzCQ,UAAU,EAAEF;OACb;MACDvB,SAAS,EAAER;KACZ,CAAC;IAEF,IAAI,CAACS,IAAI,CAACC,IAAI,GAAGP,SAAS,CAACQ,WAAW,EAAE,CAACC,SAAS,CAAEsB,SAAS,IAAI;MAC/D,IAAIA,SAAS,EAAE;QACb;QACA,MAAMC,WAAW,GAAG,IAAI,CAAC9F,SAAS,CAACoF,QAAQ,CAACrH,GAAG,CAACgI,IAAI,IAAIA,IAAI,CAACnB,EAAE,CAAC;QAEhE;QACA,IAAI,CAAC5B,cAAc,CAACgD,sBAAsB,CAACF,WAAW,CAAC,CAACvB,SAAS,CAAC;UAChE0B,IAAI,EAAEA,CAAA,KAAK;YACT;YACA,IAAI,CAACjG,SAAS,CAACoF,QAAQ,CAACG,OAAO,CAAEQ,IAAI,IAAI;cACvC,MAAMG,KAAK,GAAW,IAAI,CAACzD,UAAU,CAACC,YAAY,CAACyD,SAAS,CACzDC,CAAC,IAAKA,CAAC,KAAKL,IAAI,CAClB;cACD,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;gBAChB,IAAI,CAACG,eAAe,EAAEC,UAAU,CAACC,KAAK,CAACC,MAAM,CAACN,KAAK,EAAE,CAAC,CAAC;;YAE3D,CAAC,CAAC;YAEF;YACA,IAAI,CAAClG,SAAS,GAAG,IAAI9B,cAAc,CAAU,IAAI,EAAE,EAAE,CAAC;YAEtD;YACA,IAAI,CAAC6G,YAAY,EAAE;YAEnB;YACA,IAAI,CAAC/B,cAAc,CAACyB,cAAc,EAAE,CAACF,SAAS,EAAE;YAEhD;YACA,IAAI,CAACG,gBAAgB,CACnB,iBAAiB,EACjB,GAAGgB,WAAW,uCAAuC,EACrD,QAAQ,EACR,QAAQ,CACT;UACH,CAAC;UACDe,KAAK,EAAGA,KAAK,IAAI;YACfC,OAAO,CAACD,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;YACnE,IAAI,CAAC/B,gBAAgB,CACnB,iBAAiB,EACjB,iEAAiE,EACjE,QAAQ,EACR,QAAQ,CACT;UACH;SACD,CAAC;;IAEN,CAAC,CAAC;EACJ;EAEOlB,QAAQA,CAAA;IACjB,IAAI,CAACf,UAAU,GAAG,IAAIkE,iBAAiB,CACjC,IAAI,CAAC3D,cAAc,EACnB,IAAI,CAACgC,SAAS,EACd,IAAI,CAAC4B,IAAI,CACV;IAED,IAAI,CAACxC,IAAI,CAACC,IAAI,GAAGxG,SAAS,CAAC,IAAI,CAACgJ,MAAM,CAACC,aAAa,EAAE,OAAO,CAAC,CAACvC,SAAS,CACtE,MAAK;MACH,IAAI,CAAC,IAAI,CAAC9B,UAAU,EAAE;QACpB;;MAEF,IAAI,CAACA,UAAU,CAACoE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,aAAa,CAACP,KAAK;IAC1D,CAAC,CACF;IAED,IAAI,CAACnC,IAAI,CAACC,IAAI,GAAGxG,SAAS,CAAC,IAAI,CAACgJ,MAAM,CAACC,aAAa,EAAE,OAAO,CAAC,CAACvC,SAAS,CACtE,MAAK;MACH,IAAI,CAAC,IAAI,CAAC9B,UAAU,EAAE;QACpB;;MAEF,IAAI,CAACA,UAAU,CAACoE,MAAM,GAAG,IAAI,CAACA,MAAM,CAACC,aAAa,CAACP,KAAK;IAC1D,CAAC,CACF;EACH;EAEAQ,WAAWA,CAAA;IACT,MAAMC,UAAU,GACd,IAAI,CAACvE,UAAU,CAACwE,YAAY,CAAClJ,GAAG,CAAEsF,CAAC,KAAM;MACvC,cAAc,EAAEA,CAAC,CAACpC,QAAQ;MAC1BiG,IAAI,EAAE7D,CAAC,CAAClC,IAAI;MACZgG,WAAW,EAAE9D,CAAC,CAAChC,WAAW;MAC1B,SAAS,EAAEgC,CAAC,CAAC9B,cAAc;MAC3B,UAAU,EAAE8B,CAAC,CAAC5B,eAAe;MAC7B,SAAS,EAAE4B,CAAC,CAAC1B;KACd,CAAC,CAAC;IAELpD,eAAe,CAAC6I,aAAa,CAACJ,UAAU,EAAE,UAAU,CAAC;EACvD;EAEAtC,gBAAgBA,CACd2C,SAAiB,EACjBC,IAAY,EACZC,aAA0C,EAC1CC,cAA6C;IAE7C,IAAI,CAACvE,QAAQ,CAACc,IAAI,CAACuD,IAAI,EAAE,EAAE,EAAE;MAC3BG,QAAQ,EAAE,IAAI;MACdC,gBAAgB,EAAEH,aAAa;MAC/BI,kBAAkB,EAAEH,cAAc;MAClCI,UAAU,EAAEP;KACb,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBApRUjF,gBAAgB,EAAA3D,EAAA,CAAA6I,iBAAA,CAAAC,EAAA,CAAAC,UAAA,GAAA/I,EAAA,CAAA6I,iBAAA,CAAAG,EAAA,CAAAC,SAAA,GAAAjJ,EAAA,CAAA6I,iBAAA,CAAAK,EAAA,CAAAC,cAAA,GAAAnJ,EAAA,CAAA6I,iBAAA,CAAAO,EAAA,CAAAC,WAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAhB3F,gBAAgB;IAAA4F,SAAA;IAAAC,SAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBA6BhBpL,YAAY;uBACZE,OAAO;;uBAEPO,cAAc;;;;;;;;;;;;;;;;;QCvF3BiB,EAAA,CAAAC,cAAA,iBAAyB;QAInBD,EAAA,CAAAqD,SAAA,wBAAoG;QACtGrD,EAAA,CAAAW,YAAA,EAAM;QACNX,EAAA,CAAAC,cAAA,aAAiB;QAUOD,EAAA,CAAA6B,MAAA,gBAAQ;QAAA7B,EAAA,CAAAW,YAAA,EAAK;QAEnBX,EAAA,CAAAC,cAAA,cAA2B;QACuCD,EAAA,CAAA6B,MAAA,cAAM;QAAA7B,EAAA,CAAAW,YAAA,EAAI;QAC1EX,EAAA,CAAAqD,SAAA,qBAC0B;QAC5BrD,EAAA,CAAAW,YAAA,EAAK;QAGTX,EAAA,CAAAC,cAAA,eAAmB;QAI0BD,EAAA,CAAAE,UAAA,mBAAA0J,mDAAA;UAAA,OAASD,GAAA,CAAAlF,MAAA,EAAQ;QAAA,EAAC;QACrDzE,EAAA,CAAAC,cAAA,oBAA4B;QAAAD,EAAA,CAAA6B,MAAA,WAAG;QAAA7B,EAAA,CAAAW,YAAA,EAAW;QAIhDX,EAAA,CAAAC,cAAA,cAA2B;QAEcD,EAAA,CAAAE,UAAA,mBAAA2J,mDAAA;UAAA,OAASF,GAAA,CAAAnF,OAAA,EAAS;QAAA,EAAC;QACtDxE,EAAA,CAAAC,cAAA,oBAA4B;QAAAD,EAAA,CAAA6B,MAAA,eAAO;QAAA7B,EAAA,CAAAW,YAAA,EAAW;QAIpDX,EAAA,CAAAC,cAAA,cAA2B;QAEWD,EAAA,CAAAE,UAAA,mBAAA4J,mDAAA;UAAA,OAASH,GAAA,CAAAnD,kBAAA,EAAoB;QAAA,EAAC;QAC9DxG,EAAA,CAAAC,cAAA,oBAA4B;QAAAD,EAAA,CAAA6B,MAAA,cAAM;QAAA7B,EAAA,CAAAW,YAAA,EAAW;QAInDX,EAAA,CAAAC,cAAA,UAAI;QAE+CD,EAAA,CAAAE,UAAA,mBAAA6J,gDAAA;UAAA,OAASJ,GAAA,CAAA7B,WAAA,EAAa;QAAA,EAAC;QAAtE9H,EAAA,CAAAW,YAAA,EAAyE;QAMnFX,EAAA,CAAAC,cAAA,eAAgC;QAI1BD,EAAA,CAAAgK,uBAAA,QAAoC;QAClChK,EAAA,CAAAiK,UAAA,KAAAC,4CAAA,8BAKkB,KAAAC,qCAAA;QAOpBnK,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAAsC;QACpChK,EAAA,CAAAiK,UAAA,KAAAI,4CAAA,8BAAiF,KAAAC,qCAAA;QAInFtK,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAAkC;QAChChK,EAAA,CAAAiK,UAAA,KAAAM,4CAAA,8BAAyE,KAAAC,qCAAA;QAI3ExK,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAAyC;QACvChK,EAAA,CAAAiK,UAAA,KAAAQ,4CAAA,8BAAgF,KAAAC,qCAAA;QAIlF1K,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAA4C;QAC1ChK,EAAA,CAAAiK,UAAA,KAAAU,4CAAA,8BAA4E,KAAAC,qCAAA;QAI9E5K,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAA6C;QAC3ChK,EAAA,CAAAiK,UAAA,KAAAY,4CAAA,8BAA6E,KAAAC,qCAAA;QAI/E9K,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAAiC;QAC/BhK,EAAA,CAAAiK,UAAA,KAAAc,4CAAA,8BAA4E,KAAAC,qCAAA;QAI9EhL,EAAA,CAAAoK,qBAAA,EAAe;QAGfpK,EAAA,CAAAgK,uBAAA,QAAqC;QACnChK,EAAA,CAAAiK,UAAA,KAAAgB,4CAAA,8BAA+F,KAAAC,qCAAA;QAgBjGlL,EAAA,CAAAoK,qBAAA,EAAe;QAEfpK,EAAA,CAAAiK,UAAA,KAAAkB,2CAAA,6BAAqE,KAAAC,oCAAA;QAGvEpL,EAAA,CAAAW,YAAA,EAAQ;QAGRX,EAAA,CAAAiK,UAAA,KAAAoB,wCAAA,kBAKC,KAAAC,wCAAA;QAMDtL,EAAA,CAAAqD,SAAA,6BAEgB;QAClBrD,EAAA,CAAAW,YAAA,EAAM;;;QA/JFX,EAAA,CAAAa,SAAA,GAAoB;QAApBb,EAAA,CAAAY,UAAA,qBAAoB,UAAAZ,EAAA,CAAAuL,eAAA,KAAAC,GAAA;QAsCExL,EAAA,CAAAa,SAAA,IAA8B;QAA9Bb,EAAA,CAAAY,UAAA,YAAA+I,GAAA,CAAA5I,SAAA,CAAAC,QAAA,GAA8B;QAgBrChB,EAAA,CAAAa,SAAA,GAAyB;QAAzBb,EAAA,CAAAY,UAAA,eAAA+I,GAAA,CAAAnG,UAAA,CAAyB;QAqFvBxD,EAAA,CAAAa,SAAA,IAAiC;QAAjCb,EAAA,CAAAY,UAAA,oBAAA+I,GAAA,CAAAzF,gBAAA,CAAiC;QACpBlE,EAAA,CAAAa,SAAA,EAA0B;QAA1Bb,EAAA,CAAAY,UAAA,qBAAA+I,GAAA,CAAAzF,gBAAA,CAA0B;QAK1DlE,EAAA,CAAAa,SAAA,EAKC;QALDb,EAAA,CAAAyL,aAAA,MAAA9B,GAAA,CAAAvC,eAAA,kBAAAuC,GAAA,CAAAvC,eAAA,CAAAsE,YAAA,YAKC;QACD1L,EAAA,CAAAa,SAAA,EAIC;QAJDb,EAAA,CAAAyL,aAAA,OAAA9B,GAAA,CAAAvC,eAAA,kBAAAuC,GAAA,CAAAvC,eAAA,CAAAsE,YAAA,YAIC;QACyB1L,EAAA,CAAAa,SAAA,EAAyC;QAAzCb,EAAA,CAAAY,UAAA,WAAA+I,GAAA,CAAAnG,UAAA,CAAAwE,YAAA,CAAAtE,MAAA,CAAyC,oDAAA1D,EAAA,CAAAuL,eAAA,KAAAI,GAAA;;;mBDxHnF5L,mBAAmB,EACnBD,gBAAgB,EAAA8L,EAAA,CAAAC,UAAA,EAChBhM,eAAe,EAAAiM,EAAA,CAAAC,aAAA,EAAAD,EAAA,CAAAE,gBAAA,EACfpM,aAAa,EAAAqM,EAAA,CAAAC,OAAA,EACbvM,cAAc,EAAAwM,EAAA,CAAAC,QAAA,EAAAD,EAAA,CAAAE,gBAAA,EAAAF,EAAA,CAAAG,eAAA,EAAAH,EAAA,CAAAI,YAAA,EAAAJ,EAAA,CAAAK,UAAA,EAAAL,EAAA,CAAAM,SAAA,EAAAN,EAAA,CAAAO,aAAA,EAAAP,EAAA,CAAAQ,OAAA,EAAAR,EAAA,CAAAS,YAAA,EAAAT,EAAA,CAAAU,MAAA,EACdpO,aAAa,EAAAqO,EAAA,CAAAtO,OAAA,EAAAsO,EAAA,CAAAC,aAAA,EACbxN,OAAO,EACPG,iBAAiB,EAAAsN,GAAA,CAAAC,WAAA,EACjBjO,aAAa,EAAAkO,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,WAAA,EAAAF,GAAA,CAAAnO,cAAA,EACbU,eAAe,EAAA4N,GAAA,CAAAC,SAAA,EACf9N,wBAAwB,EAAA+N,GAAA,CAAAC,kBAAA,EACxBjP,kBAAkB,EAAAkP,GAAA,CAAAnP,YAAA;IAAAoP,MAAA;EAAA;;AA2RtB,OAAM,MAAOhG,iBAAkB,SAAQhJ,WAAW,CAACiP,UAAmB;EAEpE,IAAI/F,MAAMA,CAAA;IACR,OAAO,IAAI,CAACgG,YAAY,CAACtG,KAAK;EAChC;EACA,IAAIM,MAAMA,CAACA,MAAc;IACvB,IAAI,CAACgG,YAAY,CAAC5G,IAAI,CAACY,MAAM,CAAC;EAChC;EAIAhE,YACSwD,eAA+B,EAC/BrB,SAAuB,EACvB8H,KAAc;IAErB,KAAK,EAAE;IAJA,KAAAzG,eAAe,GAAfA,eAAe;IACf,KAAArB,SAAS,GAATA,SAAS;IACT,KAAA8H,KAAK,GAALA,KAAK;IAbd,KAAAD,YAAY,GAAG,IAAIjP,eAAe,CAAC,EAAE,CAAC;IAOtC,KAAAqJ,YAAY,GAAc,EAAE;IAC5B,KAAAvE,YAAY,GAAc,EAAE;IAQ1B,IAAI,CAACmK,YAAY,CAACtI,SAAS,CAAC,MAAO,IAAI,CAACS,SAAS,CAAC+H,SAAS,GAAG,CAAE,CAAC;EACnE;EAEEC,OAAOA,CAAA;IACP,MAAMC,kBAAkB,GAAG,CACzB,IAAI,CAAC5G,eAAe,CAACC,UAAU,EAC/B,IAAI,CAACwG,KAAK,CAACI,UAAU,EACrB,IAAI,CAACL,YAAY,EACjB,IAAI,CAAC7H,SAAS,CAACmI,IAAI,CACpB;IAED;IACA,IAAI,CAAC9G,eAAe,CAAC5B,cAAc,EAAE,CAACF,SAAS,CAAC;MAC9C0B,IAAI,EAAGmH,QAAQ,IAAI;QACjB,IAAI,CAAC/G,eAAe,CAACC,UAAU,CAACL,IAAI,CAACmH,QAAQ,CAAC;MAChD,CAAC;MACD3G,KAAK,EAAG4G,GAAG,IAAK3G,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAE4G,GAAG;KAC5D,CAAC;IAEF,OAAOvP,KAAK,CAAC,GAAGmP,kBAAkB,CAAC,CAACK,IAAI,CACtCvP,GAAG,CAAC,MAAK;MACP,IAAI,CAACkJ,YAAY,GAAG,IAAI,CAACZ,eAAe,CAACrC,IAAI,CAC1CuJ,KAAK,EAAE,CACP1G,MAAM,CAAE5C,OAAgB,IAAI;QAC3B,MAAMuJ,SAAS,GAAG,CAChB,CAACvJ,OAAO,CAAChD,QAAQ,IAAI,EAAE,IACvBgD,OAAO,CAAC9C,IAAI,GACZ8C,OAAO,CAAC5C,WAAW,GACnB4C,OAAO,CAAC1C,cAAc,GACtB0C,OAAO,CAACxC,eAAe,GACvBwC,OAAO,CAACtC,GAAG,EACX8L,WAAW,EAAE;QACf,OAAOD,SAAS,CAACE,OAAO,CAAC,IAAI,CAAC7G,MAAM,CAAC4G,WAAW,EAAE,CAAC,KAAK,CAAC,CAAC;MAC5D,CAAC,CAAC;MAEJ,MAAME,UAAU,GAAG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAAC3G,YAAY,CAACsG,KAAK,EAAE,CAAC;MAC3D,MAAMM,UAAU,GAAG,IAAI,CAAC7I,SAAS,CAAC+H,SAAS,GAAG,IAAI,CAAC/H,SAAS,CAACE,QAAQ;MACrE,IAAI,CAACxC,YAAY,GAAGiL,UAAU,CAACnH,MAAM,CACnCqH,UAAU,EACV,IAAI,CAAC7I,SAAS,CAACE,QAAQ,CACxB;MACD,OAAO,IAAI,CAACxC,YAAY;IAC1B,CAAC,CAAC,CACH;EACH;EAIAoL,UAAUA,CAAA,GAAI;EAEdF,QAAQA,CAAC5J,IAAe;IACtB,IAAI,CAAC,IAAI,CAAC8I,KAAK,CAACiB,MAAM,IAAI,IAAI,CAACjB,KAAK,CAAC3I,SAAS,KAAK,EAAE,EAAE;MACrD,OAAOH,IAAI;;IAEb,OAAOA,IAAI,CAAC4C,IAAI,CAAC,CAACoH,CAAC,EAAEC,CAAC,KAAI;MACxB,IAAIC,SAAS,GAAoB,EAAE;MACnC,IAAIC,SAAS,GAAoB,EAAE;MACnC,QAAQ,IAAI,CAACrB,KAAK,CAACiB,MAAM;QACvB,KAAK,UAAU;UACb,CAACG,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAAC/M,QAAQ,IAAI,EAAE,EAAEgN,CAAC,CAAChN,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC;UAC/D;QACF,KAAK,MAAM;UACT,CAACiN,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAAC7M,IAAI,EAAE8M,CAAC,CAAC9M,IAAI,CAAC;UACzC;QACF,KAAK,gBAAgB;UACnB,CAAC+M,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACzM,cAAc,EAAE0M,CAAC,CAAC1M,cAAc,CAAC;UAC7D;QACF,KAAK,iBAAiB;UACpB,CAAC2M,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACvM,eAAe,EAAEwM,CAAC,CAACxM,eAAe,CAAC;UAC/D;QACF,KAAK,KAAK;UACR,CAACyM,SAAS,EAAEC,SAAS,CAAC,GAAG,CAACH,CAAC,CAACrM,GAAG,EAAEsM,CAAC,CAACtM,GAAG,CAAC;UACvC;;MAEJ,MAAMyM,MAAM,GAAGC,KAAK,CAAC,CAACH,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS;MACzD,MAAMI,MAAM,GAAGD,KAAK,CAAC,CAACF,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS;MACzD,OACE,CAACC,MAAM,GAAGE,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,IAAI,CAACxB,KAAK,CAAC3I,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAE1E,CAAC,CAAC;EACN"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}