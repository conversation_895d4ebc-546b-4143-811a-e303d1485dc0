{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders()\n    }).pipe(tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) actualId = clientByCode.id;\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\n    }\n    // Nettoyer les données en utilisant le même format que createClient\n    const cleanedClient = {};\n    // Ajouter seulement les champs non vides\n    if (client.code?.trim()) {\n      cleanedClient.code = client.code.trim();\n    }\n    if (client.syntax?.trim()) {\n      cleanedClient.syntax = client.syntax.trim();\n    }\n    if (client.matFiscal?.trim()) {\n      cleanedClient.matFiscal = client.matFiscal.trim();\n    }\n    if (client.email?.trim()) {\n      cleanedClient.email = client.email.trim();\n    }\n    if (client.telephone?.trim()) {\n      cleanedClient.telephone = client.telephone.trim();\n    }\n    console.log('Données avant nettoyage:', client);\n    console.log('Données après nettoyage:', cleanedClient);\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    // Validation supplémentaire\n    if (cleanedClient.code && cleanedClient.code.length < 2) {\n      return throwError(() => new Error('Le code client doit contenir au moins 2 caractères'));\n    }\n    if (cleanedClient.email && !cleanedClient.email.includes('@')) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Données nettoyées à envoyer:', cleanedClient);\n    console.log('Headers:', this.getHeaders());\n    console.log('==============================');\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Mise à jour réussie côté serveur');\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = {\n          ...currentData[index],\n          ...cleanedClient\n        };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(error => {\n      console.error('=== ERREUR SERVICE CLIENT ===');\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      console.error('Message:', error.message);\n      console.error('URL:', error.url);\n      console.error('Headers sent:', this.getHeaders());\n      console.error('Data sent:', cleanedClient);\n      console.error('==============================');\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || error.status === 400 && error.error?.message?.includes('validation')) {\n        console.log('Activation du mode simulation locale');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId);\n    }));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(error => {\n      // Simulation locale en cas d'erreur\n      if (error.status === 0 || error.status === 500) {\n        return this.simulateLocalDeletion(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Simulation locale de mise à jour\n  simulateLocalUpdate(id, updateData) {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Simulation locale de suppression\n  simulateLocalDeletion(ids) {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length\n        });\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "handleError", "error", "errorMessage", "message", "status", "navigate", "Error", "getAllClients", "get", "headers", "pipe", "clients", "next", "getClientById", "id", "client", "setItem", "stringify", "createClient", "code", "trim", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "post", "newClient", "currentData", "value", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "cleanedClient", "console", "log", "Object", "keys", "length", "includes", "put", "index", "findIndex", "updatedClient", "url", "simulateLocalUpdate", "deleteSelectedClients", "ids", "delete", "body", "filter", "simulateLocalDeletion", "deleteClient", "updateData", "observer", "setTimeout", "complete", "deletedCount", "clearCurrentClient", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\n  private currentClientSubject: BehaviorSubject<Client | null>;\n  public currentClient$: Observable<Client | null>;\n  dataChange = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n\n  constructor(private http: HttpClient, private router: Router) {\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n\n  private getClientFromStorage(): Client | null {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\n    });\n  }\n\n  private handleError(error: HttpErrorResponse) {\n    let errorMessage = 'Une erreur est survenue';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true;\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false;\n        this.dataChange.next(clients || []);\n      }),\n      catchError(error => {\n        this.isTblLoading = false;\n        this.dataChange.next([]);\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n\n    return this.http.post<Client>(this.baseUrl, clientData, { headers: this.getHeaders() }).pipe(\n      tap(newClient => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Mettre à jour un client\nupdateClient(id: string, client: UpdateClientDto): Observable<Client> {\n  if (!id?.trim() || !client) {\n    return throwError(() => new Error('ID et données client requis'));\n  }\n\n  // Résoudre l'ID si c'est un code client\n  let actualId = id;\n  const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n  if (isCode) {\n    const clientByCode = this.dataChange.value.find(c => c.code === id);\n    if (clientByCode) actualId = clientByCode.id;\n  }\n\n  // Valider que l'ID final est un GUID\n  const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n  if (!isGuid) {\n    return throwError(() => new Error(`ID invalide: ${actualId}`));\n  }\n\n  // Nettoyer les données en utilisant le même format que createClient\n  const cleanedClient: any = {};\n\n  // Ajouter seulement les champs non vides\n  if (client.code?.trim()) {\n    cleanedClient.code = client.code.trim();\n  }\n  if (client.syntax?.trim()) {\n    cleanedClient.syntax = client.syntax.trim();\n  }\n  if (client.matFiscal?.trim()) {\n    cleanedClient.matFiscal = client.matFiscal.trim();\n  }\n  if (client.email?.trim()) {\n    cleanedClient.email = client.email.trim();\n  }\n  if (client.telephone?.trim()) {\n    cleanedClient.telephone = client.telephone.trim();\n  }\n\n  console.log('Données avant nettoyage:', client);\n  console.log('Données après nettoyage:', cleanedClient);\n\n  if (Object.keys(cleanedClient).length === 0) {\n    return throwError(() => new Error('Aucune donnée à mettre à jour'));\n  }\n\n  // Validation supplémentaire\n  if (cleanedClient.code && cleanedClient.code.length < 2) {\n    return throwError(() => new Error('Le code client doit contenir au moins 2 caractères'));\n  }\n  if (cleanedClient.email && !cleanedClient.email.includes('@')) {\n    return throwError(() => new Error('Format d\\'email invalide'));\n  }\n\n  console.log('=== SERVICE CLIENT UPDATE ===');\n  console.log('URL finale:', `${this.baseUrl}${actualId}`);\n  console.log('Données nettoyées à envoyer:', cleanedClient);\n  console.log('Headers:', this.getHeaders());\n  console.log('==============================');\n\n  return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n    headers: this.getHeaders()\n  }).pipe(\n    tap(() => {\n      console.log('Mise à jour réussie côté serveur');\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = { ...currentData[index], ...cleanedClient };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }),\n    catchError(error => {\n      console.error('=== ERREUR SERVICE CLIENT ===');\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      console.error('Message:', error.message);\n      console.error('URL:', error.url);\n      console.error('Headers sent:', this.getHeaders());\n      console.error('Data sent:', cleanedClient);\n      console.error('==============================');\n\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || (error.status === 400 && error.error?.message?.includes('validation'))) {\n        console.log('Activation du mode simulation locale');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId)!;\n    })\n  );\n}\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n      }),\n      catchError(error => {\n        // Simulation locale en cas d'erreur\n        if (error.status === 0 || error.status === 500) {\n          return this.simulateLocalDeletion(ids);\n        }\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => client.id !== id));\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Simulation locale de mise à jour\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    \n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n\n    const updatedClient: Client = { ...currentData[index], ...updateData };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    \n    return new Observable<Client>(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Simulation locale de suppression\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    \n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({ deletedCount: ids.length });\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthodes utilitaires\n  private generateGuid(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n}\n"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEQC,WAAWA,CAACC,KAAwB;IAC1C,IAAIC,YAAY,GAAG,yBAAyB;IAC5C,IAAID,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAE;MACxBD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACE,OAAO;KACnC,MAAM,IAAIF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MAC7BF,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAID,KAAK,CAACG,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACjB,MAAM,CAACoB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAElC,OAAO3B,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;EAEA;EACAK,aAAaA,CAAA;IACX,IAAI,CAACnB,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACwB,GAAG,CAAW,IAAI,CAACtB,OAAO,EAAE;MAAEuB,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CAACY,IAAI,CAC/E/B,GAAG,CAACgC,OAAO,IAAG;MACZ,IAAI,CAACvB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACyB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,EACF/B,UAAU,CAACqB,KAAK,IAAG;MACjB,IAAI,CAACb,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACyB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACZ,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAY,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAAC9B,IAAI,CAACwB,GAAG,CAAS,GAAG,IAAI,CAACtB,OAAO,GAAG4B,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CAACY,IAAI,CACvF/B,GAAG,CAACoC,MAAM,IAAG;MACXrB,YAAY,CAACsB,OAAO,CAAC,eAAe,EAAEpB,IAAI,CAACqB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAAC1B,oBAAoB,CAACuB,IAAI,CAACG,MAAM,CAAC;IACxC,CAAC,CAAC,EACFnC,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAkB,YAAYA,CAACH,MAA6B;IACxC,IAAI,CAACA,MAAM,EAAEI,IAAI,EAAEC,IAAI,EAAE,EAAE;MACzB,OAAO1C,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D,MAAMb,UAAU,GAAG;MACjBqB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACO,YAAY,EAAE;MACpCF,IAAI,EAAEJ,MAAM,CAACI,IAAI,CAACC,IAAI,EAAE;MACxBE,MAAM,EAAEP,MAAM,CAACO,MAAM,EAAEF,IAAI,EAAE;MAC7BG,SAAS,EAAER,MAAM,CAACQ,SAAS,EAAEH,IAAI,EAAE;MACnCI,KAAK,EAAET,MAAM,CAACS,KAAK,EAAEJ,IAAI,EAAE;MAC3BK,SAAS,EAAEV,MAAM,CAACU,SAAS,EAAEL,IAAI;KAClC;IAED,OAAO,IAAI,CAACpC,IAAI,CAAC0C,IAAI,CAAS,IAAI,CAACxC,OAAO,EAAEO,UAAU,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CAACY,IAAI,CAC1F/B,GAAG,CAACgD,SAAS,IAAG;MACd,MAAMC,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACyB,IAAI,CAAC,CAAC,GAAGgB,WAAW,EAAED,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF/C,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAC7B;EACH;EAEA;EACF8B,YAAYA,CAAChB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEM,IAAI,EAAE,IAAI,CAACL,MAAM,EAAE;MAC1B,OAAOrC,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIyB,QAAQ,GAAGjB,EAAE;IACjB,MAAMkB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACnB,EAAE,CAAC;IAC1C,IAAIkB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAAC/C,UAAU,CAAC0C,KAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjB,IAAI,KAAKL,EAAE,CAAC;MACnE,IAAIoB,YAAY,EAAEH,QAAQ,GAAGG,YAAY,CAACpB,EAAE;;IAG9C;IACA,MAAMuB,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAO3D,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,gBAAgByB,QAAQ,EAAE,CAAC,CAAC;;IAGhE;IACA,MAAMO,aAAa,GAAQ,EAAE;IAE7B;IACA,IAAIvB,MAAM,CAACI,IAAI,EAAEC,IAAI,EAAE,EAAE;MACvBkB,aAAa,CAACnB,IAAI,GAAGJ,MAAM,CAACI,IAAI,CAACC,IAAI,EAAE;;IAEzC,IAAIL,MAAM,CAACO,MAAM,EAAEF,IAAI,EAAE,EAAE;MACzBkB,aAAa,CAAChB,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACF,IAAI,EAAE;;IAE7C,IAAIL,MAAM,CAACQ,SAAS,EAAEH,IAAI,EAAE,EAAE;MAC5BkB,aAAa,CAACf,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAACH,IAAI,EAAE;;IAEnD,IAAIL,MAAM,CAACS,KAAK,EAAEJ,IAAI,EAAE,EAAE;MACxBkB,aAAa,CAACd,KAAK,GAAGT,MAAM,CAACS,KAAK,CAACJ,IAAI,EAAE;;IAE3C,IAAIL,MAAM,CAACU,SAAS,EAAEL,IAAI,EAAE,EAAE;MAC5BkB,aAAa,CAACb,SAAS,GAAGV,MAAM,CAACU,SAAS,CAACL,IAAI,EAAE;;IAGnDmB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEzB,MAAM,CAAC;IAC/CwB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,aAAa,CAAC;IAEtD,IAAIG,MAAM,CAACC,IAAI,CAACJ,aAAa,CAAC,CAACK,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAOjE,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE;IACA,IAAIgC,aAAa,CAACnB,IAAI,IAAImB,aAAa,CAACnB,IAAI,CAACwB,MAAM,GAAG,CAAC,EAAE;MACvD,OAAOjE,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,oDAAoD,CAAC,CAAC;;IAE1F,IAAIgC,aAAa,CAACd,KAAK,IAAI,CAACc,aAAa,CAACd,KAAK,CAACoB,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7D,OAAOlE,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhEiC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAACtD,OAAO,GAAG6C,QAAQ,EAAE,CAAC;IACxDQ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,aAAa,CAAC;IAC1DC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC1C,UAAU,EAAE,CAAC;IAC1CyC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAACxD,IAAI,CAAC6D,GAAG,CAAC,GAAG,IAAI,CAAC3D,OAAO,GAAG6C,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChE7B,OAAO,EAAE,IAAI,CAACX,UAAU;KACzB,CAAC,CAACY,IAAI,CACL/B,GAAG,CAAC,MAAK;MACP4D,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C;MACA,MAAMZ,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAACX,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKiB,QAAQ,CAAC;MAC3D,IAAIe,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAME,aAAa,GAAG;UAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;UAAE,GAAGR;QAAa,CAAE;QACjEV,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;QAClC,IAAI,CAAC7D,UAAU,CAACyB,IAAI,CAAC,CAAC,GAAGgB,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACFhD,UAAU,CAACqB,KAAK,IAAG;MACjBsC,OAAO,CAACtC,KAAK,CAAC,+BAA+B,CAAC;MAC9CsC,OAAO,CAACtC,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACG,MAAM,CAAC;MACtCmC,OAAO,CAACtC,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MACzCsC,OAAO,CAACtC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAACE,OAAO,CAAC;MACxCoC,OAAO,CAACtC,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACgD,GAAG,CAAC;MAChCV,OAAO,CAACtC,KAAK,CAAC,eAAe,EAAE,IAAI,CAACH,UAAU,EAAE,CAAC;MACjDyC,OAAO,CAACtC,KAAK,CAAC,YAAY,EAAEqC,aAAa,CAAC;MAC1CC,OAAO,CAACtC,KAAK,CAAC,gCAAgC,CAAC;MAE/C;MACA,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,IAAKH,KAAK,CAACG,MAAM,KAAK,GAAG,IAAIH,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAEyC,QAAQ,CAAC,YAAY,CAAE,EAAE;QAChGL,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,OAAO,IAAI,CAACU,mBAAmB,CAACnB,QAAQ,EAAEO,aAAa,CAAC;;MAE1D,OAAO,IAAI,CAACtC,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC;IACF;IACApB,GAAG,CAAC,MAAK;MACP,MAAM+C,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,OAAOD,WAAW,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,EAAE,KAAKiB,QAAQ,CAAE;IAClD,CAAC,CAAC,CACH;EACH;EACE;EACAoB,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,EAAET,MAAM,EAAE;MAChB,OAAOjE,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,iBAAiB,CAAC,CAAC;;IAGvD,OAAO,IAAI,CAACtB,IAAI,CAACqE,MAAM,CAAC,GAAG,IAAI,CAACnE,OAAO,iBAAiB,EAAE;MACxDuB,OAAO,EAAE,IAAI,CAACX,UAAU,EAAE;MAC1BwD,IAAI,EAAEF;KACP,CAAC,CAAC1C,IAAI,CACL/B,GAAG,CAAC,MAAK;MACP,MAAMiD,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACyB,IAAI,CAACgB,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAI,CAACqC,GAAG,CAACR,QAAQ,CAAC7B,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACFlC,UAAU,CAACqB,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,IAAIH,KAAK,CAACG,MAAM,KAAK,GAAG,EAAE;QAC9C,OAAO,IAAI,CAACoD,qBAAqB,CAACJ,GAAG,CAAC;;MAExC,OAAO,IAAI,CAACpD,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAwD,YAAYA,CAAC3C,EAAU;IACrB,OAAO,IAAI,CAAC9B,IAAI,CAACqE,MAAM,CAAO,GAAG,IAAI,CAACnE,OAAO,GAAG4B,EAAE,EAAE,EAAE;MAAEL,OAAO,EAAE,IAAI,CAACX,UAAU;IAAE,CAAE,CAAC,CAACY,IAAI,CACxF/B,GAAG,CAAC,MAAK;MACP,MAAMiD,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACyB,IAAI,CAACgB,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACFlC,UAAU,CAAC,IAAI,CAACoB,WAAW,CAAC,CAC7B;EACH;EAEA;EACQkD,mBAAmBA,CAACpC,EAAU,EAAE4C,UAA2B;IACjE,MAAM9B,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAAChC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIgC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOpE,UAAU,CAAC,MAAM,IAAI4B,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,MAAM0C,aAAa,GAAW;MAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;MAAE,GAAGY;IAAU,CAAE;IACtE9B,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;IAClC,IAAI,CAAC7D,UAAU,CAACyB,IAAI,CAAC,CAAC,GAAGgB,WAAW,CAAC,CAAC;IAEtC,OAAO,IAAInD,UAAU,CAASkF,QAAQ,IAAG;MACvCC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/C,IAAI,CAACoC,aAAa,CAAC;QAC5BW,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACQL,qBAAqBA,CAACJ,GAAa;IACzC,MAAMxB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,IAAI,CAAC1C,UAAU,CAACyB,IAAI,CAACgB,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAI,CAACqC,GAAG,CAACR,QAAQ,CAAC7B,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,IAAIrC,UAAU,CAACkF,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/C,IAAI,CAAC;UAAEkD,YAAY,EAAEV,GAAG,CAACT;QAAM,CAAE,CAAC;QAC3CgB,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACAE,kBAAkBA,CAAA;IAChBrE,YAAY,CAACsE,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC3E,oBAAoB,CAACuB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIqD,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC9E,UAAU,CAAC0C,KAAK;EAC9B;EAEAqC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQ9C,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAAC+C,OAAO,CAAC,OAAO,EAAE,UAAShC,CAAC;MACvE,MAAMiC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGpC,CAAC,KAAK,GAAG,GAAGiC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBAvRU5F,aAAa,EAAA6F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbnG,aAAa;IAAAoG,OAAA,EAAbpG,aAAa,CAAAqG,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}