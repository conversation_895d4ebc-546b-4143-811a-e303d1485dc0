{"ast": null, "code": "import { __decorate, __param } from \"tslib\";\nimport { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nlet ClientDeleteComponent = class ClientDeleteComponent {\n  constructor(dialogRef, data, clientService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.clientService = clientService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.clientService.deleteClient(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: error => {\n        console.error('Erreur lors de la suppression du client:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n};\nClientDeleteComponent = __decorate([Component({\n  selector: 'app-client-delete',\n  templateUrl: './client-delete.component.html',\n  styleUrls: ['./client-delete.component.scss'],\n  standalone: true,\n  imports: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, MatIconModule, MatDialogClose, CommonModule]\n}), __param(1, Inject(MAT_DIALOG_DATA))], ClientDeleteComponent);\nexport { ClientDeleteComponent };", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatDialogClose", "Component", "Inject", "MatButtonModule", "MatIconModule", "CommonModule", "ClientDeleteComponent", "constructor", "dialogRef", "data", "clientService", "onNoClick", "close", "confirmDelete", "deleteClient", "id", "subscribe", "next", "error", "console", "__decorate", "selector", "templateUrl", "styleUrls", "standalone", "imports", "__param"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client-delete\\client-delete.component.ts"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { ClientService } from '../../services/clent.service.optimized';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\n\nexport interface DialogData {\n  id: string;\n  code: string;\n  syntax?: string;\n  matFiscal?: string;\n  email?: string;\n  telephone?: string;\n}\n\n@Component({\n  selector: 'app-client-delete',\n  templateUrl: './client-delete.component.html',\n  styleUrls: ['./client-delete.component.scss'],\n  standalone: true,\n  imports: [\n    MatDialogTitle,\n    MatDialogContent,\n    MatDialogActions,\n    MatButtonModule,\n    MatIconModule,\n    MatDialogClose,\n    CommonModule,\n  ],\n})\nexport class ClientDeleteComponent {\n  constructor(\n    public dialogRef: MatDialogRef<ClientDeleteComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\n    public clientService: ClientService\n  ) {}\n\n  onNoClick(): void {\n    this.dialogRef.close();\n  }\n\n  confirmDelete(): void {\n    this.clientService.deleteClient(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: (error) => {\n        console.error('Erreur lors de la suppression du client:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n}\n"], "mappings": ";AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,0BAA0B;AAC5I,SAASC,SAAS,EAAEC,MAAM,QAAQ,eAAe;AAEjD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;AA0BvC,IAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAChCC,YACSC,SAA8C,EACrBC,IAAgB,EACzCC,aAA4B;IAF5B,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,aAAa,GAAbA,aAAa;EACnB;EAEHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,aAAa,CAACI,YAAY,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC,CAACC,SAAS,CAAC;MACtDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,KAAK,CAAC;MAC7B;KACD,CAAC;EACJ;CACD;AAtBYN,qBAAqB,GAAAc,UAAA,EAfjCnB,SAAS,CAAC;EACToB,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,gCAAgC,CAAC;EAC7CC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CACP5B,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBI,eAAe,EACfC,aAAa,EACbJ,cAAc,EACdK,YAAY;CAEf,CAAC,EAIGqB,OAAA,IAAAxB,MAAM,CAACN,eAAe,CAAC,E,EAHfU,qBAAqB,CAsBjC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}