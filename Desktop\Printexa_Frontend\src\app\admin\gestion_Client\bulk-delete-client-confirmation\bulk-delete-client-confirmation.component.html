<div class="container">
  <h3 mat-dialog-title>
    <mat-icon class="warning-icon">warning</mat-icon>
    Confirmation de suppression en lot
  </h3>
  
  <div mat-dialog-content>
    <div class="warning-message">
      <p>
        <strong>Attention !</strong> Vous êtes sur le point de supprimer définitivement 
        <strong>{{data.totalCount}} client(s)</strong>. Cette action est irréversible.
      </p>
    </div>

    <div class="summary-section">
      <h4>Résumé de la sélection :</h4>
      
      <div class="summary-stats">
        <div class="stat-item">
          <mat-icon>people</mat-icon>
          <span class="stat-label">Nombre de clients :</span>
          <span class="stat-value">{{data.totalCount}}</span>
        </div>
        
        <div class="stat-item">
          <mat-icon>email</mat-icon>
          <span class="stat-label">Avec email :</span>
          <span class="stat-value">{{getClientsSummary().withEmail}}</span>
        </div>

        <div class="stat-item">
          <mat-icon>phone</mat-icon>
          <span class="stat-label">Avec téléphone :</span>
          <span class="stat-value">{{getClientsSummary().withPhone}}</span>
        </div>

        <div class="stat-item">
          <mat-icon>receipt_long</mat-icon>
          <span class="stat-label">Avec matricule fiscal :</span>
          <span class="stat-value">{{getClientsSummary().withMatFiscal}}</span>
        </div>
      </div>
    </div>

    <!-- Affichage des premiers clients si la liste n'est pas trop longue -->
    <div class="clients-preview" *ngIf="data.selectedClients.length <= 5">
      <h5>Clients à supprimer :</h5>
      <div class="client-list">
        <div class="client-item" *ngFor="let client of data.selectedClients">
          <div class="client-info">
            <span class="client-code">{{client.code}}</span>
            <span class="client-syntax">{{client.syntax || 'Sans raison sociale'}}</span>
            <span class="client-contact" *ngIf="client.email || client.telephone">
              <mat-icon *ngIf="client.email">email</mat-icon>
              <mat-icon *ngIf="client.telephone">phone</mat-icon>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Message pour les grandes sélections -->
    <div class="large-selection-message" *ngIf="data.selectedClients.length > 5">
      <p>
        <mat-icon>info</mat-icon>
        Trop de clients sélectionnés pour les afficher individuellement. 
        Consultez le résumé ci-dessus.
      </p>
      <div class="codes-preview">
        <strong>Codes des clients :</strong>
        <span class="codes-list">{{getClientCodes().slice(0, 10).join(', ')}}
          <span *ngIf="data.totalCount > 10">... et {{data.totalCount - 10}} autres</span>
        </span>
      </div>
    </div>
  </div>
  
  <div mat-dialog-actions class="mb-1">
    <button mat-flat-button color="warn" (click)="confirmDelete()" class="delete-btn">
      <mat-icon>delete_forever</mat-icon>
      Supprimer tout
    </button>
    <button mat-flat-button (click)="onNoClick()" class="cancel-btn">
      <mat-icon>cancel</mat-icon>
      Annuler
    </button>
  </div>
</div>
