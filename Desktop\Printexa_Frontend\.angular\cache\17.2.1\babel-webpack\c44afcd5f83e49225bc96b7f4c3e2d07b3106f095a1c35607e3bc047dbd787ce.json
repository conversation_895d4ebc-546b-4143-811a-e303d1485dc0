{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/client.service\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nexport class ClientDeleteComponent {\n  constructor(dialogRef, data, clientService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.clientService = clientService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.clientService.deleteClient(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: error => {\n        console.error('Erreur lors de la suppression du client:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ClientDeleteComponent_Factory(t) {\n    return new (t || ClientDeleteComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ClientService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ClientDeleteComponent,\n    selectors: [[\"app-client-delete\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 47,\n    vars: 6,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [1, \"warning-icon\"], [\"mat-dialog-content\", \"\"], [1, \"warning-message\"], [1, \"client-details\"], [1, \"clearfix\"], [1, \"font-weight-bold\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"delete-btn\", 3, \"mat-dialog-close\", \"click\"], [\"mat-flat-button\", \"\", \"tabindex\", \"-1\", 1, \"cancel-btn\", 3, \"click\"]],\n    template: function ClientDeleteComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" \\u00CAtes-vous s\\u00FBr de vouloir supprimer ce client ? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"p\");\n        i0.ɵɵtext(8, \"Cette action est irr\\u00E9versible. Le client sera d\\u00E9finitivement supprim\\u00E9.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"h4\");\n        i0.ɵɵtext(11, \"D\\u00E9tails du client \\u00E0 supprimer :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"ul\", 6)(13, \"li\")(14, \"p\")(15, \"span\", 7);\n        i0.ɵɵtext(16, \"Code Client : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"li\")(19, \"p\")(20, \"span\", 7);\n        i0.ɵɵtext(21, \"Raison Sociale : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"li\")(24, \"p\")(25, \"span\", 7);\n        i0.ɵɵtext(26, \"Matricule Fiscal : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"li\")(29, \"p\")(30, \"span\", 7);\n        i0.ɵɵtext(31, \"Email : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(32);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(33, \"li\")(34, \"p\")(35, \"span\", 7);\n        i0.ɵɵtext(36, \"T\\u00E9l\\u00E9phone : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(37);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(38, \"div\", 8)(39, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ClientDeleteComponent_Template_button_click_39_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵelementStart(40, \"mat-icon\");\n        i0.ɵɵtext(41, \"delete\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(42, \" Supprimer \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(43, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ClientDeleteComponent_Template_button_click_43_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵelementStart(44, \"mat-icon\");\n        i0.ɵɵtext(45, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(46, \" Annuler \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(17);\n        i0.ɵɵtextInterpolate(ctx.data.code);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.syntax || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.matFiscal || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.email || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.telephone || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"mat-dialog-close\", 1);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i3.MatButton, MatIconModule, i4.MatIcon, MatDialogClose, CommonModule],\n    styles: [\".container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 500px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #d32f2f;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 24px;\\n  color: #ff9800;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 4px;\\n  padding: 12px;\\n  margin-bottom: 20px;\\n  color: #856404;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  padding: 4px 0;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6c757d;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   .font-weight-bold[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n  margin-top: 20px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%] {\\n  background-color: #d32f2f;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #b71c1c;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);\\n}\\n\\n@media (max-width: 480px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    margin-right: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .container[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n.container[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.font-weight-bold[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n}\\n\\nbutton[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatDialogClose", "MatButtonModule", "MatIconModule", "CommonModule", "ClientDeleteComponent", "constructor", "dialogRef", "data", "clientService", "onNoClick", "close", "confirmDelete", "deleteClient", "id", "subscribe", "next", "error", "console", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ClientService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ClientDeleteComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ClientDeleteComponent_Template_button_click_39_listener", "ClientDeleteComponent_Template_button_click_43_listener", "ɵɵadvance", "ɵɵtextInterpolate", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "ɵɵproperty", "i3", "MatButton", "i4", "MatIcon", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client-delete\\client-delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client-delete\\client-delete.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { ClientService } from '../../services/client.service';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\n\nexport interface DialogData {\n  id: string;\n  code: string;\n  syntax?: string;\n  matFiscal?: string;\n  email?: string;\n  telephone?: string;\n}\n\n@Component({\n  selector: 'app-client-delete',\n  templateUrl: './client-delete.component.html',\n  styleUrls: ['./client-delete.component.scss'],\n  standalone: true,\n  imports: [\n    MatDialogTitle,\n    MatDialogContent,\n    MatDialogActions,\n    MatButtonModule,\n    MatIconModule,\n    MatDialogClose,\n    CommonModule,\n  ],\n})\nexport class ClientDeleteComponent {\n  constructor(\n    public dialogRef: MatDialogRef<ClientDeleteComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\n    public clientService: ClientService\n  ) {}\n\n  onNoClick(): void {\n    this.dialogRef.close();\n  }\n\n  confirmDelete(): void {\n    this.clientService.deleteClient(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: (error) => {\n        console.error('Erreur lors de la suppression du client:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n}\n", "<div class=\"container\">\n  <h3 mat-dialog-title>\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\n    Êtes-vous sûr de vouloir supprimer ce client ?\n  </h3>\n  \n  <div mat-dialog-content>\n    <div class=\"warning-message\">\n      <p>Cette action est irréversible. Le client sera définitivement supprimé.</p>\n    </div>\n    \n    <div class=\"client-details\">\n      <h4>Détails du client à supprimer :</h4>\n      <ul class=\"clearfix\">\n        <li>\n          <p><span class=\"font-weight-bold\">Code Client : </span>{{data.code}}</p>\n        </li>\n        <li>\n          <p><span class=\"font-weight-bold\">Raison Sociale : </span>{{data.syntax || 'Non défini'}}</p>\n        </li>\n        <li>\n          <p><span class=\"font-weight-bold\">Matricule Fiscal : </span>{{data.matFiscal || 'Non défini'}}</p>\n        </li>\n        <li>\n          <p><span class=\"font-weight-bold\">Email : </span>{{data.email || 'Non défini'}}</p>\n        </li>\n        <li>\n          <p><span class=\"font-weight-bold\">Téléphone : </span>{{data.telephone || 'Non défini'}}</p>\n        </li>\n      </ul>\n    </div>\n  </div>\n  \n  <div mat-dialog-actions class=\"mb-1\">\n    <button mat-flat-button color=\"warn\" [mat-dialog-close]=\"1\" (click)=\"confirmDelete()\" class=\"delete-btn\">\n      <mat-icon>delete</mat-icon>\n      Supprimer\n    </button>\n    <button mat-flat-button (click)=\"onNoClick()\" tabindex=\"-1\" class=\"cancel-btn\">\n      <mat-icon>cancel</mat-icon>\n      Annuler\n    </button>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,0BAA0B;AAG5I,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;;;;;;AA0B9C,OAAM,MAAOC,qBAAqB;EAChCC,YACSC,SAA8C,EACrBC,IAAgB,EACzCC,aAA4B;IAF5B,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,aAAa,GAAbA,aAAa;EACnB;EAEHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,aAAa,CAACI,YAAY,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC,CAACC,SAAS,CAAC;MACtDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;QAChE,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,KAAK,CAAC;MAC7B;KACD,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBArBUd,qBAAqB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGtBxB,eAAe,GAAAuB,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,aAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdrB,qBAAqB;IAAAsB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAT,EAAA,CAAAU,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC/BlChB,EAAA,CAAAkB,cAAA,aAAuB;QAEYlB,EAAA,CAAAmB,MAAA,cAAO;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QACjDpB,EAAA,CAAAmB,MAAA,iEACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAELpB,EAAA,CAAAkB,cAAA,aAAwB;QAEjBlB,EAAA,CAAAmB,MAAA,4FAAsE;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAG/EpB,EAAA,CAAAkB,cAAA,aAA4B;QACtBlB,EAAA,CAAAmB,MAAA,iDAA+B;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QACxCpB,EAAA,CAAAkB,cAAA,aAAqB;QAEiBlB,EAAA,CAAAmB,MAAA,sBAAc;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAE1EpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,yBAAiB;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAA+B;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAE/FpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,2BAAmB;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAkC;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEpGpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,gBAAQ;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAA8B;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAErFpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,8BAAY;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAkC;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAMnGpB,EAAA,CAAAkB,cAAA,cAAqC;QACyBlB,EAAA,CAAAqB,UAAA,mBAAAC,wDAAA;UAAA,OAASL,GAAA,CAAAzB,aAAA,EAAe;QAAA,EAAC;QACnFQ,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,mBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAkB,cAAA,kBAA+E;QAAvDlB,EAAA,CAAAqB,UAAA,mBAAAE,wDAAA;UAAA,OAASN,GAAA,CAAA3B,SAAA,EAAW;QAAA,EAAC;QAC3CU,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,iBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;;;QA1BoDpB,EAAA,CAAAwB,SAAA,IAAa;QAAbxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAsC,IAAA,CAAa;QAGV1B,EAAA,CAAAwB,SAAA,GAA+B;QAA/BxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAuC,MAAA,sBAA+B;QAG7B3B,EAAA,CAAAwB,SAAA,GAAkC;QAAlCxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAwC,SAAA,sBAAkC;QAG7C5B,EAAA,CAAAwB,SAAA,GAA8B;QAA9BxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAyC,KAAA,sBAA8B;QAG1B7B,EAAA,CAAAwB,SAAA,GAAkC;QAAlCxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAA0C,SAAA,sBAAkC;QAOxD9B,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAA+B,UAAA,uBAAsB;;;mBDZ3DrD,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBE,eAAe,EAAAkD,EAAA,CAAAC,SAAA,EACflD,aAAa,EAAAmD,EAAA,CAAAC,OAAA,EACbtD,cAAc,EACdG,YAAY;IAAAoD,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}