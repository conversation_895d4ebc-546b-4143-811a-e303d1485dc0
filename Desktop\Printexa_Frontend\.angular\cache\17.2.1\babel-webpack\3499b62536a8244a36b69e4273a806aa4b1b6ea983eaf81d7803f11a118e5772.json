{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError } from 'rxjs';\nimport { tap, catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    console.log('=== SERVICE CREATE CLIENT ===');\n    console.log('URL:', this.baseUrl);\n    console.log('Données reçues:', client);\n    console.log('Headers:', this.getHeaders());\n    const clientData = {\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    // Nettoyer les valeurs undefined\n    Object.keys(clientData).forEach(key => {\n      if (clientData[key] === undefined) {\n        delete clientData[key];\n      }\n    });\n    console.log('Données nettoyées à envoyer:', clientData);\n    console.log('==============================');\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    const cleanedClient = {};\n    Object.keys(client).forEach(key => {\n      if (key !== 'id' && client[key]?.toString().trim()) {\n        cleanedClient[key] = client[key];\n      }\n    });\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    return this.http.put(`${this.baseUrl}${id}`, cleanedClient, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === id);\n      if (index !== -1) {\n        currentData[index] = {\n          ...currentData[index],\n          ...cleanedClient\n        };\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(this.handleError));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(this.handleError));\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Gestionnaire d'erreur spécialisé pour la création\n  handleCreateError(error) {\n    console.error('=== ERREUR SERVICE ===');\n    console.error('Status:', error.status);\n    console.error('Status Text:', error.statusText);\n    console.error('Error object:', error.error);\n    console.error('URL:', error.url);\n    console.error('======================');\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez que l\\'API est démarrée.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      } else if (error.error?.title) {\n        errorMessage = error.error.title;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ce code existe déjà';\n    } else if (error.status === 500) {\n      errorMessage = 'Erreur interne du serveur';\n    }\n    // Créer une erreur avec plus de détails\n    const detailedError = new Error(errorMessage);\n    detailedError.originalError = error;\n    return throwError(() => detailedError);\n  }\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Non autorisé';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - le client existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "console", "log", "code", "trim", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "Object", "keys", "for<PERSON>ach", "key", "undefined", "post", "observe", "response", "newClient", "body", "Error", "currentData", "value", "handleCreateError", "updateClient", "cleanedClient", "toString", "length", "put", "index", "findIndex", "c", "deleteClient", "delete", "filter", "deleteSelectedClients", "ids", "includes", "clearCurrentClient", "removeItem", "data", "getDialogData", "dialogData", "status", "statusText", "url", "errorMessage", "apiError", "message", "title", "errors", "validationErrors", "values", "flat", "join", "navigate", "detailedError", "originalError", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\r\nimport { tap, catchError, map } from 'rxjs/operators';\r\nimport { Client, CreateClientDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientDto): Observable<Client> {\r\n    console.log('=== SERVICE CREATE CLIENT ===');\r\n    console.log('URL:', this.baseUrl);\r\n    console.log('Données reçues:', client);\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const clientData: CreateClientDto = {\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    // Nettoyer les valeurs undefined\r\n    Object.keys(clientData).forEach(key => {\r\n      if (clientData[key as keyof CreateClientDto] === undefined) {\r\n        delete clientData[key as keyof CreateClientDto];\r\n      }\r\n    });\r\n\r\n    console.log('Données nettoyées à envoyer:', clientData);\r\n    console.log('==============================');\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\r\n    if (!id || !client) {\r\n      return throwError(() => new Error('ID et données client requis'));\r\n    }\r\n\r\n    const cleanedClient: UpdateClientDto = {};\r\n    Object.keys(client).forEach(key => {\r\n      if (key !== 'id' && client[key as keyof UpdateClientDto]?.toString().trim()) {\r\n        cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];\r\n      }\r\n    });\r\n\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\r\n    }\r\n\r\n    return this.http.put<void>(`${this.baseUrl}${id}`, cleanedClient, { \r\n      headers: this.getHeaders()\r\n    }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === id);\r\n        if (index !== -1) {\r\n          currentData[index] = { ...currentData[index], ...cleanedClient };\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next(currentData.filter(client => client.id !== id));\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Gestionnaire d'erreur spécialisé pour la création\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    console.error('=== ERREUR SERVICE ===');\r\n    console.error('Status:', error.status);\r\n    console.error('Status Text:', error.statusText);\r\n    console.error('Error object:', error.error);\r\n    console.error('URL:', error.url);\r\n    console.error('======================');\r\n\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez que l\\'API est démarrée.';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      } else if (error.error?.title) {\r\n        errorMessage = error.error.title;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec ce code existe déjà';\r\n    } else if (error.status === 500) {\r\n      errorMessage = 'Erreur interne du serveur';\r\n    }\r\n\r\n    // Créer une erreur avec plus de détails\r\n    const detailedError = new Error(errorMessage);\r\n    (detailedError as any).originalError = error;\r\n    return throwError(() => detailedError);\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Une erreur est survenue';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Non autorisé';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - le client existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAMrD,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIV,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAW,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIZ,eAAe,CAAgB,IAAI,CAACa,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAInB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIuB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAAuB;IAClCI,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC7B,OAAO,CAAC;IACjC4B,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEL,MAAM,CAAC;IACtCI,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACjB,UAAU,EAAE,CAAC;IAE1C,MAAML,UAAU,GAAoB;MAClCuB,IAAI,EAAEN,MAAM,CAACM,IAAI,CAACC,IAAI,EAAE;MACxBC,MAAM,EAAER,MAAM,CAACQ,MAAM,EAAED,IAAI,EAAE;MAC7BE,SAAS,EAAET,MAAM,CAACS,SAAS,EAAEF,IAAI,EAAE;MACnCG,KAAK,EAAEV,MAAM,CAACU,KAAK,EAAEH,IAAI,EAAE;MAC3BI,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEJ,IAAI;KAClC;IAED;IACAK,MAAM,CAACC,IAAI,CAAC9B,UAAU,CAAC,CAAC+B,OAAO,CAACC,GAAG,IAAG;MACpC,IAAIhC,UAAU,CAACgC,GAA4B,CAAC,KAAKC,SAAS,EAAE;QAC1D,OAAOjC,UAAU,CAACgC,GAA4B,CAAC;;IAEnD,CAAC,CAAC;IAEFX,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEtB,UAAU,CAAC;IACvDqB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAAC/B,IAAI,CAAC2C,IAAI,CAAS,IAAI,CAACzC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B8B,OAAO,EAAE;KACV,CAAC,CAACzB,IAAI,CACLtB,GAAG,CAAEgD,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIE,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOF,SAAS;IAClB,CAAC,CAAC,EACFnD,GAAG,CAAEmD,SAAiB,IAAI;MACxB,MAAMG,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG4B,WAAW,EAAEH,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFlD,UAAU,CAAC,IAAI,CAACuD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAAC3B,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,IAAI,CAACC,MAAM,EAAE;MAClB,OAAOhC,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE,MAAMK,aAAa,GAAoB,EAAE;IACzCf,MAAM,CAACC,IAAI,CAACb,MAAM,CAAC,CAACc,OAAO,CAACC,GAAG,IAAG;MAChC,IAAIA,GAAG,KAAK,IAAI,IAAIf,MAAM,CAACe,GAA4B,CAAC,EAAEa,QAAQ,EAAE,CAACrB,IAAI,EAAE,EAAE;QAC3EoB,aAAa,CAACZ,GAA4B,CAAC,GAAGf,MAAM,CAACe,GAA4B,CAAC;;IAEtF,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACC,IAAI,CAACc,aAAa,CAAC,CAACE,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO7D,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE,OAAO,IAAI,CAAChD,IAAI,CAACwD,GAAG,CAAO,GAAG,IAAI,CAACtD,OAAO,GAAGuB,EAAE,EAAE,EAAE4B,aAAa,EAAE;MAChEnC,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACK,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,MAAMO,KAAK,GAAGR,WAAW,CAACS,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAClC,EAAE,KAAKA,EAAE,CAAC;MACrD,IAAIgC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBR,WAAW,CAACQ,KAAK,CAAC,GAAG;UAAE,GAAGR,WAAW,CAACQ,KAAK,CAAC;UAAE,GAAGJ;QAAa,CAAE;QAChE,IAAI,CAAClD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG4B,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACFrD,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAqC,YAAYA,CAACnC,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAAC6D,MAAM,CAAO,GAAG,IAAI,CAAC3D,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC4B,WAAW,CAACa,MAAM,CAACpC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAwC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACT,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO7D,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF,OAAO,IAAI,CAAChD,IAAI,CAAC6D,MAAM,CAAC,GAAG,IAAI,CAAC3D,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,IAAI,EAAEiB;KACP,CAAC,CAAC7C,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMsD,WAAW,GAAG,IAAI,CAAC9C,UAAU,CAAC+C,KAAK;MACzC,IAAI,CAAC/C,UAAU,CAACkB,IAAI,CAAC4B,WAAW,CAACa,MAAM,CAACpC,MAAM,IAAI,CAACsC,GAAG,CAACC,QAAQ,CAACvC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA2C,kBAAkBA,CAAA;IAChBxD,YAAY,CAACyD,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC9D,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI+C,IAAIA,CAAA;IACN,OAAO,IAAI,CAACjE,UAAU,CAAC+C,KAAK;EAC9B;EAEAmB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQnB,iBAAiBA,CAAC7B,KAAwB;IAChDQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;IACvCQ,OAAO,CAACR,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACiD,MAAM,CAAC;IACtCzC,OAAO,CAACR,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACkD,UAAU,CAAC;IAC/C1C,OAAO,CAACR,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CQ,OAAO,CAACR,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACmD,GAAG,CAAC;IAChC3C,OAAO,CAACR,KAAK,CAAC,wBAAwB,CAAC;IAEvC,IAAIoD,YAAY,GAAG,sCAAsC;IACzD,MAAMC,QAAQ,GAAGrD,KAAK,CAACA,KAAK,EAAEsD,OAAO,IAAItD,KAAK,CAACA,KAAK,EAAEuD,KAAK,IAAIvD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAIqD,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAIrD,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BG,YAAY,GAAG,0EAA0E;KAC1F,MAAM,IAAIpD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,mBAAmB;MAClC,IAAIpD,KAAK,CAACA,KAAK,EAAEwD,MAAM,EAAE;QACvB,MAAMC,gBAAgB,GAAGzC,MAAM,CAAC0C,MAAM,CAAC1D,KAAK,CAACA,KAAK,CAACwD,MAAM,CAAC,CAACG,IAAI,EAAE;QACjEP,YAAY,IAAI,aAAaK,gBAAgB,CAACG,IAAI,CAAC,IAAI,CAAC,EAAE;OAC3D,MAAM,IAAI5D,KAAK,CAACA,KAAK,EAAEuD,KAAK,EAAE;QAC7BH,YAAY,GAAGpD,KAAK,CAACA,KAAK,CAACuD,KAAK;;KAEnC,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACzE,MAAM,CAACkF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI7D,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,oCAAoC;KACpD,MAAM,IAAIpD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,2BAA2B;;IAG5C;IACA,MAAMU,aAAa,GAAG,IAAIpC,KAAK,CAAC0B,YAAY,CAAC;IAC5CU,aAAqB,CAACC,aAAa,GAAG/D,KAAK;IAC5C,OAAO5B,UAAU,CAAC,MAAM0F,aAAa,CAAC;EACxC;EAEQ7D,WAAWA,CAACD,KAAwB;IAC1C,IAAIoD,YAAY,GAAG,yBAAyB;IAC5C,MAAMC,QAAQ,GAAGrD,KAAK,CAACA,KAAK,EAAEsD,OAAO,IAAItD,KAAK,CAACA,KAAK,EAAEuD,KAAK;IAE3D,IAAIF,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAIrD,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BG,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIpD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACzE,MAAM,CAACkF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI7D,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAIpD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BG,YAAY,GAAG,iCAAiC;;IAGlD,OAAOhF,UAAU,CAAC,MAAM,IAAIsD,KAAK,CAAC0B,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAY,CAAA,G;qBApOUxF,aAAa,EAAAyF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb/F,aAAa;IAAAgG,OAAA,EAAbhG,aAAa,CAAAiG,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}