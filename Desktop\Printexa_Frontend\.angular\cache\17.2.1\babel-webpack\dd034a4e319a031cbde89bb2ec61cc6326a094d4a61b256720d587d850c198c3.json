{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/produit.service\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/common\";\nexport class ProduitDeleteComponent {\n  constructor(dialogRef, data, produitService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.produitService = produitService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.produitService.deleteProduit(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: error => {\n        console.error('<PERSON><PERSON>ur lors de la suppression du produit:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProduitDeleteComponent_Factory(t) {\n    return new (t || ProduitDeleteComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ProduitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProduitDeleteComponent,\n    selectors: [[\"app-produit-delete\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 54,\n    vars: 17,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [1, \"warning-icon\"], [\"mat-dialog-content\", \"\"], [1, \"warning-message\"], [1, \"product-details\"], [1, \"clearfix\"], [1, \"font-weight-bold\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"delete-btn\", 3, \"mat-dialog-close\", \"click\"], [\"mat-flat-button\", \"\", \"tabindex\", \"-1\", 1, \"cancel-btn\", 3, \"click\"]],\n    template: function ProduitDeleteComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" \\u00CAtes-vous s\\u00FBr de vouloir supprimer ce produit ? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"p\");\n        i0.ɵɵtext(8, \"Cette action est irr\\u00E9versible. Le produit sera d\\u00E9finitivement supprim\\u00E9.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"h4\");\n        i0.ɵɵtext(11, \"D\\u00E9tails du produit \\u00E0 supprimer :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"ul\", 6)(13, \"li\")(14, \"p\")(15, \"span\", 7);\n        i0.ɵɵtext(16, \"Type : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"li\")(19, \"p\")(20, \"span\", 7);\n        i0.ɵɵtext(21, \"Code produit : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"li\")(24, \"p\")(25, \"span\", 7);\n        i0.ɵɵtext(26, \"Description : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"li\")(29, \"p\")(30, \"span\", 7);\n        i0.ɵɵtext(31, \"Prix HT : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(32);\n        i0.ɵɵpipe(33, \"currency\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"li\")(35, \"p\")(36, \"span\", 7);\n        i0.ɵɵtext(37, \"Prix TTC : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(38);\n        i0.ɵɵpipe(39, \"currency\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"li\")(41, \"p\")(42, \"span\", 7);\n        i0.ɵɵtext(43, \"TVA : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(45, \"div\", 8)(46, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ProduitDeleteComponent_Template_button_click_46_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵelementStart(47, \"mat-icon\");\n        i0.ɵɵtext(48, \"delete\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(49, \" Supprimer \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ProduitDeleteComponent_Template_button_click_50_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵelementStart(51, \"mat-icon\");\n        i0.ɵɵtext(52, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(53, \" Annuler \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(17);\n        i0.ɵɵtextInterpolate(ctx.data.type);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.codeProd || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.description);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(33, 7, ctx.data.prixUnitaireHT, \"EUR\", \"symbol\", \"1.2-2\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(39, 12, ctx.data.prixUnitaireTTC, \"EUR\", \"symbol\", \"1.2-2\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"\", ctx.data.tva, \"%\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"mat-dialog-close\", 1);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i3.MatButton, MatDialogClose, CommonModule, i4.CurrencyPipe],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatDialogClose", "MatButtonModule", "CommonModule", "ProduitDeleteComponent", "constructor", "dialogRef", "data", "produitService", "onNoClick", "close", "confirmDelete", "deleteProduit", "id", "subscribe", "next", "error", "console", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ProduitService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProduitDeleteComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProduitDeleteComponent_Template_button_click_46_listener", "ProduitDeleteComponent_Template_button_click_50_listener", "ɵɵadvance", "ɵɵtextInterpolate", "type", "codeProd", "description", "ɵɵpipeBind4", "prixUnitaireHT", "prixUnitaireTTC", "ɵɵtextInterpolate1", "tva", "ɵɵproperty", "i3", "MatButton", "i4", "C<PERSON><PERSON>cyPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface DialogData {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-produit-delete',\r\n  templateUrl: './produit-delete.component.html',\r\n  styleUrls: ['./produit-delete.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatDialogTitle,\r\n    MatDialogContent,\r\n    MatDialogActions,\r\n    MatButtonModule,\r\n    MatDialogClose,\r\n    CommonModule,\r\n  ],\r\n})\r\nexport class ProduitDeleteComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ProduitDeleteComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public produitService: ProduitService\r\n  ) {}\r\n\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  confirmDelete(): void {\r\n    this.produitService.deleteProduit(this.data.id).subscribe({\r\n      next: () => {\r\n        this.dialogRef.close(true);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de la suppression du produit:', error);\r\n        this.dialogRef.close(false);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <h3 mat-dialog-title>\r\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n    Êtes-vous sûr de vouloir supprimer ce produit ?\r\n  </h3>\r\n\r\n  <div mat-dialog-content>\r\n    <div class=\"warning-message\">\r\n      <p>Cette action est irréversible. Le produit sera définitivement supprimé.</p>\r\n    </div>\r\n\r\n    <div class=\"product-details\">\r\n      <h4>Détails du produit à supprimer :</h4>\r\n      <ul class=\"clearfix\">\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Type : </span>{{data.type}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Code produit : </span>{{data.codeProd || 'Non défini'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Description : </span>{{data.description}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Prix HT : </span>{{data.prixUnitaireHT | currency:'EUR':'symbol':'1.2-2'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Prix TTC : </span>{{data.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">TVA : </span>{{data.tva}}%</p>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n\r\n  <div mat-dialog-actions class=\"mb-1\">\r\n    <button mat-flat-button color=\"warn\" [mat-dialog-close]=\"1\" (click)=\"confirmDelete()\" class=\"delete-btn\">\r\n      <mat-icon>delete</mat-icon>\r\n      Supprimer\r\n    </button>\r\n    <button mat-flat-button (click)=\"onNoClick()\" tabindex=\"-1\" class=\"cancel-btn\">\r\n      <mat-icon>cancel</mat-icon>\r\n      Annuler\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,0BAA0B;AAG5I,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,YAAY,QAAQ,iBAAiB;;;;;;AA0B9C,OAAM,MAAOC,sBAAsB;EACjCC,YACSC,SAA+C,EACtBC,IAAgB,EACzCC,cAA8B;IAF9B,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,cAAc,GAAdA,cAAc;EACpB;EAEHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,cAAc,CAACI,aAAa,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC,CAACC,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,KAAK,CAAC;MAC7B;KACD,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBArBUd,sBAAsB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGvBvB,eAAe,GAAAsB,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdrB,sBAAsB;IAAAsB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAT,EAAA,CAAAU,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC/BnChB,EAAA,CAAAkB,cAAA,aAAuB;QAEYlB,EAAA,CAAAmB,MAAA,cAAO;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QACjDpB,EAAA,CAAAmB,MAAA,kEACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAELpB,EAAA,CAAAkB,cAAA,aAAwB;QAEjBlB,EAAA,CAAAmB,MAAA,6FAAuE;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAGhFpB,EAAA,CAAAkB,cAAA,aAA6B;QACvBlB,EAAA,CAAAmB,MAAA,kDAAgC;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QACzCpB,EAAA,CAAAkB,cAAA,aAAqB;QAEiBlB,EAAA,CAAAmB,MAAA,eAAO;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEnEpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAiC;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAE/FpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,sBAAc;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAoB;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEjFpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,kBAAU;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAyD;;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAElHpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,mBAAW;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAA0D;;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEpHpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAMxEpB,EAAA,CAAAkB,cAAA,cAAqC;QACyBlB,EAAA,CAAAqB,UAAA,mBAAAC,yDAAA;UAAA,OAASL,GAAA,CAAAzB,aAAA,EAAe;QAAA,EAAC;QACnFQ,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,mBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAkB,cAAA,kBAA+E;QAAvDlB,EAAA,CAAAqB,UAAA,mBAAAE,yDAAA;UAAA,OAASN,GAAA,CAAA3B,SAAA,EAAW;QAAA,EAAC;QAC3CU,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,iBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;;;QA7B6CpB,EAAA,CAAAwB,SAAA,IAAa;QAAbxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAsC,IAAA,CAAa;QAGL1B,EAAA,CAAAwB,SAAA,GAAiC;QAAjCxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAuC,QAAA,sBAAiC;QAGlC3B,EAAA,CAAAwB,SAAA,GAAoB;QAApBxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAwC,WAAA,CAAoB;QAGxB5B,EAAA,CAAAwB,SAAA,GAAyD;QAAzDxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA6B,WAAA,QAAAZ,GAAA,CAAA7B,IAAA,CAAA0C,cAAA,4BAAyD;QAGxD9B,EAAA,CAAAwB,SAAA,GAA0D;QAA1DxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA6B,WAAA,SAAAZ,GAAA,CAAA7B,IAAA,CAAA2C,eAAA,4BAA0D;QAG/D/B,EAAA,CAAAwB,SAAA,GAAa;QAAbxB,EAAA,CAAAgC,kBAAA,KAAAf,GAAA,CAAA7B,IAAA,CAAA6C,GAAA,MAAa;QAO7BjC,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAAkC,UAAA,uBAAsB;;;mBDd3DvD,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBE,eAAe,EAAAoD,EAAA,CAAAC,SAAA,EACftD,cAAc,EACdE,YAAY,EAAAqD,EAAA,CAAAC,YAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}