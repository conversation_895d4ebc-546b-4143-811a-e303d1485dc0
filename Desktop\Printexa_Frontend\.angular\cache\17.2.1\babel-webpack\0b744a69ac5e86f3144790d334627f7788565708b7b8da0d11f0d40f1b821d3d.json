{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/produit.service\";\nexport class ProduitDeleteComponent {\n  constructor(dialogRef, data, produitService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.produitService = produitService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.produitService.deleteProduit(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: error => {\n        console.error('Erreur lors de la suppression du produit:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProduitDeleteComponent_Factory(t) {\n    return new (t || ProduitDeleteComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ProduitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProduitDeleteComponent,\n    selectors: [[\"app-produit-delete\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 2,\n    vars: 0,\n    template: function ProduitDeleteComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"p\");\n        i0.ɵɵtext(1, \"produit-delete works!\");\n        i0.ɵɵelementEnd();\n      }\n    },\n    dependencies: [MatButtonModule, CommonModule],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatButtonModule", "CommonModule", "ProduitDeleteComponent", "constructor", "dialogRef", "data", "produitService", "onNoClick", "close", "confirmDelete", "deleteProduit", "id", "subscribe", "next", "error", "console", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ProduitService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "template", "ProduitDeleteComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface DialogData {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-produit-delete',\r\n  templateUrl: './produit-delete.component.html',\r\n  styleUrls: ['./produit-delete.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatDialogTitle,\r\n    MatDialogContent,\r\n    MatDialogActions,\r\n    MatButtonModule,\r\n    MatDialogClose,\r\n    CommonModule,\r\n  ],\r\n})\r\nexport class ProduitDeleteComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ProduitDeleteComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public produitService: ProduitService\r\n  ) {}\r\n\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  confirmDelete(): void {\r\n    this.produitService.deleteProduit(this.data.id).subscribe({\r\n      next: () => {\r\n        this.dialogRef.close(true);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de la suppression du produit:', error);\r\n        this.dialogRef.close(false);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<p>produit-delete works!</p>\r\n"], "mappings": "AAAA,SAASA,eAAe,QAA0F,0BAA0B;AAG5I,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;;;;AA0B9C,OAAM,MAAOC,sBAAsB;EACjCC,YACSC,SAA+C,EACtBC,IAAgB,EACzCC,cAA8B;IAF9B,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,cAAc,GAAdA,cAAc;EACpB;EAEHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,cAAc,CAACI,aAAa,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC,CAACC,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,KAAK,CAAC;MAC7B;KACD,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBArBUd,sBAAsB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGvBnB,eAAe,GAAAkB,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdrB,sBAAsB;IAAAsB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAT,EAAA,CAAAU,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC9BnCf,EAAA,CAAAiB,cAAA,QAAG;QAAAjB,EAAA,CAAAkB,MAAA,4BAAqB;QAAAlB,EAAA,CAAAmB,YAAA,EAAI;;;mBDyBxBpC,eAAe,EAEfC,YAAY;IAAAoC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}