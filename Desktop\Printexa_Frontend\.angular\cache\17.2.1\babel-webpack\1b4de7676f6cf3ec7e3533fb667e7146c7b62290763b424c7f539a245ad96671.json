{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\n// import { environment } from '../../environments/environment';\nexport class ProduitService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Produits/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentProduitSubject = new BehaviorSubject(this.getProduitFromStorage());\n    this.currentProduit$ = this.currentProduitSubject.asObservable();\n  }\n  getProduitFromStorage() {\n    const produitData = localStorage.getItem('currentProduit');\n    return produitData ? JSON.parse(produitData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les produits\n  getAllProduits() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(produits => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(produits);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un produit par son ID\n  getProduitById(id) {\n    return this.http.get(`${this.baseUrl}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(produit => {\n      localStorage.setItem('currentProduit', JSON.stringify(produit));\n      this.currentProduitSubject.next(produit);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau produit\n  createProduit(produit) {\n    if (!produit.type || !produit.prixUnitaireHT) {\n      return throwError(() => new Error('Type and price are required'));\n    }\n    return this.http.post(this.baseUrl, produit, {\n      headers: this.getHeaders()\n    }).pipe(tap(newProduit => {\n      console.log('Produit créé avec succès:', newProduit);\n    }), catchError(this.handleError));\n  }\n  // Mettre à jour un produit\n  updateProduit(id, produit) {\n    return this.http.put(`${this.baseUrl}${id}`, produit, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Produit mis à jour avec succès');\n      this.clearCurrentProduit();\n    }), catchError(this.handleError));\n  }\n  // Supprimer un produit\n  deleteProduit(id) {\n    return this.http.delete(`${this.baseUrl}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Produit supprimé avec succès');\n      this.clearCurrentProduit();\n    }), catchError(this.handleError));\n  }\n  // Calculer le prix TTC\n  calculateTTC(prixHT, tva) {\n    return prixHT * (1 + tva / 100);\n  }\n  // Effacer le produit courant\n  clearCurrentProduit() {\n    localStorage.removeItem('currentProduit');\n    this.currentProduitSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  handleError(error) {\n    console.error('ProduitService error:', error);\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Product not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - product already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ProduitService_Factory(t) {\n    return new (t || ProduitService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ProduitService,\n    factory: ProduitService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "ProduitService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentProduitSubject", "getProduitFromStorage", "currentProduit$", "asObservable", "produitData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllProduits", "get", "headers", "pipe", "produits", "next", "error", "handleError", "getProduitById", "id", "produit", "setItem", "stringify", "createProduit", "type", "prixUnitaireHT", "Error", "post", "newProduit", "console", "log", "updateProduit", "put", "clearCurrentProduit", "deleteProduit", "delete", "calculateTTC", "prixHT", "tva", "removeItem", "data", "value", "getDialogData", "dialogData", "errorMessage", "apiError", "message", "title", "status", "navigate", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\produit.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError } from 'rxjs';\r\nimport { Produit, ProduitDTO, CreateProduitDTO, UpdateProduitDTO } from '../Model/Produit';\r\n\r\n// import { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProduitService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Produits/';\r\n  private currentProduitSubject: BehaviorSubject<Produit | null>;\r\n  public currentProduit$: Observable<Produit | null>;\r\n  dataChange = new BehaviorSubject<Produit[]>([]);\r\n  dialogData!: Produit;\r\n    isTblLoading = true;\r\n\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentProduitSubject = new BehaviorSubject<Produit | null>(this.getProduitFromStorage());\r\n    this.currentProduit$ = this.currentProduitSubject.asObservable();\r\n  }\r\n\r\n  private getProduitFromStorage(): Produit | null {\r\n    const produitData = localStorage.getItem('currentProduit');\r\n    return produitData ? JSON.parse(produitData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les produits\r\ngetAllProduits(): Observable<Produit[]> {\r\n    this.isTblLoading = true; // Activation du loading\r\n    return this.http.get<Produit[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(produits => {\r\n        this.isTblLoading = false; // Désactivation du loading\r\n        this.dataChange.next(produits);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false; // Désactivation en cas d'erreur\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n  // Récupérer un produit par son ID\r\n  getProduitById(id: string): Observable<ProduitDTO> {\r\n    return this.http.get<ProduitDTO>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(produit => {\r\n        localStorage.setItem('currentProduit', JSON.stringify(produit));\r\n        this.currentProduitSubject.next(produit);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau produit\r\n  createProduit(produit: CreateProduitDTO): Observable<ProduitDTO> {\r\n    if (!produit.type || !produit.prixUnitaireHT) {\r\n      return throwError(() => new Error('Type and price are required'));\r\n    }\r\n\r\n    return this.http.post<ProduitDTO>(this.baseUrl, produit, { headers: this.getHeaders() }).pipe(\r\n      tap(newProduit => {\r\n        console.log('Produit créé avec succès:', newProduit);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un produit\r\n  updateProduit(id: string, produit: UpdateProduitDTO): Observable<void> {\r\n    return this.http.put<void>(`${this.baseUrl}${id}`, produit, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        console.log('Produit mis à jour avec succès');\r\n        this.clearCurrentProduit();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer un produit\r\n  deleteProduit(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        console.log('Produit supprimé avec succès');\r\n        this.clearCurrentProduit();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Calculer le prix TTC\r\n  calculateTTC(prixHT: number, tva: number): number {\r\n    return prixHT * (1 + tva / 100);\r\n  }\r\n\r\n  // Effacer le produit courant\r\n  clearCurrentProduit(): void {\r\n    localStorage.removeItem('currentProduit');\r\n    this.currentProduitSubject.next(null);\r\n  }\r\nget data(): Produit[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    console.error('ProduitService error:', error);\r\n\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Forbidden';\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Product not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - product already exists';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Server error';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,QAAQ,MAAM;;;;AAG/E;AAKA,OAAM,MAAOC,cAAc;EASzBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAR5C,KAAAC,OAAO,GAAW,sCAAsC;IAGhE,KAAAC,UAAU,GAAG,IAAIT,eAAe,CAAY,EAAE,CAAC;IAE7C,KAAAU,YAAY,GAAG,IAAI;IAInB,IAAI,CAACC,qBAAqB,GAAG,IAAIX,eAAe,CAAiB,IAAI,CAACY,qBAAqB,EAAE,CAAC;IAC9F,IAAI,CAACC,eAAe,GAAG,IAAI,CAACF,qBAAqB,CAACG,YAAY,EAAE;EAClE;EAEQF,qBAAqBA,CAAA;IAC3B,MAAMG,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC1D,OAAOF,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,GAAG,IAAI;EACrD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIlB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIsB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACFC,cAAcA,CAAA;IACV,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAY,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAChFvB,GAAG,CAACwB,QAAQ,IAAG;MACb,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,QAAQ,CAAC;IAChC,CAAC,CAAC,EACFvB,UAAU,CAACyB,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EACA;EACAE,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAa,GAAG,IAAI,CAACf,OAAO,IAAIuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC5FvB,GAAG,CAAC8B,OAAO,IAAG;MACZhB,YAAY,CAACiB,OAAO,CAAC,gBAAgB,EAAEf,IAAI,CAACgB,SAAS,CAACF,OAAO,CAAC,CAAC;MAC/D,IAAI,CAACrB,qBAAqB,CAACgB,IAAI,CAACK,OAAO,CAAC;IAC1C,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,aAAaA,CAACH,OAAyB;IACrC,IAAI,CAACA,OAAO,CAACI,IAAI,IAAI,CAACJ,OAAO,CAACK,cAAc,EAAE;MAC5C,OAAOpC,UAAU,CAAC,MAAM,IAAIqC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE,OAAO,IAAI,CAAChC,IAAI,CAACiC,IAAI,CAAa,IAAI,CAAC/B,OAAO,EAAEwB,OAAO,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3FvB,GAAG,CAACsC,UAAU,IAAG;MACfC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEF,UAAU,CAAC;IACtD,CAAC,CAAC,EACFrC,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAc,aAAaA,CAACZ,EAAU,EAAEC,OAAyB;IACjD,OAAO,IAAI,CAAC1B,IAAI,CAACsC,GAAG,CAAO,GAAG,IAAI,CAACpC,OAAO,GAAGuB,EAAE,EAAE,EAAEC,OAAO,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC9FvB,GAAG,CAAC,MAAK;MACPuC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,IAAI,CAACG,mBAAmB,EAAE;IAC5B,CAAC,CAAC,EACF1C,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAiB,aAAaA,CAACf,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACyC,MAAM,CAAO,GAAG,IAAI,CAACvC,OAAO,IAAIuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACzFvB,GAAG,CAAC,MAAK;MACPuC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACG,mBAAmB,EAAE;IAC5B,CAAC,CAAC,EACF1C,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmB,YAAYA,CAACC,MAAc,EAAEC,GAAW;IACtC,OAAOD,MAAM,IAAI,CAAC,GAAGC,GAAG,GAAG,GAAG,CAAC;EACjC;EAEA;EACAL,mBAAmBA,CAAA;IACjB7B,YAAY,CAACmC,UAAU,CAAC,gBAAgB,CAAC;IACzC,IAAI,CAACxC,qBAAqB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACvC;EACF,IAAIyB,IAAIA,CAAA;IACJ,OAAO,IAAI,CAAC3C,UAAU,CAAC4C,KAAK;EAC9B;EAEAC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEQ1B,WAAWA,CAACD,KAAwB;IAC1Ca,OAAO,CAACb,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAE7C,IAAI4B,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAG7B,KAAK,CAACA,KAAK,EAAE8B,OAAO,IAAI9B,KAAK,CAACA,KAAK,EAAE+B,KAAK;IAE3D,IAAIF,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAI7B,KAAK,CAACgC,MAAM,KAAK,CAAC,EAAE;MAC7BJ,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAI5B,KAAK,CAACgC,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAI5B,KAAK,CAACgC,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACjD,MAAM,CAACsD,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIjC,KAAK,CAACgC,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,WAAW;KAC3B,MAAM,IAAI5B,KAAK,CAACgC,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAI5B,KAAK,CAACgC,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,mCAAmC;KACnD,MAAM,IAAI5B,KAAK,CAACgC,MAAM,IAAI,GAAG,EAAE;MAC9BJ,YAAY,GAAG,cAAc;;IAG/B,OAAOvD,UAAU,CAAC,MAAM,IAAIqC,KAAK,CAACkB,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAM,CAAA,G;qBApIU1D,cAAc,EAAA2D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAdjE,cAAc;IAAAkE,OAAA,EAAdlE,cAAc,CAAAmE,IAAA;IAAAC,UAAA,EAFb;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}