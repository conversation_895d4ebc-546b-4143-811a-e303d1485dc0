.content {
  padding: 20px;
}

.content-block {
  .block-header {
    margin-bottom: 20px;

    h3 {
      margin: 0;
      color: #333;
      font-weight: 500;
    }
  }
}

.search-card {
  margin-bottom: 20px;

  .search-container {
    display: flex;
    align-items: center;
    gap: 10px;

    .search-field {
      flex: 1;
    }

    .clear-button {
      margin-top: -10px;
    }
  }

  .search-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
    color: #666;

    mat-spinner {
      margin-right: 5px;
    }
  }

  .search-results {
    margin-top: 10px;
  }
}

.table-card {
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;

    mat-spinner {
      margin-bottom: 20px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }

  .no-data {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 40px;
    color: #999;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      margin-bottom: 16px;
    }

    p {
      margin: 0;
      font-size: 16px;
    }
  }

  .table-container {
    overflow-x: auto;

    .devis-table {
      width: 100%;

      th {
        font-weight: 600;
        color: #333;
      }

      .amount {
        text-align: right;
        font-family: 'Roboto Mono', monospace;
      }

      .mat-mdc-row {
        cursor: pointer;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        &.selected-row {
          background-color: #e3f2fd;
        }
      }
    }
  }
}

.details-card {
  margin-top: 20px;

  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .detail-item {
      padding: 8px 0;
      border-bottom: 1px solid #eee;

      strong {
        color: #333;
        margin-right: 8px;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .content {
    padding: 10px;
  }

  .search-container {
    flex-direction: column;
    align-items: stretch;

    .clear-button {
      align-self: flex-end;
      margin-top: 0;
    }
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .block-header .row {
    flex-direction: column;
    gap: 10px;

    .text-right {
      text-align: left !important;
    }
  }
}

// Snackbar styles
.snackbar-success {
  background-color: #4caf50;
  color: white;
}

.snackbar-error {
  background-color: #f44336;
  color: white;
}

.snackbar-info {
  background-color: #2196f3;
  color: white;
}