<div class="content">
  <div class="content-block">
    <div class="block-header">
      <div class="row">
        <div class="col-lg-8 col-sm-8 col-md-8">
          <h3>Gestion des Devis</h3>
        </div>
        <div class="col-lg-4 col-sm-4 col-md-4 text-right">
          <button
            mat-raised-button
            color="primary"
            (click)="onRefresh()"
            [disabled]="isLoading"
            matTooltip="Actualiser la liste">
            <mat-icon>refresh</mat-icon>
            Actualiser
          </button>
        </div>
      </div>
    </div>

    <!-- Barre de recherche -->
    <mat-card class="search-card">
      <mat-card-content>
        <div class="search-container">
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Rechercher par référence de devis</mat-label>
            <input
              matInput
              [formControl]="referenceControl"
              placeholder="Saisir la référence du devis..."
              autocomplete="off">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <button
            mat-icon-button
            (click)="onClearSearch()"
            matTooltip="Effacer la recherche"
            class="clear-button">
            <mat-icon>clear</mat-icon>
          </button>
        </div>

        <!-- Indicateur de recherche -->
        <div *ngIf="isSearching" class="search-indicator">
          <mat-spinner diameter="20"></mat-spinner>
          <span>Recherche en cours...</span>
        </div>

        <!-- Résultats de recherche -->
        <div *ngIf="referenceControl.value && !isSearching" class="search-results">
          <mat-chip-listbox>
            <mat-chip-option>
              {{ filteredDevis.length }} résultat(s) trouvé(s)
            </mat-chip-option>
          </mat-chip-listbox>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Tableau des devis -->
    <mat-card class="table-card">
      <mat-card-content>
        <div *ngIf="isLoading" class="loading-container">
          <mat-spinner></mat-spinner>
          <p>Chargement des devis...</p>
        </div>

        <div *ngIf="!isLoading && filteredDevis.length === 0" class="no-data">
          <mat-icon>inbox</mat-icon>
          <p>Aucun devis trouvé</p>
        </div>

        <div *ngIf="!isLoading && filteredDevis.length > 0" class="table-container">
          <table mat-table [dataSource]="filteredDevis" class="devis-table">

            <!-- Colonne Référence -->
            <ng-container matColumnDef="reference">
              <th mat-header-cell *matHeaderCellDef>Référence</th>
              <td mat-cell *matCellDef="let devis">
                <strong>{{ devis.reference }}</strong>
              </td>
            </ng-container>

            <!-- Colonne Date de création -->
            <ng-container matColumnDef="dateCreation">
              <th mat-header-cell *matHeaderCellDef>Date de création</th>
              <td mat-cell *matCellDef="let devis">
                {{ formatDate(devis.dateCreation) }}
              </td>
            </ng-container>

            <!-- Colonne Statut -->
            <ng-container matColumnDef="status">
              <th mat-header-cell *matHeaderCellDef>Statut</th>
              <td mat-cell *matCellDef="let devis">
                <mat-chip-listbox>
                  <mat-chip-option [color]="getStatusColor(devis.status)">
                    {{ devis.status }}
                  </mat-chip-option>
                </mat-chip-listbox>
              </td>
            </ng-container>

            <!-- Colonne Période de production -->
            <ng-container matColumnDef="periodeProd">
              <th mat-header-cell *matHeaderCellDef>Période Prod.</th>
              <td mat-cell *matCellDef="let devis">
                {{ devis.periodeProd }}
              </td>
            </ng-container>

            <!-- Colonne Total HT -->
            <ng-container matColumnDef="totalHT">
              <th mat-header-cell *matHeaderCellDef>Total HT</th>
              <td mat-cell *matCellDef="let devis" class="amount">
                {{ formatCurrency(devis.totalHT) }}
              </td>
            </ng-container>

            <!-- Colonne Total TTC -->
            <ng-container matColumnDef="totalTTC">
              <th mat-header-cell *matHeaderCellDef>Total TTC</th>
              <td mat-cell *matCellDef="let devis" class="amount">
                <strong>{{ formatCurrency(devis.totalTTC) }}</strong>
              </td>
            </ng-container>

            <!-- Colonne Client ID -->
            <ng-container matColumnDef="clientId">
              <th mat-header-cell *matHeaderCellDef>Client</th>
              <td mat-cell *matCellDef="let devis">
                {{ devis.clientId }}
              </td>
            </ng-container>

            <!-- Colonne Actions -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Actions</th>
              <td mat-cell *matCellDef="let devis">
                <button
                  mat-icon-button
                  (click)="onSelectDevis(devis)"
                  matTooltip="Sélectionner ce devis"
                  color="primary">
                  <mat-icon>visibility</mat-icon>
                </button>
                <button
                  mat-icon-button
                  matTooltip="Modifier le devis"
                  color="accent">
                  <mat-icon>edit</mat-icon>
                </button>
                <button
                  mat-icon-button
                  matTooltip="Supprimer le devis"
                  color="warn">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"
                [class.selected-row]="selectedDevis?.id === row.id"
                (click)="onSelectDevis(row)"></tr>
          </table>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Détails du devis sélectionné -->
    <mat-card *ngIf="selectedDevis" class="details-card">
      <mat-card-header>
        <mat-card-title>Détails du Devis {{ selectedDevis.reference }}</mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="details-grid">
          <div class="detail-item">
            <strong>ID:</strong> {{ selectedDevis.id }}
          </div>
          <div class="detail-item">
            <strong>Référence:</strong> {{ selectedDevis.reference }}
          </div>
          <div class="detail-item">
            <strong>Date de création:</strong> {{ formatDate(selectedDevis.dateCreation) }}
          </div>
          <div class="detail-item">
            <strong>Statut:</strong> {{ selectedDevis.status }}
          </div>
          <div class="detail-item">
            <strong>Période de production:</strong> {{ selectedDevis.periodeProd }}
          </div>
          <div class="detail-item">
            <strong>Total HT:</strong> {{ formatCurrency(selectedDevis.totalHT) }}
          </div>
          <div class="detail-item">
            <strong>Remise:</strong> {{ formatCurrency(selectedDevis.remise) }}
          </div>
          <div class="detail-item">
            <strong>Total TTC:</strong> {{ formatCurrency(selectedDevis.totalTTC) }}
          </div>
          <div class="detail-item">
            <strong>Total FODEC:</strong> {{ formatCurrency(selectedDevis.totalFodec) }}
          </div>
          <div class="detail-item">
            <strong>Total Timber:</strong> {{ selectedDevis.totalTimber }}
          </div>
          <div class="detail-item">
            <strong>Client ID:</strong> {{ selectedDevis.clientId }}
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
