.container {
  padding: 20px;
  max-width: 500px;

  h3[mat-dialog-title] {
    display: flex;
    align-items: center;
    color: #d32f2f;
    font-weight: 600;
    margin-bottom: 20px;
    font-size: 18px;

    .warning-icon {
      margin-right: 12px;
      font-size: 24px;
      color: #ff9800;
    }
  }

  .warning-message {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 12px;
    margin-bottom: 20px;
    color: #856404;

    p {
      margin: 0;
      font-weight: 500;
    }
  }

  .client-details {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 20px;

    h4 {
      color: #495057;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      margin-top: 0;
    }

    ul {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: 8px;
        padding: 4px 0;

        p {
          margin: 0;
          color: #6c757d;

          .font-weight-bold {
            color: #495057;
            font-weight: 600;
          }
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  div[mat-dialog-actions] {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
    margin-top: 20px;

    button {
      min-width: 120px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      mat-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      &.delete-btn {
        background-color: #d32f2f;
        color: white;

        &:hover {
          background-color: #b71c1c;
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);
        }
      }

      &.cancel-btn {
        background-color: #6c757d;
        color: white;

        &:hover {
          background-color: #5a6268;
        }

        &:focus {
          box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 480px) {
  .container {
    padding: 16px;

    h3[mat-dialog-title] {
      font-size: 16px;
      
      .warning-icon {
        font-size: 20px;
        margin-right: 8px;
      }
    }

    .client-details {
      padding: 12px;

      h4 {
        font-size: 14px;
      }

      ul li p {
        font-size: 14px;
      }
    }

    div[mat-dialog-actions] {
      flex-direction: column;
      gap: 8px;

      button {
        width: 100%;
        min-width: auto;
      }
    }
  }
}

// Animation pour l'apparition du dialogue
.container {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Style pour les éléments en surbrillance
.font-weight-bold {
  font-weight: 600 !important;
}

// Amélioration de l'accessibilité
button:focus {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}
