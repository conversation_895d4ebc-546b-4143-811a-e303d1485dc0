{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ClientModel } from '../../Model/Client';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/client.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/common\";\nfunction FormClientComponent_Conditional_1_mat_error_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le code client est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_1_mat_error_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le code doit contenir au moins 2 caract\\u00E8res \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_1_mat_error_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format invalide (ex: 1234567ABC123) \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_1_mat_error_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format d'email invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_1_mat_error_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Format de t\\u00E9l\\u00E9phone invalide \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"mat-icon\", 6);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function FormClientComponent_Conditional_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.dialogRef.close());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function FormClientComponent_Conditional_1_Template_form_ngSubmit_12_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.submit());\n    });\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"mat-form-field\", 13)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Code Client\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"input\", 14);\n    i0.ɵɵelementStart(19, \"mat-icon\", 15);\n    i0.ɵɵtext(20, \"badge\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(21, FormClientComponent_Conditional_1_mat_error_21_Template, 2, 0, \"mat-error\", 16)(22, FormClientComponent_Conditional_1_mat_error_22_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 12)(24, \"mat-form-field\", 13)(25, \"mat-label\");\n    i0.ɵɵtext(26, \"Raison Sociale\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"input\", 17);\n    i0.ɵɵelementStart(28, \"mat-icon\", 15);\n    i0.ɵɵtext(29, \"business\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(30, \"div\", 12)(31, \"mat-form-field\", 13)(32, \"mat-label\");\n    i0.ɵɵtext(33, \"Matricule Fiscal\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(34, \"input\", 18);\n    i0.ɵɵelementStart(35, \"mat-icon\", 15);\n    i0.ɵɵtext(36, \"receipt_long\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"mat-hint\");\n    i0.ɵɵtext(38, \"Format: 7 chiffres + 3 lettres + 3 chiffres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, FormClientComponent_Conditional_1_mat_error_39_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(40, \"div\", 12)(41, \"mat-form-field\", 13)(42, \"mat-label\");\n    i0.ɵɵtext(43, \"Email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(44, \"input\", 19);\n    i0.ɵɵelementStart(45, \"mat-icon\", 15);\n    i0.ɵɵtext(46, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(47, FormClientComponent_Conditional_1_mat_error_47_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 20)(49, \"mat-form-field\", 13)(50, \"mat-label\");\n    i0.ɵɵtext(51, \"T\\u00E9l\\u00E9phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(52, \"input\", 21);\n    i0.ɵɵelementStart(53, \"mat-icon\", 15);\n    i0.ɵɵtext(54, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"mat-hint\");\n    i0.ɵɵtext(56, \"Format: +216 XX XXX XXX ou 12 345 678\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, FormClientComponent_Conditional_1_mat_error_57_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(58, \"div\", 11)(59, \"div\", 20)(60, \"div\", 22)(61, \"button\", 23)(62, \"mat-icon\");\n    i0.ɵɵtext(63, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(64);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function FormClientComponent_Conditional_1_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.onNoClick());\n    });\n    i0.ɵɵelementStart(66, \"mat-icon\");\n    i0.ɵɵtext(67, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(68, \" Annuler \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_2_0;\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dialogTitle, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.clientForm);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.clientForm.get(\"code\")) == null ? null : tmp_2_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.clientForm.get(\"code\")) == null ? null : tmp_3_0.hasError(\"minlength\"));\n    i0.ɵɵadvance(17);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r0.clientForm.get(\"matFiscal\")) == null ? null : tmp_4_0.hasError(\"invalidMatFiscal\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r0.clientForm.get(\"email\")) == null ? null : tmp_5_0.hasError(\"email\"));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r0.clientForm.get(\"telephone\")) == null ? null : tmp_6_0.hasError(\"invalidPhone\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.clientForm.valid);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.action === \"add\" ? \"Ajouter\" : \"Modifier\", \" \");\n  }\n}\nfunction FormClientComponent_Conditional_2_a_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r11 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", \"mailto:\" + ctx_r11.client.email, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r11.client.email);\n  }\n}\nfunction FormClientComponent_Conditional_2_span_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Non d\\u00E9fini\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_2_a_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 32);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"href\", \"tel:\" + ctx_r13.client.telephone, i0.ɵɵsanitizeUrl);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r13.client.telephone);\n  }\n}\nfunction FormClientComponent_Conditional_2_span_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Non d\\u00E9fini\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormClientComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"mat-icon\", 6);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function FormClientComponent_Conditional_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.dialogRef.close());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 26)(13, \"div\", 11)(14, \"div\", 27)(15, \"div\", 28)(16, \"strong\");\n    i0.ɵɵtext(17, \"Code Client:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 28)(20, \"strong\");\n    i0.ɵɵtext(21, \"Raison Sociale:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 28)(24, \"strong\");\n    i0.ɵɵtext(25, \"Matricule Fiscal:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 27)(28, \"div\", 28)(29, \"strong\");\n    i0.ɵɵtext(30, \"Email:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(31, FormClientComponent_Conditional_2_a_31_Template, 2, 2, \"a\", 29)(32, FormClientComponent_Conditional_2_span_32_Template, 2, 0, \"span\", 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 28)(34, \"strong\");\n    i0.ɵɵtext(35, \"T\\u00E9l\\u00E9phone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(36, FormClientComponent_Conditional_2_a_36_Template, 2, 2, \"a\", 29)(37, FormClientComponent_Conditional_2_span_37_Template, 2, 0, \"span\", 16);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 11)(39, \"div\", 30)(40, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function FormClientComponent_Conditional_2_Template_button_click_40_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onNoClick());\n    });\n    i0.ɵɵelementStart(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43, \" Fermer \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails du client: \", ctx_r1.client.code, \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.code, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.syntax || \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.client.matFiscal || \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.client.email);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.client.telephone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.client.telephone);\n  }\n}\nexport class FormClientComponent {\n  constructor(dialogRef, data, clientService, fb) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.clientService = clientService;\n    this.fb = fb;\n    this.isDetails = false;\n    this.formControl = new UntypedFormControl('', [Validators.required]);\n    // Set the defaults\n    this.action = data.action;\n    if (this.action === 'edit') {\n      this.isDetails = false;\n      this.dialogTitle = `Modifier: ${data.client.code}`;\n      this.client = new ClientModel(data.client);\n      this.clientForm = this.createClientForm();\n    } else if (this.action === 'details') {\n      this.client = new ClientModel(data.client);\n      this.isDetails = true;\n    } else {\n      this.isDetails = false;\n      this.dialogTitle = 'Nouveau Client';\n      const blankObject = {};\n      this.client = new ClientModel(blankObject);\n      this.clientForm = this.createClientForm();\n    }\n  }\n  getErrorMessage() {\n    return this.formControl.hasError('required') ? 'Champ requis' : this.formControl.hasError('email') ? 'Format d\\'email invalide' : '';\n  }\n  createClientForm() {\n    return this.fb.group({\n      id: [this.client.id],\n      code: [this.client.code, [Validators.required, Validators.minLength(2), Validators.maxLength(20)]],\n      syntax: [this.client.syntax, [Validators.maxLength(100)]],\n      matFiscal: [this.client.matFiscal, [this.validateMatFiscal]],\n      email: [this.client.email, [Validators.email, Validators.maxLength(100)]],\n      telephone: [this.client.telephone, [this.validateTelephone]]\n    });\n  }\n  submit() {\n    if (this.clientForm?.valid) {\n      if (this.action === 'add') {\n        this.confirmAdd();\n      } else if (this.action === 'edit') {\n        this.confirmEdit();\n      }\n    }\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmAdd() {\n    if (this.clientForm?.valid) {\n      const formValue = this.clientForm.getRawValue();\n      // Validation supplémentaire côté client\n      if (!formValue.code || formValue.code.trim() === '') {\n        console.error('Code client requis');\n        return;\n      }\n      const createDto = {\n        code: formValue.code.trim(),\n        syntax: formValue.syntax?.trim(),\n        matFiscal: formValue.matFiscal?.trim(),\n        email: formValue.email?.trim(),\n        telephone: formValue.telephone?.trim()\n      };\n      console.log('Tentative de création avec les données:', createDto);\n      this.clientService.createClient(createDto).subscribe({\n        next: result => {\n          console.log('Client créé avec succès dans le composant:', result);\n          this.dialogRef.close(result);\n        },\n        error: error => {\n          console.error('Erreur lors de la création du client dans le composant:', error);\n          alert(`Erreur lors de la création: ${error.message}`);\n        }\n      });\n    } else {\n      console.error('Formulaire invalide:', this.clientForm?.errors);\n    }\n  }\n  confirmEdit() {\n    if (this.clientForm?.valid) {\n      const formValue = this.clientForm.getRawValue();\n      // Validation supplémentaire côté client\n      if (!formValue.code || formValue.code.trim() === '') {\n        console.error('Code client requis');\n        alert('Le code client est requis');\n        return;\n      }\n      // Créer l'UpdateClientDto en excluant explicitement l'id\n      const updateDto = {};\n      // Ajouter seulement les champs définis dans UpdateClientDto (pas l'id)\n      if (formValue.code !== undefined && formValue.code !== null) {\n        updateDto.code = formValue.code.trim();\n      }\n      if (formValue.syntax !== undefined && formValue.syntax !== null) {\n        updateDto.syntax = formValue.syntax.trim();\n      }\n      if (formValue.matFiscal !== undefined && formValue.matFiscal !== null) {\n        updateDto.matFiscal = formValue.matFiscal.trim();\n      }\n      if (formValue.email !== undefined && formValue.email !== null) {\n        updateDto.email = formValue.email.trim();\n      }\n      if (formValue.telephone !== undefined && formValue.telephone !== null) {\n        updateDto.telephone = formValue.telephone.trim();\n      }\n      console.log('FormValue complet:', formValue);\n      console.log('UpdateDto créé (sans ID):', updateDto);\n      console.log('ID du client (sera dans URL):', this.client.id);\n      // Supprimer les champs vides, undefined ou null\n      Object.keys(updateDto).forEach(key => {\n        const value = updateDto[key];\n        if (value === '' || value === undefined || value === null || typeof value === 'string' && value.trim() === '') {\n          delete updateDto[key];\n        }\n      });\n      // Vérifier qu'au moins un champ est fourni\n      if (Object.keys(updateDto).length === 0) {\n        alert('Aucune modification détectée');\n        return;\n      }\n      console.log('Tentative de mise à jour avec les données:', updateDto);\n      this.clientService.updateClient(this.client.id, updateDto).subscribe({\n        next: updatedClient => {\n          console.log('Client mis à jour avec succès:', updatedClient);\n          // Mettre à jour les données du service pour la liste\n          this.clientService.dialogData = updatedClient;\n          this.dialogRef.close(1); // Retourner 1 pour indiquer le succès\n        },\n        error: error => {\n          console.error('Erreur lors de la modification du client:', error);\n          // Message d'erreur plus détaillé et formaté\n          let errorMessage = 'Erreur lors de la modification du client';\n          if (error.message) {\n            errorMessage = error.message;\n            // Formater les messages d'erreur de validation pour un meilleur affichage\n            if (errorMessage.includes('\\n')) {\n              // Utiliser une boîte de dialogue plus appropriée pour les erreurs multi-lignes\n              const formattedMessage = errorMessage.replace(/\\n/g, '\\n• ');\n              console.error('Erreurs de validation:', formattedMessage);\n              // Créer un message plus lisible\n              const lines = errorMessage.split('\\n').filter(line => line.trim());\n              if (lines.length > 1) {\n                errorMessage = lines[0] + '\\n\\n' + lines.slice(1).map(line => '• ' + line).join('\\n');\n              }\n            }\n          }\n          // Utiliser une alerte ou un snackbar selon le type d'erreur\n          if (errorMessage.includes('validation') || errorMessage.includes('Détails:')) {\n            // Pour les erreurs de validation, afficher dans la console et une alerte simple\n            console.warn('Erreurs de validation détaillées:', errorMessage);\n            alert('Erreurs de validation détectées. Veuillez vérifier les champs du formulaire.\\n\\nConsultez la console pour plus de détails.');\n          } else {\n            alert(errorMessage);\n          }\n        }\n      });\n    } else {\n      console.error('Formulaire invalide:', this.clientForm?.errors);\n      alert('Veuillez corriger les erreurs dans le formulaire');\n    }\n  }\n  // Validation personnalisée pour le matricule fiscal tunisien\n  validateMatFiscal(control) {\n    const value = control.value?.trim();\n    if (!value) return null;\n    // Format: 7 chiffres + 3 lettres + 3 chiffres (ex: 1234567ABC123)\n    // Ou format simplifié: au moins 8 caractères alphanumériques\n    const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n    return matFiscalPattern.test(value.toUpperCase()) ? null : {\n      invalidMatFiscal: true\n    };\n  }\n  // Validation personnalisée pour le téléphone\n  validateTelephone(control) {\n    const value = control.value?.trim();\n    if (!value) return null;\n    // Accepte différents formats de téléphone (plus flexible)\n    const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n    return phonePattern.test(value) ? null : {\n      invalidPhone: true\n    };\n  }\n  // Méthode pour obtenir les messages d'erreur spécifiques\n  getFieldErrorMessage(fieldName) {\n    const field = this.clientForm?.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} est requis`;\n      if (field.errors['email']) return 'Format d\\'email invalide';\n      if (field.errors['minlength']) return `${fieldName} trop court`;\n      if (field.errors['maxlength']) return `${fieldName} trop long`;\n      if (field.errors['invalidMatFiscal']) return 'Format de matricule fiscal invalide';\n      if (field.errors['invalidPhone']) return 'Format de téléphone invalide';\n    }\n    return '';\n  }\n  static #_ = this.ɵfac = function FormClientComponent_Factory(t) {\n    return new (t || FormClientComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ClientService), i0.ɵɵdirectiveInject(i3.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormClientComponent,\n    selectors: [[\"app-form-client\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"addContainer\"], [\"class\", \"client-form\"], [1, \"client-form\"], [1, \"modalHeader\"], [1, \"editRowModal\"], [1, \"modalHeader\", \"clearfix\"], [1, \"client-icon\"], [1, \"modal-about\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Close dialog\", 1, \"modal-close-button\", 3, \"click\"], [\"mat-dialog-content\", \"\"], [1, \"register-form\", \"m-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"appearance\", \"outline\", 1, \"example-full-width\", \"mb-3\"], [\"matInput\", \"\", \"formControlName\", \"code\", \"required\", \"\", \"placeholder\", \"Ex: CLI001\"], [\"matSuffix\", \"\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"syntax\", \"placeholder\", \"Nom de l'entreprise\"], [\"matInput\", \"\", \"formControlName\", \"matFiscal\", \"placeholder\", \"Ex: 1234567ABC123\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"matInput\", \"\", \"formControlName\", \"telephone\", \"placeholder\", \"Ex: +216 12 345 678\"], [1, \"example-button-row\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"btn-space\", 3, \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [1, \"client-details\"], [1, \"details-content\", \"m-4\"], [1, \"col-md-6\"], [1, \"detail-item\"], [3, \"href\", 4, \"ngIf\"], [1, \"col-md-12\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [3, \"href\"]],\n    template: function FormClientComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, FormClientComponent_Conditional_1_Template, 69, 9, \"div\", 1)(2, FormClientComponent_Conditional_2_Template, 44, 8);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(1, !ctx.isDetails ? 1 : 2);\n      }\n    },\n    dependencies: [MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatHint, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatCardModule, CommonModule, i8.NgIf],\n    styles: [\".addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 24px;\\n  border-bottom: 1px solid #e0e0e0;\\n  background-color: #f5f5f5;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  padding: 0;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .client-icon[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .client-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 12px;\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%]:hover, .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-full-width[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  margin-top: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  color: #ddd;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-icon[matSuffix][_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-hint[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-error[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-error[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #f44336;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #f44336 !important;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .mat-form-field.ng-valid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .mat-form-field.ng-valid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #4caf50 !important;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  padding: 8px 0;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n  margin-right: 8px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n  text-decoration: none;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n.addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n@media (max-width: 768px) {\\n  .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .client-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .client-details[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.mat-error[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_shake 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  25% {\\n    transform: translateX(-5px);\\n  }\\n  75% {\\n    transform: translateX(5px);\\n  }\\n}\\n.client-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\nbutton[mat-raised-button][_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\nbutton[mat-raised-button][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\nmat-form-field[_ngcontent-%COMP%]:has(input[formControlName=code])   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\nmat-form-field[_ngcontent-%COMP%]:has(input[formControlName=syntax])   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #ff9800;\\n}\\nmat-form-field[_ngcontent-%COMP%]:has(input[formControlName=matFiscal])   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #9c27b0;\\n}\\nmat-form-field[_ngcontent-%COMP%]:has(input[formControlName=email])   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\nmat-form-field[_ngcontent-%COMP%]:has(input[formControlName=telephone])   .mat-icon[matSuffix][_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.mat-form-field.ng-valid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  border-color: #4caf50 !important;\\n}\\n.mat-form-field.ng-invalid.ng-touched[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  border-color: #f44336 !important;\\n}\\n\\nbutton[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #1976d2;\\n  outline-offset: 2px;\\n}\\n\\ninput[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n}\\n\\n.addContainer[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "UntypedFormControl", "Validators", "FormsModule", "ReactiveFormsModule", "ClientModel", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatIconModule", "MatButtonModule", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "FormClientComponent_Conditional_1_Template_button_click_8_listener", "ɵɵrestoreView", "_r8", "ctx_r7", "ɵɵnextContext", "ɵɵresetView", "dialogRef", "close", "FormClientComponent_Conditional_1_Template_form_ngSubmit_12_listener", "ctx_r9", "submit", "ɵɵelement", "ɵɵtemplate", "FormClientComponent_Conditional_1_mat_error_21_Template", "FormClientComponent_Conditional_1_mat_error_22_Template", "FormClientComponent_Conditional_1_mat_error_39_Template", "FormClientComponent_Conditional_1_mat_error_47_Template", "FormClientComponent_Conditional_1_mat_error_57_Template", "FormClientComponent_Conditional_1_Template_button_click_65_listener", "ctx_r10", "onNoClick", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "dialogTitle", "ɵɵproperty", "clientForm", "tmp_2_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_3_0", "tmp_4_0", "tmp_5_0", "tmp_6_0", "valid", "action", "ctx_r11", "client", "email", "ɵɵsanitizeUrl", "ɵɵtextInterpolate", "ctx_r13", "telephone", "FormClientComponent_Conditional_2_Template_button_click_8_listener", "_r16", "ctx_r15", "FormClientComponent_Conditional_2_a_31_Template", "FormClientComponent_Conditional_2_span_32_Template", "FormClientComponent_Conditional_2_a_36_Template", "FormClientComponent_Conditional_2_span_37_Template", "FormClientComponent_Conditional_2_Template_button_click_40_listener", "ctx_r17", "ctx_r1", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "FormClientComponent", "constructor", "data", "clientService", "fb", "isDetails", "formControl", "required", "createClientForm", "blankObject", "getErrorMessage", "group", "id", "<PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "validateMatFiscal", "validateTelephone", "confirmAdd", "confirmEdit", "formValue", "getRawValue", "trim", "console", "error", "createDto", "log", "createClient", "subscribe", "next", "result", "alert", "message", "errors", "updateDto", "undefined", "Object", "keys", "for<PERSON>ach", "key", "value", "length", "updateClient", "updatedClient", "dialogData", "errorMessage", "includes", "formattedMessage", "replace", "lines", "split", "filter", "line", "slice", "map", "join", "warn", "control", "matFiscalPattern", "test", "toUpperCase", "invalidMatFiscal", "phonePattern", "invalidPhone", "getFieldErrorMessage", "fieldName", "field", "touched", "_", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ClientService", "i3", "UntypedFormBuilder", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FormClientComponent_Template", "rf", "ctx", "FormClientComponent_Conditional_1_Template", "FormClientComponent_Conditional_2_Template", "ɵɵconditional", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "ɵNgNoValidate", "DefaultValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "FormGroupDirective", "FormControlName", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\form-client\\form-client.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\form-client\\form-client.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { ClientService } from '../../services/client.service';\nimport { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { Client, ClientModel, CreateClientSimpleDto, UpdateClientDto } from '../../Model/Client';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { CommonModule } from '@angular/common';\n\nexport interface DialogData {\n  id: string;\n  action: string;\n  client: Client;\n}\n\n@Component({\n  selector: 'app-form-client',\n  templateUrl: './form-client.component.html',\n  styleUrls: ['./form-client.component.scss'],\n  standalone: true,\n  imports: [\n    MatButtonModule,\n    MatIconModule,\n    FormsModule,\n    ReactiveFormsModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatCardModule,\n    CommonModule,\n  ],\n})\nexport class FormClientComponent {\n  action: string;\n  dialogTitle?: string;\n  isDetails = false;\n  clientForm?: UntypedFormGroup;\n  client: ClientModel;\n\n  constructor(\n    public dialogRef: MatDialogRef<FormClientComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\n    public clientService: ClientService,\n    private fb: UntypedFormBuilder\n  ) {\n    // Set the defaults\n    this.action = data.action;\n    if (this.action === 'edit') {\n      this.isDetails = false;\n      this.dialogTitle = `Modifier: ${data.client.code}`;\n      this.client = new ClientModel(data.client);\n      this.clientForm = this.createClientForm();\n    } else if (this.action === 'details') {\n      this.client = new ClientModel(data.client);\n      this.isDetails = true;\n    } else {\n      this.isDetails = false;\n      this.dialogTitle = 'Nouveau Client';\n      const blankObject = {} as Client;\n      this.client = new ClientModel(blankObject);\n      this.clientForm = this.createClientForm();\n    }\n  }\n\n  formControl = new UntypedFormControl('', [\n    Validators.required,\n  ]);\n\n  getErrorMessage() {\n    return this.formControl.hasError('required')\n      ? 'Champ requis'\n      : this.formControl.hasError('email')\n      ? 'Format d\\'email invalide'\n      : '';\n  }\n\n  createClientForm(): UntypedFormGroup {\n    return this.fb.group({\n      id: [this.client.id],\n      code: [this.client.code, [Validators.required, Validators.minLength(2), Validators.maxLength(20)]],\n      syntax: [this.client.syntax, [Validators.maxLength(100)]],\n      matFiscal: [this.client.matFiscal, [this.validateMatFiscal]],\n      email: [this.client.email, [Validators.email, Validators.maxLength(100)]],\n      telephone: [this.client.telephone, [this.validateTelephone]],\n    });\n  }\n\n  submit() {\n    if (this.clientForm?.valid) {\n      if (this.action === 'add') {\n        this.confirmAdd();\n      } else if (this.action === 'edit') {\n        this.confirmEdit();\n      }\n    }\n  }\n\n  onNoClick(): void {\n    this.dialogRef.close();\n  }\n\n  public confirmAdd(): void {\n    if (this.clientForm?.valid) {\n      const formValue = this.clientForm.getRawValue();\n      \n      // Validation supplémentaire côté client\n      if (!formValue.code || formValue.code.trim() === '') {\n        console.error('Code client requis');\n        return;\n      }\n\n      const createDto: CreateClientSimpleDto = {\n        code: formValue.code.trim(),\n        syntax: formValue.syntax?.trim(),\n        matFiscal: formValue.matFiscal?.trim(),\n        email: formValue.email?.trim(),\n        telephone: formValue.telephone?.trim()\n      };\n\n      console.log('Tentative de création avec les données:', createDto);\n\n      this.clientService.createClient(createDto).subscribe({\n        next: (result) => {\n          console.log('Client créé avec succès dans le composant:', result);\n          this.dialogRef.close(result);\n        },\n        error: (error) => {\n          console.error('Erreur lors de la création du client dans le composant:', error);\n          alert(`Erreur lors de la création: ${error.message}`);\n        }\n      });\n    } else {\n      console.error('Formulaire invalide:', this.clientForm?.errors);\n    }\n  }\n\n  public confirmEdit(): void {\n    if (this.clientForm?.valid) {\n      const formValue = this.clientForm.getRawValue();\n\n      // Validation supplémentaire côté client\n      if (!formValue.code || formValue.code.trim() === '') {\n        console.error('Code client requis');\n        alert('Le code client est requis');\n        return;\n      }\n\n      // Créer l'UpdateClientDto en excluant explicitement l'id\n      const updateDto: UpdateClientDto = {};\n\n      // Ajouter seulement les champs définis dans UpdateClientDto (pas l'id)\n      if (formValue.code !== undefined && formValue.code !== null) {\n        updateDto.code = formValue.code.trim();\n      }\n      if (formValue.syntax !== undefined && formValue.syntax !== null) {\n        updateDto.syntax = formValue.syntax.trim();\n      }\n      if (formValue.matFiscal !== undefined && formValue.matFiscal !== null) {\n        updateDto.matFiscal = formValue.matFiscal.trim();\n      }\n      if (formValue.email !== undefined && formValue.email !== null) {\n        updateDto.email = formValue.email.trim();\n      }\n      if (formValue.telephone !== undefined && formValue.telephone !== null) {\n        updateDto.telephone = formValue.telephone.trim();\n      }\n\n      console.log('FormValue complet:', formValue);\n      console.log('UpdateDto créé (sans ID):', updateDto);\n      console.log('ID du client (sera dans URL):', this.client.id);\n\n      // Supprimer les champs vides, undefined ou null\n      Object.keys(updateDto).forEach(key => {\n        const value = updateDto[key as keyof UpdateClientDto];\n        if (value === '' || value === undefined || value === null || (typeof value === 'string' && value.trim() === '')) {\n          delete updateDto[key as keyof UpdateClientDto];\n        }\n      });\n\n      // Vérifier qu'au moins un champ est fourni\n      if (Object.keys(updateDto).length === 0) {\n        alert('Aucune modification détectée');\n        return;\n      }\n\n      console.log('Tentative de mise à jour avec les données:', updateDto);\n\n      this.clientService.updateClient(this.client.id, updateDto).subscribe({\n        next: (updatedClient) => {\n          console.log('Client mis à jour avec succès:', updatedClient);\n          // Mettre à jour les données du service pour la liste\n          this.clientService.dialogData = updatedClient;\n          this.dialogRef.close(1); // Retourner 1 pour indiquer le succès\n        },\n        error: (error) => {\n          console.error('Erreur lors de la modification du client:', error);\n\n          // Message d'erreur plus détaillé et formaté\n          let errorMessage = 'Erreur lors de la modification du client';\n          if (error.message) {\n            errorMessage = error.message;\n\n            // Formater les messages d'erreur de validation pour un meilleur affichage\n            if (errorMessage.includes('\\n')) {\n              // Utiliser une boîte de dialogue plus appropriée pour les erreurs multi-lignes\n              const formattedMessage = errorMessage.replace(/\\n/g, '\\n• ');\n              console.error('Erreurs de validation:', formattedMessage);\n\n              // Créer un message plus lisible\n              const lines = errorMessage.split('\\n').filter(line => line.trim());\n              if (lines.length > 1) {\n                errorMessage = lines[0] + '\\n\\n' + lines.slice(1).map(line => '• ' + line).join('\\n');\n              }\n            }\n          }\n\n          // Utiliser une alerte ou un snackbar selon le type d'erreur\n          if (errorMessage.includes('validation') || errorMessage.includes('Détails:')) {\n            // Pour les erreurs de validation, afficher dans la console et une alerte simple\n            console.warn('Erreurs de validation détaillées:', errorMessage);\n            alert('Erreurs de validation détectées. Veuillez vérifier les champs du formulaire.\\n\\nConsultez la console pour plus de détails.');\n          } else {\n            alert(errorMessage);\n          }\n        }\n      });\n    } else {\n      console.error('Formulaire invalide:', this.clientForm?.errors);\n      alert('Veuillez corriger les erreurs dans le formulaire');\n    }\n  }\n\n  // Validation personnalisée pour le matricule fiscal tunisien\n  validateMatFiscal(control: UntypedFormControl) {\n    const value = control.value?.trim();\n    if (!value) return null;\n\n    // Format: 7 chiffres + 3 lettres + 3 chiffres (ex: 1234567ABC123)\n    // Ou format simplifié: au moins 8 caractères alphanumériques\n    const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n    return matFiscalPattern.test(value.toUpperCase()) ? null : { invalidMatFiscal: true };\n  }\n\n  // Validation personnalisée pour le téléphone\n  validateTelephone(control: UntypedFormControl) {\n    const value = control.value?.trim();\n    if (!value) return null;\n\n    // Accepte différents formats de téléphone (plus flexible)\n    const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n    return phonePattern.test(value) ? null : { invalidPhone: true };\n  }\n\n  // Méthode pour obtenir les messages d'erreur spécifiques\n  getFieldErrorMessage(fieldName: string): string {\n    const field = this.clientForm?.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) return `${fieldName} est requis`;\n      if (field.errors['email']) return 'Format d\\'email invalide';\n      if (field.errors['minlength']) return `${fieldName} trop court`;\n      if (field.errors['maxlength']) return `${fieldName} trop long`;\n      if (field.errors['invalidMatFiscal']) return 'Format de matricule fiscal invalide';\n      if (field.errors['invalidPhone']) return 'Format de téléphone invalide';\n    }\n    return '';\n  }\n}\n", "<div class=\"addContainer\">\n  @if (!isDetails) {\n  <div class=\"client-form\">\n    <div class=\"modalHeader\">\n      <div class=\"editRowModal\">\n        <div class=\"modalHeader clearfix\">\n          <mat-icon class=\"client-icon\">person</mat-icon>\n          <div class=\"modal-about\">\n            {{dialogTitle}}\n          </div>\n        </div>\n      </div>\n      <button mat-icon-button (click)=\"dialogRef.close()\" class=\"modal-close-button\" aria-label=\"Close dialog\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n    <div mat-dialog-content>\n      <form class=\"register-form m-4\" [formGroup]=\"clientForm!\" (ngSubmit)=\"submit()\">\n        <div class=\"row\">\n          <!-- Code Client -->\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\n              <mat-label>Code Client</mat-label>\n              <input matInput formControlName=\"code\" required placeholder=\"Ex: CLI001\">\n              <mat-icon matSuffix>badge</mat-icon>\n              <mat-error *ngIf=\"clientForm!.get('code')?.hasError('required')\">\n                Le code client est requis\n              </mat-error>\n              <mat-error *ngIf=\"clientForm!.get('code')?.hasError('minlength')\">\n                Le code doit contenir au moins 2 caractères\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <!-- Raison Sociale -->\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\n              <mat-label>Raison Sociale</mat-label>\n              <input matInput formControlName=\"syntax\" placeholder=\"Nom de l'entreprise\">\n              <mat-icon matSuffix>business</mat-icon>\n            </mat-form-field>\n          </div>\n\n          <!-- Matricule Fiscal -->\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\n              <mat-label>Matricule Fiscal</mat-label>\n              <input matInput formControlName=\"matFiscal\" placeholder=\"Ex: 1234567ABC123\">\n              <mat-icon matSuffix>receipt_long</mat-icon>\n              <mat-hint>Format: 7 chiffres + 3 lettres + 3 chiffres</mat-hint>\n              <mat-error *ngIf=\"clientForm!.get('matFiscal')?.hasError('invalidMatFiscal')\">\n                Format invalide (ex: 1234567ABC123)\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <!-- Email -->\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\n              <mat-label>Email</mat-label>\n              <input matInput type=\"email\" formControlName=\"email\" placeholder=\"<EMAIL>\">\n              <mat-icon matSuffix>email</mat-icon>\n              <mat-error *ngIf=\"clientForm!.get('email')?.hasError('email')\">\n                Format d'email invalide\n              </mat-error>\n            </mat-form-field>\n          </div>\n\n          <!-- Téléphone -->\n          <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\n              <mat-label>Téléphone</mat-label>\n              <input matInput formControlName=\"telephone\" placeholder=\"Ex: +216 12 345 678\">\n              <mat-icon matSuffix>phone</mat-icon>\n              <mat-hint>Format: +216 XX XXX XXX ou 12 345 678</mat-hint>\n              <mat-error *ngIf=\"clientForm!.get('telephone')?.hasError('invalidPhone')\">\n                Format de téléphone invalide\n              </mat-error>\n            </mat-form-field>\n          </div>\n        </div>\n\n        <!-- Boutons d'action -->\n        <div class=\"row\">\n          <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\n            <div class=\"example-button-row\">\n              <button mat-raised-button color=\"primary\" type=\"submit\" \n                      [disabled]=\"!clientForm!.valid\" class=\"btn-space\">\n                <mat-icon>save</mat-icon>\n                {{action === 'add' ? 'Ajouter' : 'Modifier'}}\n              </button>\n              <button mat-raised-button color=\"warn\" (click)=\"onNoClick()\" type=\"button\">\n                <mat-icon>cancel</mat-icon>\n                Annuler\n              </button>\n            </div>\n          </div>\n        </div>\n      </form>\n    </div>\n  </div>\n  } @else {\n  <!-- Mode détails (lecture seule) -->\n  <div class=\"client-details\">\n    <div class=\"modalHeader\">\n      <div class=\"editRowModal\">\n        <div class=\"modalHeader clearfix\">\n          <mat-icon class=\"client-icon\">person</mat-icon>\n          <div class=\"modal-about\">\n            Détails du client: {{client.code}}\n          </div>\n        </div>\n      </div>\n      <button mat-icon-button (click)=\"dialogRef.close()\" class=\"modal-close-button\" aria-label=\"Close dialog\">\n        <mat-icon>close</mat-icon>\n      </button>\n    </div>\n    <div mat-dialog-content>\n      <div class=\"details-content m-4\">\n        <div class=\"row\">\n          <div class=\"col-md-6\">\n            <div class=\"detail-item\">\n              <strong>Code Client:</strong> {{client.code}}\n            </div>\n            <div class=\"detail-item\">\n              <strong>Raison Sociale:</strong> {{client.syntax || 'Non défini'}}\n            </div>\n            <div class=\"detail-item\">\n              <strong>Matricule Fiscal:</strong> {{client.matFiscal || 'Non défini'}}\n            </div>\n          </div>\n          <div class=\"col-md-6\">\n            <div class=\"detail-item\">\n              <strong>Email:</strong> \n              <a *ngIf=\"client.email\" [href]=\"'mailto:' + client.email\">{{client.email}}</a>\n              <span *ngIf=\"!client.email\">Non défini</span>\n            </div>\n            <div class=\"detail-item\">\n              <strong>Téléphone:</strong> \n              <a *ngIf=\"client.telephone\" [href]=\"'tel:' + client.telephone\">{{client.telephone}}</a>\n              <span *ngIf=\"!client.telephone\">Non défini</span>\n            </div>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-12\">\n            <button mat-raised-button color=\"primary\" (click)=\"onNoClick()\">\n              <mat-icon>close</mat-icon>\n              Fermer\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n  }\n</div>\n"], "mappings": "AAAA,SAASA,eAAe,QAAsB,0BAA0B;AAGxE,SAASC,kBAAkB,EAAEC,UAAU,EAAwCC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvI,SAAiBC,WAAW,QAAgD,oBAAoB;AAChG,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;;;;;ICehCC,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAE,MAAA,yDACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAoBZH,EAAA,CAAAC,cAAA,gBAA8E;IAC5ED,EAAA,CAAAE,MAAA,4CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAUZH,EAAA,CAAAC,cAAA,gBAA+D;IAC7DD,EAAA,CAAAE,MAAA,gCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA0E;IACxED,EAAA,CAAAE,MAAA,+CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IA3ExBH,EAAA,CAAAC,cAAA,aAAyB;IAIaD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAyG;IAAjFD,EAAA,CAAAI,UAAA,mBAAAC,mEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IACjDZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG9BH,EAAA,CAAAC,cAAA,cAAwB;IACoCD,EAAA,CAAAI,UAAA,sBAAAS,qEAAA;MAAAb,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAO,MAAA,GAAAd,EAAA,CAAAS,aAAA;MAAA,OAAYT,EAAA,CAAAU,WAAA,CAAAI,MAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC7Ef,EAAA,CAAAC,cAAA,eAAiB;IAIAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAgB,SAAA,iBAAyE;IACzEhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAiB,UAAA,KAAAC,uDAAA,wBAEY,KAAAC,uDAAA;IAIdnB,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACrCH,EAAA,CAAAgB,SAAA,iBAA2E;IAC3EhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAK3CH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAgB,SAAA,iBAA4E;IAC5EhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3CH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,mDAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChEH,EAAA,CAAAiB,UAAA,KAAAG,uDAAA,wBAEY;IACdpB,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC5BH,EAAA,CAAAgB,SAAA,iBAAsF;IACtFhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAiB,UAAA,KAAAI,uDAAA,wBAEY;IACdrB,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAA0D;IAE3CD,EAAA,CAAAE,MAAA,2BAAS;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAChCH,EAAA,CAAAgB,SAAA,iBAA8E;IAC9EhB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1DH,EAAA,CAAAiB,UAAA,KAAAK,uDAAA,wBAEY;IACdtB,EAAA,CAAAG,YAAA,EAAiB;IAKrBH,EAAA,CAAAC,cAAA,eAAiB;IAKCD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2E;IAApCD,EAAA,CAAAI,UAAA,mBAAAmB,oEAAA;MAAAvB,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAiB,OAAA,GAAAxB,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAc,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAC1DzB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;IAtFXH,EAAA,CAAA0B,SAAA,GACF;IADE1B,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAC,WAAA,MACF;IAQ4B7B,EAAA,CAAA0B,SAAA,GAAyB;IAAzB1B,EAAA,CAAA8B,UAAA,cAAAF,MAAA,CAAAG,UAAA,CAAyB;IAQrC/B,EAAA,CAAA0B,SAAA,GAAmD;IAAnD1B,EAAA,CAAA8B,UAAA,UAAAE,OAAA,GAAAJ,MAAA,CAAAG,UAAA,CAAAE,GAAA,2BAAAD,OAAA,CAAAE,QAAA,aAAmD;IAGnDlC,EAAA,CAAA0B,SAAA,EAAoD;IAApD1B,EAAA,CAAA8B,UAAA,UAAAK,OAAA,GAAAP,MAAA,CAAAG,UAAA,CAAAE,GAAA,2BAAAE,OAAA,CAAAD,QAAA,cAAoD;IAsBpDlC,EAAA,CAAA0B,SAAA,IAAgE;IAAhE1B,EAAA,CAAA8B,UAAA,UAAAM,OAAA,GAAAR,MAAA,CAAAG,UAAA,CAAAE,GAAA,gCAAAG,OAAA,CAAAF,QAAA,qBAAgE;IAYhElC,EAAA,CAAA0B,SAAA,GAAiD;IAAjD1B,EAAA,CAAA8B,UAAA,UAAAO,OAAA,GAAAT,MAAA,CAAAG,UAAA,CAAAE,GAAA,4BAAAI,OAAA,CAAAH,QAAA,UAAiD;IAajDlC,EAAA,CAAA0B,SAAA,IAA4D;IAA5D1B,EAAA,CAAA8B,UAAA,UAAAQ,OAAA,GAAAV,MAAA,CAAAG,UAAA,CAAAE,GAAA,gCAAAK,OAAA,CAAAJ,QAAA,iBAA4D;IAYhElC,EAAA,CAAA0B,SAAA,GAA+B;IAA/B1B,EAAA,CAAA8B,UAAA,cAAAF,MAAA,CAAAG,UAAA,CAAAQ,KAAA,CAA+B;IAErCvC,EAAA,CAAA0B,SAAA,GACF;IADE1B,EAAA,CAAA2B,kBAAA,MAAAC,MAAA,CAAAY,MAAA,yCACF;;;;;IA4CAxC,EAAA,CAAAC,cAAA,YAA0D;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAAtDH,EAAA,CAAA8B,UAAA,qBAAAW,OAAA,CAAAC,MAAA,CAAAC,KAAA,EAAA3C,EAAA,CAAA4C,aAAA,CAAiC;IAAC5C,EAAA,CAAA0B,SAAA,EAAgB;IAAhB1B,EAAA,CAAA6C,iBAAA,CAAAJ,OAAA,CAAAC,MAAA,CAAAC,KAAA,CAAgB;;;;;IAC1E3C,EAAA,CAAAC,cAAA,WAA4B;IAAAD,EAAA,CAAAE,MAAA,sBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAI7CH,EAAA,CAAAC,cAAA,YAA+D;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;IAA3DH,EAAA,CAAA8B,UAAA,kBAAAgB,OAAA,CAAAJ,MAAA,CAAAK,SAAA,EAAA/C,EAAA,CAAA4C,aAAA,CAAkC;IAAC5C,EAAA,CAAA0B,SAAA,EAAoB;IAApB1B,EAAA,CAAA6C,iBAAA,CAAAC,OAAA,CAAAJ,MAAA,CAAAK,SAAA,CAAoB;;;;;IACnF/C,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,sBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;;IArC7DH,EAAA,CAAAC,cAAA,cAA4B;IAIUD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAyG;IAAjFD,EAAA,CAAAI,UAAA,mBAAA4C,mEAAA;MAAAhD,EAAA,CAAAM,aAAA,CAAA2C,IAAA;MAAA,MAAAC,OAAA,GAAAlD,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAAwC,OAAA,CAAAvC,SAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IACjDZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG9BH,EAAA,CAAAC,cAAA,cAAwB;IAKND,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAChC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACnC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACrC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAsB;IAEVD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACvBH,EAAA,CAAAiB,UAAA,KAAAkC,+CAAA,gBAA8E,KAAAC,kDAAA;IAEhFpD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,4BAAU;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC3BH,EAAA,CAAAiB,UAAA,KAAAoC,+CAAA,gBAAuF,KAAAC,kDAAA;IAEzFtD,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAiB;IAE6BD,EAAA,CAAAI,UAAA,mBAAAmD,oEAAA;MAAAvD,EAAA,CAAAM,aAAA,CAAA2C,IAAA;MAAA,MAAAO,OAAA,GAAAxD,EAAA,CAAAS,aAAA;MAAA,OAAST,EAAA,CAAAU,WAAA,CAAA8C,OAAA,CAAA/B,SAAA,EAAW;IAAA,EAAC;IAC7DzB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAxCTH,EAAA,CAAA0B,SAAA,GACF;IADE1B,EAAA,CAAA2B,kBAAA,8BAAA8B,MAAA,CAAAf,MAAA,CAAAgB,IAAA,MACF;IAYkC1D,EAAA,CAAA0B,SAAA,IAChC;IADgC1B,EAAA,CAAA2B,kBAAA,MAAA8B,MAAA,CAAAf,MAAA,CAAAgB,IAAA,MAChC;IAEmC1D,EAAA,CAAA0B,SAAA,GACnC;IADmC1B,EAAA,CAAA2B,kBAAA,MAAA8B,MAAA,CAAAf,MAAA,CAAAiB,MAAA,2BACnC;IAEqC3D,EAAA,CAAA0B,SAAA,GACrC;IADqC1B,EAAA,CAAA2B,kBAAA,MAAA8B,MAAA,CAAAf,MAAA,CAAAkB,SAAA,2BACrC;IAKM5D,EAAA,CAAA0B,SAAA,GAAkB;IAAlB1B,EAAA,CAAA8B,UAAA,SAAA2B,MAAA,CAAAf,MAAA,CAAAC,KAAA,CAAkB;IACf3C,EAAA,CAAA0B,SAAA,EAAmB;IAAnB1B,EAAA,CAAA8B,UAAA,UAAA2B,MAAA,CAAAf,MAAA,CAAAC,KAAA,CAAmB;IAItB3C,EAAA,CAAA0B,SAAA,GAAsB;IAAtB1B,EAAA,CAAA8B,UAAA,SAAA2B,MAAA,CAAAf,MAAA,CAAAK,SAAA,CAAsB;IACnB/C,EAAA,CAAA0B,SAAA,EAAuB;IAAvB1B,EAAA,CAAA8B,UAAA,UAAA2B,MAAA,CAAAf,MAAA,CAAAK,SAAA,CAAuB;;;AD1G5C,OAAM,MAAOc,mBAAmB;EAO9BC,YACSnD,SAA4C,EACnBoD,IAAgB,EACzCC,aAA4B,EAC3BC,EAAsB;IAHvB,KAAAtD,SAAS,GAATA,SAAS;IACgB,KAAAoD,IAAI,GAAJA,IAAI;IAC7B,KAAAC,aAAa,GAAbA,aAAa;IACZ,KAAAC,EAAE,GAAFA,EAAE;IARZ,KAAAC,SAAS,GAAG,KAAK;IA6BjB,KAAAC,WAAW,GAAG,IAAI9E,kBAAkB,CAAC,EAAE,EAAE,CACvCC,UAAU,CAAC8E,QAAQ,CACpB,CAAC;IArBA;IACA,IAAI,CAAC5B,MAAM,GAAGuB,IAAI,CAACvB,MAAM;IACzB,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAI,CAAC0B,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrC,WAAW,GAAG,aAAakC,IAAI,CAACrB,MAAM,CAACgB,IAAI,EAAE;MAClD,IAAI,CAAChB,MAAM,GAAG,IAAIjD,WAAW,CAACsE,IAAI,CAACrB,MAAM,CAAC;MAC1C,IAAI,CAACX,UAAU,GAAG,IAAI,CAACsC,gBAAgB,EAAE;KAC1C,MAAM,IAAI,IAAI,CAAC7B,MAAM,KAAK,SAAS,EAAE;MACpC,IAAI,CAACE,MAAM,GAAG,IAAIjD,WAAW,CAACsE,IAAI,CAACrB,MAAM,CAAC;MAC1C,IAAI,CAACwB,SAAS,GAAG,IAAI;KACtB,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACrC,WAAW,GAAG,gBAAgB;MACnC,MAAMyC,WAAW,GAAG,EAAY;MAChC,IAAI,CAAC5B,MAAM,GAAG,IAAIjD,WAAW,CAAC6E,WAAW,CAAC;MAC1C,IAAI,CAACvC,UAAU,GAAG,IAAI,CAACsC,gBAAgB,EAAE;;EAE7C;EAMAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACJ,WAAW,CAACjC,QAAQ,CAAC,UAAU,CAAC,GACxC,cAAc,GACd,IAAI,CAACiC,WAAW,CAACjC,QAAQ,CAAC,OAAO,CAAC,GAClC,0BAA0B,GAC1B,EAAE;EACR;EAEAmC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACJ,EAAE,CAACO,KAAK,CAAC;MACnBC,EAAE,EAAE,CAAC,IAAI,CAAC/B,MAAM,CAAC+B,EAAE,CAAC;MACpBf,IAAI,EAAE,CAAC,IAAI,CAAChB,MAAM,CAACgB,IAAI,EAAE,CAACpE,UAAU,CAAC8E,QAAQ,EAAE9E,UAAU,CAACoF,SAAS,CAAC,CAAC,CAAC,EAAEpF,UAAU,CAACqF,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC;MAClGhB,MAAM,EAAE,CAAC,IAAI,CAACjB,MAAM,CAACiB,MAAM,EAAE,CAACrE,UAAU,CAACqF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACzDf,SAAS,EAAE,CAAC,IAAI,CAAClB,MAAM,CAACkB,SAAS,EAAE,CAAC,IAAI,CAACgB,iBAAiB,CAAC,CAAC;MAC5DjC,KAAK,EAAE,CAAC,IAAI,CAACD,MAAM,CAACC,KAAK,EAAE,CAACrD,UAAU,CAACqD,KAAK,EAAErD,UAAU,CAACqF,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;MACzE5B,SAAS,EAAE,CAAC,IAAI,CAACL,MAAM,CAACK,SAAS,EAAE,CAAC,IAAI,CAAC8B,iBAAiB,CAAC;KAC5D,CAAC;EACJ;EAEA9D,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACgB,UAAU,EAAEQ,KAAK,EAAE;MAC1B,IAAI,IAAI,CAACC,MAAM,KAAK,KAAK,EAAE;QACzB,IAAI,CAACsC,UAAU,EAAE;OAClB,MAAM,IAAI,IAAI,CAACtC,MAAM,KAAK,MAAM,EAAE;QACjC,IAAI,CAACuC,WAAW,EAAE;;;EAGxB;EAEAtD,SAASA,CAAA;IACP,IAAI,CAACd,SAAS,CAACC,KAAK,EAAE;EACxB;EAEOkE,UAAUA,CAAA;IACf,IAAI,IAAI,CAAC/C,UAAU,EAAEQ,KAAK,EAAE;MAC1B,MAAMyC,SAAS,GAAG,IAAI,CAACjD,UAAU,CAACkD,WAAW,EAAE;MAE/C;MACA,IAAI,CAACD,SAAS,CAACtB,IAAI,IAAIsB,SAAS,CAACtB,IAAI,CAACwB,IAAI,EAAE,KAAK,EAAE,EAAE;QACnDC,OAAO,CAACC,KAAK,CAAC,oBAAoB,CAAC;QACnC;;MAGF,MAAMC,SAAS,GAA0B;QACvC3B,IAAI,EAAEsB,SAAS,CAACtB,IAAI,CAACwB,IAAI,EAAE;QAC3BvB,MAAM,EAAEqB,SAAS,CAACrB,MAAM,EAAEuB,IAAI,EAAE;QAChCtB,SAAS,EAAEoB,SAAS,CAACpB,SAAS,EAAEsB,IAAI,EAAE;QACtCvC,KAAK,EAAEqC,SAAS,CAACrC,KAAK,EAAEuC,IAAI,EAAE;QAC9BnC,SAAS,EAAEiC,SAAS,CAACjC,SAAS,EAAEmC,IAAI;OACrC;MAEDC,OAAO,CAACG,GAAG,CAAC,yCAAyC,EAAED,SAAS,CAAC;MAEjE,IAAI,CAACrB,aAAa,CAACuB,YAAY,CAACF,SAAS,CAAC,CAACG,SAAS,CAAC;QACnDC,IAAI,EAAGC,MAAM,IAAI;UACfP,OAAO,CAACG,GAAG,CAAC,4CAA4C,EAAEI,MAAM,CAAC;UACjE,IAAI,CAAC/E,SAAS,CAACC,KAAK,CAAC8E,MAAM,CAAC;QAC9B,CAAC;QACDN,KAAK,EAAGA,KAAK,IAAI;UACfD,OAAO,CAACC,KAAK,CAAC,yDAAyD,EAAEA,KAAK,CAAC;UAC/EO,KAAK,CAAC,+BAA+BP,KAAK,CAACQ,OAAO,EAAE,CAAC;QACvD;OACD,CAAC;KACH,MAAM;MACLT,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACrD,UAAU,EAAE8D,MAAM,CAAC;;EAElE;EAEOd,WAAWA,CAAA;IAChB,IAAI,IAAI,CAAChD,UAAU,EAAEQ,KAAK,EAAE;MAC1B,MAAMyC,SAAS,GAAG,IAAI,CAACjD,UAAU,CAACkD,WAAW,EAAE;MAE/C;MACA,IAAI,CAACD,SAAS,CAACtB,IAAI,IAAIsB,SAAS,CAACtB,IAAI,CAACwB,IAAI,EAAE,KAAK,EAAE,EAAE;QACnDC,OAAO,CAACC,KAAK,CAAC,oBAAoB,CAAC;QACnCO,KAAK,CAAC,2BAA2B,CAAC;QAClC;;MAGF;MACA,MAAMG,SAAS,GAAoB,EAAE;MAErC;MACA,IAAId,SAAS,CAACtB,IAAI,KAAKqC,SAAS,IAAIf,SAAS,CAACtB,IAAI,KAAK,IAAI,EAAE;QAC3DoC,SAAS,CAACpC,IAAI,GAAGsB,SAAS,CAACtB,IAAI,CAACwB,IAAI,EAAE;;MAExC,IAAIF,SAAS,CAACrB,MAAM,KAAKoC,SAAS,IAAIf,SAAS,CAACrB,MAAM,KAAK,IAAI,EAAE;QAC/DmC,SAAS,CAACnC,MAAM,GAAGqB,SAAS,CAACrB,MAAM,CAACuB,IAAI,EAAE;;MAE5C,IAAIF,SAAS,CAACpB,SAAS,KAAKmC,SAAS,IAAIf,SAAS,CAACpB,SAAS,KAAK,IAAI,EAAE;QACrEkC,SAAS,CAAClC,SAAS,GAAGoB,SAAS,CAACpB,SAAS,CAACsB,IAAI,EAAE;;MAElD,IAAIF,SAAS,CAACrC,KAAK,KAAKoD,SAAS,IAAIf,SAAS,CAACrC,KAAK,KAAK,IAAI,EAAE;QAC7DmD,SAAS,CAACnD,KAAK,GAAGqC,SAAS,CAACrC,KAAK,CAACuC,IAAI,EAAE;;MAE1C,IAAIF,SAAS,CAACjC,SAAS,KAAKgD,SAAS,IAAIf,SAAS,CAACjC,SAAS,KAAK,IAAI,EAAE;QACrE+C,SAAS,CAAC/C,SAAS,GAAGiC,SAAS,CAACjC,SAAS,CAACmC,IAAI,EAAE;;MAGlDC,OAAO,CAACG,GAAG,CAAC,oBAAoB,EAAEN,SAAS,CAAC;MAC5CG,OAAO,CAACG,GAAG,CAAC,2BAA2B,EAAEQ,SAAS,CAAC;MACnDX,OAAO,CAACG,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC5C,MAAM,CAAC+B,EAAE,CAAC;MAE5D;MACAuB,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACI,OAAO,CAACC,GAAG,IAAG;QACnC,MAAMC,KAAK,GAAGN,SAAS,CAACK,GAA4B,CAAC;QACrD,IAAIC,KAAK,KAAK,EAAE,IAAIA,KAAK,KAAKL,SAAS,IAAIK,KAAK,KAAK,IAAI,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAClB,IAAI,EAAE,KAAK,EAAG,EAAE;UAC/G,OAAOY,SAAS,CAACK,GAA4B,CAAC;;MAElD,CAAC,CAAC;MAEF;MACA,IAAIH,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACO,MAAM,KAAK,CAAC,EAAE;QACvCV,KAAK,CAAC,8BAA8B,CAAC;QACrC;;MAGFR,OAAO,CAACG,GAAG,CAAC,4CAA4C,EAAEQ,SAAS,CAAC;MAEpE,IAAI,CAAC9B,aAAa,CAACsC,YAAY,CAAC,IAAI,CAAC5D,MAAM,CAAC+B,EAAE,EAAEqB,SAAS,CAAC,CAACN,SAAS,CAAC;QACnEC,IAAI,EAAGc,aAAa,IAAI;UACtBpB,OAAO,CAACG,GAAG,CAAC,gCAAgC,EAAEiB,aAAa,CAAC;UAC5D;UACA,IAAI,CAACvC,aAAa,CAACwC,UAAU,GAAGD,aAAa;UAC7C,IAAI,CAAC5F,SAAS,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC;QACDwE,KAAK,EAAGA,KAAK,IAAI;UACfD,OAAO,CAACC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;UAEjE;UACA,IAAIqB,YAAY,GAAG,0CAA0C;UAC7D,IAAIrB,KAAK,CAACQ,OAAO,EAAE;YACjBa,YAAY,GAAGrB,KAAK,CAACQ,OAAO;YAE5B;YACA,IAAIa,YAAY,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;cAC/B;cACA,MAAMC,gBAAgB,GAAGF,YAAY,CAACG,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC;cAC5DzB,OAAO,CAACC,KAAK,CAAC,wBAAwB,EAAEuB,gBAAgB,CAAC;cAEzD;cACA,MAAME,KAAK,GAAGJ,YAAY,CAACK,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC9B,IAAI,EAAE,CAAC;cAClE,IAAI2B,KAAK,CAACR,MAAM,GAAG,CAAC,EAAE;gBACpBI,YAAY,GAAGI,KAAK,CAAC,CAAC,CAAC,GAAG,MAAM,GAAGA,KAAK,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,GAAG,CAACF,IAAI,IAAI,IAAI,GAAGA,IAAI,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;;;;UAK3F;UACA,IAAIV,YAAY,CAACC,QAAQ,CAAC,YAAY,CAAC,IAAID,YAAY,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;YAC5E;YACAvB,OAAO,CAACiC,IAAI,CAAC,mCAAmC,EAAEX,YAAY,CAAC;YAC/Dd,KAAK,CAAC,4HAA4H,CAAC;WACpI,MAAM;YACLA,KAAK,CAACc,YAAY,CAAC;;QAEvB;OACD,CAAC;KACH,MAAM;MACLtB,OAAO,CAACC,KAAK,CAAC,sBAAsB,EAAE,IAAI,CAACrD,UAAU,EAAE8D,MAAM,CAAC;MAC9DF,KAAK,CAAC,kDAAkD,CAAC;;EAE7D;EAEA;EACAf,iBAAiBA,CAACyC,OAA2B;IAC3C,MAAMjB,KAAK,GAAGiB,OAAO,CAACjB,KAAK,EAAElB,IAAI,EAAE;IACnC,IAAI,CAACkB,KAAK,EAAE,OAAO,IAAI;IAEvB;IACA;IACA,MAAMkB,gBAAgB,GAAG,6CAA6C;IACtE,OAAOA,gBAAgB,CAACC,IAAI,CAACnB,KAAK,CAACoB,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG;MAAEC,gBAAgB,EAAE;IAAI,CAAE;EACvF;EAEA;EACA5C,iBAAiBA,CAACwC,OAA2B;IAC3C,MAAMjB,KAAK,GAAGiB,OAAO,CAACjB,KAAK,EAAElB,IAAI,EAAE;IACnC,IAAI,CAACkB,KAAK,EAAE,OAAO,IAAI;IAEvB;IACA,MAAMsB,YAAY,GAAG,6BAA6B;IAClD,OAAOA,YAAY,CAACH,IAAI,CAACnB,KAAK,CAAC,GAAG,IAAI,GAAG;MAAEuB,YAAY,EAAE;IAAI,CAAE;EACjE;EAEA;EACAC,oBAAoBA,CAACC,SAAiB;IACpC,MAAMC,KAAK,GAAG,IAAI,CAAC/F,UAAU,EAAEE,GAAG,CAAC4F,SAAS,CAAC;IAC7C,IAAIC,KAAK,EAAEjC,MAAM,IAAIiC,KAAK,CAACC,OAAO,EAAE;MAClC,IAAID,KAAK,CAACjC,MAAM,CAAC,UAAU,CAAC,EAAE,OAAO,GAAGgC,SAAS,aAAa;MAC9D,IAAIC,KAAK,CAACjC,MAAM,CAAC,OAAO,CAAC,EAAE,OAAO,0BAA0B;MAC5D,IAAIiC,KAAK,CAACjC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGgC,SAAS,aAAa;MAC/D,IAAIC,KAAK,CAACjC,MAAM,CAAC,WAAW,CAAC,EAAE,OAAO,GAAGgC,SAAS,YAAY;MAC9D,IAAIC,KAAK,CAACjC,MAAM,CAAC,kBAAkB,CAAC,EAAE,OAAO,qCAAqC;MAClF,IAAIiC,KAAK,CAACjC,MAAM,CAAC,cAAc,CAAC,EAAE,OAAO,8BAA8B;;IAEzE,OAAO,EAAE;EACX;EAAC,QAAAmC,CAAA,G;qBAzOUnE,mBAAmB,EAAA7D,EAAA,CAAAiI,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAnI,EAAA,CAAAiI,iBAAA,CASpB7I,eAAe,GAAAY,EAAA,CAAAiI,iBAAA,CAAAG,EAAA,CAAAC,aAAA,GAAArI,EAAA,CAAAiI,iBAAA,CAAAK,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UATd3E,mBAAmB;IAAA4E,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA3I,EAAA,CAAA4I,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QClChClJ,EAAA,CAAAC,cAAA,aAA0B;QACxBD,EAAA,CAAAiB,UAAA,IAAAmI,0CAAA,kBAoGC,IAAAC,0CAAA;QAuDHrJ,EAAA,CAAAG,YAAA,EAAM;;;QA3JJH,EAAA,CAAA0B,SAAA,EAoGC;QApGD1B,EAAA,CAAAsJ,aAAA,KAAAH,GAAA,CAAAjF,SAAA,SAoGC;;;mBD7ECpE,eAAe,EAAAyJ,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACf5J,aAAa,EAAA6J,EAAA,CAAAC,OAAA,EACbpK,WAAW,EAAA+I,EAAA,CAAAsB,aAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,eAAA,EAAAxB,EAAA,CAAAyB,oBAAA,EAAAzB,EAAA,CAAA0B,iBAAA,EACXxK,mBAAmB,EAAA8I,EAAA,CAAA2B,kBAAA,EAAA3B,EAAA,CAAA4B,eAAA,EACnBtK,kBAAkB,EAAAuK,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,QAAA,EAAAJ,EAAA,CAAAK,SAAA,EAClB7K,cAAc,EAAA8K,EAAA,CAAAC,QAAA,EACdhL,aAAa,EACbK,YAAY,EAAA4K,EAAA,CAAAC,IAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}