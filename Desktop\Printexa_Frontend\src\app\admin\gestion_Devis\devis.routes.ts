import { Routes } from "@angular/router";
import { DevisComponent } from './devis/devis.component';
import { FormDevisComponent } from "./form-devis/form-devis.component";

export const DEVIS_ROUTES: Routes = [
  {
    path: '',
    children: [
      { path: 'list', component: DevisComponent },
      { path: 'add', component: FormDevisComponent },
      { path: 'edit/:id', component: FormDevisComponent },
      { path: '**', redirectTo: 'list' }
    ]
  }
];