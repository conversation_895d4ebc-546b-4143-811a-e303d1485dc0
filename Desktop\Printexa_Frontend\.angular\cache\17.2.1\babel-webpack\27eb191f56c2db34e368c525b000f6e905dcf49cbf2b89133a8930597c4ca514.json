{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/produit.service\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/icon\";\nimport * as i5 from \"@angular/common\";\nexport class ProduitDeleteComponent {\n  constructor(dialogRef, data, produitService) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.produitService = produitService;\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmDelete() {\n    this.produitService.deleteProduit(this.data.id).subscribe({\n      next: () => {\n        this.dialogRef.close(true);\n      },\n      error: error => {\n        console.error('Erreur lors de la suppression du produit:', error);\n        this.dialogRef.close(false);\n      }\n    });\n  }\n  static #_ = this.ɵfac = function ProduitDeleteComponent_Factory(t) {\n    return new (t || ProduitDeleteComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ProduitService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProduitDeleteComponent,\n    selectors: [[\"app-produit-delete\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 54,\n    vars: 17,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [1, \"warning-icon\"], [\"mat-dialog-content\", \"\"], [1, \"warning-message\"], [1, \"product-details\"], [1, \"clearfix\"], [1, \"font-weight-bold\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"delete-btn\", 3, \"mat-dialog-close\", \"click\"], [\"mat-flat-button\", \"\", \"tabindex\", \"-1\", 1, \"cancel-btn\", 3, \"click\"]],\n    template: function ProduitDeleteComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" \\u00CAtes-vous s\\u00FBr de vouloir supprimer ce produit ? \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"p\");\n        i0.ɵɵtext(8, \"Cette action est irr\\u00E9versible. Le produit sera d\\u00E9finitivement supprim\\u00E9.\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(9, \"div\", 5)(10, \"h4\");\n        i0.ɵɵtext(11, \"D\\u00E9tails du produit \\u00E0 supprimer :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"ul\", 6)(13, \"li\")(14, \"p\")(15, \"span\", 7);\n        i0.ɵɵtext(16, \"Type : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(18, \"li\")(19, \"p\")(20, \"span\", 7);\n        i0.ɵɵtext(21, \"Code produit : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(22);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(23, \"li\")(24, \"p\")(25, \"span\", 7);\n        i0.ɵɵtext(26, \"Description : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(27);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(28, \"li\")(29, \"p\")(30, \"span\", 7);\n        i0.ɵɵtext(31, \"Prix HT : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(32);\n        i0.ɵɵpipe(33, \"currency\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(34, \"li\")(35, \"p\")(36, \"span\", 7);\n        i0.ɵɵtext(37, \"Prix TTC : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(38);\n        i0.ɵɵpipe(39, \"currency\");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(40, \"li\")(41, \"p\")(42, \"span\", 7);\n        i0.ɵɵtext(43, \"TVA : \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(44);\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(45, \"div\", 8)(46, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function ProduitDeleteComponent_Template_button_click_46_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵelementStart(47, \"mat-icon\");\n        i0.ɵɵtext(48, \"delete\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(49, \" Supprimer \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(50, \"button\", 10);\n        i0.ɵɵlistener(\"click\", function ProduitDeleteComponent_Template_button_click_50_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵelementStart(51, \"mat-icon\");\n        i0.ɵɵtext(52, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(53, \" Annuler \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(17);\n        i0.ɵɵtextInterpolate(ctx.data.type);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.codeProd || \"Non d\\u00E9fini\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(ctx.data.description);\n        i0.ɵɵadvance(5);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(33, 7, ctx.data.prixUnitaireHT, \"EUR\", \"symbol\", \"1.2-2\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(39, 12, ctx.data.prixUnitaireTTC, \"EUR\", \"symbol\", \"1.2-2\"));\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate1(\"\", ctx.data.tva, \"%\");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"mat-dialog-close\", 1);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i3.MatButton, MatIconModule, i4.MatIcon, MatDialogClose, CommonModule, i5.CurrencyPipe],\n    styles: [\".container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 500px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #d32f2f;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 24px;\\n  color: #ff9800;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 4px;\\n  padding: 12px;\\n  margin-bottom: 20px;\\n  color: #856404;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 4px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  list-style: none;\\n  padding: 0;\\n  margin: 0;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 8px;\\n  padding: 4px 0;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #6c757d;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   .font-weight-bold[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 600;\\n}\\n.container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n  margin-top: 20px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 120px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%] {\\n  background-color: #d32f2f;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #b71c1c;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);\\n}\\n\\n@media (max-width: 480px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n    font-size: 20px;\\n    margin-right: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n  .container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .container[_ngcontent-%COMP%]   .product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 14px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n.container[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.font-weight-bold[_ngcontent-%COMP%] {\\n  font-weight: 600 !important;\\n}\\n\\nbutton[_ngcontent-%COMP%]:focus {\\n  outline: 2px solid #007bff;\\n  outline-offset: 2px;\\n}\\n\\n.product-details[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]:has(.font-weight-bold:contains(\\\"Prix\\\"))   .font-weight-bold[_ngcontent-%COMP%]    + *[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-weight: 600;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatDialogClose", "MatButtonModule", "MatIconModule", "CommonModule", "ProduitDeleteComponent", "constructor", "dialogRef", "data", "produitService", "onNoClick", "close", "confirmDelete", "deleteProduit", "id", "subscribe", "next", "error", "console", "_", "i0", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ProduitService", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "ProduitDeleteComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProduitDeleteComponent_Template_button_click_46_listener", "ProduitDeleteComponent_Template_button_click_50_listener", "ɵɵadvance", "ɵɵtextInterpolate", "type", "codeProd", "description", "ɵɵpipeBind4", "prixUnitaireHT", "prixUnitaireTTC", "ɵɵtextInterpolate1", "tva", "ɵɵproperty", "i3", "MatButton", "i4", "MatIcon", "i5", "C<PERSON><PERSON>cyPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\produit-delete\\produit-delete.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface DialogData {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-produit-delete',\r\n  templateUrl: './produit-delete.component.html',\r\n  styleUrls: ['./produit-delete.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatDialogTitle,\r\n    MatDialogContent,\r\n    MatDialogActions,\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    MatDialogClose,\r\n    CommonModule,\r\n  ],\r\n})\r\nexport class ProduitDeleteComponent {\r\n  constructor(\r\n    public dialogRef: MatDialogRef<ProduitDeleteComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public produitService: ProduitService\r\n  ) {}\r\n\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  confirmDelete(): void {\r\n    this.produitService.deleteProduit(this.data.id).subscribe({\r\n      next: () => {\r\n        this.dialogRef.close(true);\r\n      },\r\n      error: (error) => {\r\n        console.error('Erreur lors de la suppression du produit:', error);\r\n        this.dialogRef.close(false);\r\n      }\r\n    });\r\n  }\r\n}\r\n", "<div class=\"container\">\r\n  <h3 mat-dialog-title>\r\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\r\n    Êtes-vous sûr de vouloir supprimer ce produit ?\r\n  </h3>\r\n\r\n  <div mat-dialog-content>\r\n    <div class=\"warning-message\">\r\n      <p>Cette action est irréversible. Le produit sera définitivement supprimé.</p>\r\n    </div>\r\n\r\n    <div class=\"product-details\">\r\n      <h4>Détails du produit à supprimer :</h4>\r\n      <ul class=\"clearfix\">\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Type : </span>{{data.type}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Code produit : </span>{{data.codeProd || 'Non défini'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Description : </span>{{data.description}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Prix HT : </span>{{data.prixUnitaireHT | currency:'EUR':'symbol':'1.2-2'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">Prix TTC : </span>{{data.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}</p>\r\n        </li>\r\n        <li>\r\n          <p><span class=\"font-weight-bold\">TVA : </span>{{data.tva}}%</p>\r\n        </li>\r\n      </ul>\r\n    </div>\r\n  </div>\r\n\r\n  <div mat-dialog-actions class=\"mb-1\">\r\n    <button mat-flat-button color=\"warn\" [mat-dialog-close]=\"1\" (click)=\"confirmDelete()\" class=\"delete-btn\">\r\n      <mat-icon>delete</mat-icon>\r\n      Supprimer\r\n    </button>\r\n    <button mat-flat-button (click)=\"onNoClick()\" tabindex=\"-1\" class=\"cancel-btn\">\r\n      <mat-icon>cancel</mat-icon>\r\n      Annuler\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,cAAc,QAAQ,0BAA0B;AAG5I,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;AA2B9C,OAAM,MAAOC,sBAAsB;EACjCC,YACSC,SAA+C,EACtBC,IAAgB,EACzCC,cAA8B;IAF9B,KAAAF,SAAS,GAATA,SAAS;IACgB,KAAAC,IAAI,GAAJA,IAAI;IAC7B,KAAAC,cAAc,GAAdA,cAAc;EACpB;EAEHC,SAASA,CAAA;IACP,IAAI,CAACH,SAAS,CAACI,KAAK,EAAE;EACxB;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,cAAc,CAACI,aAAa,CAAC,IAAI,CAACL,IAAI,CAACM,EAAE,CAAC,CAACC,SAAS,CAAC;MACxDC,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAACT,SAAS,CAACI,KAAK,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDM,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;QACjE,IAAI,CAACV,SAAS,CAACI,KAAK,CAAC,KAAK,CAAC;MAC7B;KACD,CAAC;EACJ;EAAC,QAAAQ,CAAA,G;qBArBUd,sBAAsB,EAAAe,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAH,EAAA,CAAAC,iBAAA,CAGvBxB,eAAe,GAAAuB,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAHdrB,sBAAsB;IAAAsB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAT,EAAA,CAAAU,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,gCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QChCnChB,EAAA,CAAAkB,cAAA,aAAuB;QAEYlB,EAAA,CAAAmB,MAAA,cAAO;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QACjDpB,EAAA,CAAAmB,MAAA,kEACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QAELpB,EAAA,CAAAkB,cAAA,aAAwB;QAEjBlB,EAAA,CAAAmB,MAAA,6FAAuE;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAGhFpB,EAAA,CAAAkB,cAAA,aAA6B;QACvBlB,EAAA,CAAAmB,MAAA,kDAAgC;QAAAnB,EAAA,CAAAoB,YAAA,EAAK;QACzCpB,EAAA,CAAAkB,cAAA,aAAqB;QAEiBlB,EAAA,CAAAmB,MAAA,eAAO;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEnEpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,uBAAe;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAiC;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAE/FpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,sBAAc;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAoB;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEjFpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,kBAAU;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAyD;;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAElHpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,mBAAW;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAA0D;;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAEpHpB,EAAA,CAAAkB,cAAA,UAAI;QACgClB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAO;QAAApB,EAAA,CAAAmB,MAAA,IAAa;QAAAnB,EAAA,CAAAoB,YAAA,EAAI;QAMxEpB,EAAA,CAAAkB,cAAA,cAAqC;QACyBlB,EAAA,CAAAqB,UAAA,mBAAAC,yDAAA;UAAA,OAASL,GAAA,CAAAzB,aAAA,EAAe;QAAA,EAAC;QACnFQ,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,mBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;QACTpB,EAAA,CAAAkB,cAAA,kBAA+E;QAAvDlB,EAAA,CAAAqB,UAAA,mBAAAE,yDAAA;UAAA,OAASN,GAAA,CAAA3B,SAAA,EAAW;QAAA,EAAC;QAC3CU,EAAA,CAAAkB,cAAA,gBAAU;QAAAlB,EAAA,CAAAmB,MAAA,cAAM;QAAAnB,EAAA,CAAAoB,YAAA,EAAW;QAC3BpB,EAAA,CAAAmB,MAAA,iBACF;QAAAnB,EAAA,CAAAoB,YAAA,EAAS;;;QA7B6CpB,EAAA,CAAAwB,SAAA,IAAa;QAAbxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAsC,IAAA,CAAa;QAGL1B,EAAA,CAAAwB,SAAA,GAAiC;QAAjCxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAuC,QAAA,sBAAiC;QAGlC3B,EAAA,CAAAwB,SAAA,GAAoB;QAApBxB,EAAA,CAAAyB,iBAAA,CAAAR,GAAA,CAAA7B,IAAA,CAAAwC,WAAA,CAAoB;QAGxB5B,EAAA,CAAAwB,SAAA,GAAyD;QAAzDxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA6B,WAAA,QAAAZ,GAAA,CAAA7B,IAAA,CAAA0C,cAAA,4BAAyD;QAGxD9B,EAAA,CAAAwB,SAAA,GAA0D;QAA1DxB,EAAA,CAAAyB,iBAAA,CAAAzB,EAAA,CAAA6B,WAAA,SAAAZ,GAAA,CAAA7B,IAAA,CAAA2C,eAAA,4BAA0D;QAG/D/B,EAAA,CAAAwB,SAAA,GAAa;QAAbxB,EAAA,CAAAgC,kBAAA,KAAAf,GAAA,CAAA7B,IAAA,CAAA6C,GAAA,MAAa;QAO7BjC,EAAA,CAAAwB,SAAA,GAAsB;QAAtBxB,EAAA,CAAAkC,UAAA,uBAAsB;;;mBDd3DxD,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBE,eAAe,EAAAqD,EAAA,CAAAC,SAAA,EACfrD,aAAa,EAAAsD,EAAA,CAAAC,OAAA,EACbzD,cAAc,EACdG,YAAY,EAAAuD,EAAA,CAAAC,YAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}