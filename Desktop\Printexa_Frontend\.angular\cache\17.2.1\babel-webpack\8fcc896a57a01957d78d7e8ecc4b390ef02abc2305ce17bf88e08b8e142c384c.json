{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    // Validation des données d'entrée\n    if (!id || id.trim() === '') {\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\n    }\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      console.log('Recherche du client par code dans les données:', currentData.length, 'clients');\n      const clientByCode = currentData.find(c => c.code === id);\n      if (clientByCode) {\n        console.log('Client trouvé par code:', clientByCode);\n        if (clientByCode.id !== id) {\n          console.log('ID trouvé par code:', clientByCode.id);\n          actualId = clientByCode.id;\n        }\n      } else {\n        console.warn('Aucun client trouvé avec le code:', id);\n      }\n    }\n    // Vérifier que l'ID final est valide après résolution\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    console.log('ID final après résolution:', actualId, 'Est GUID:', finalIsGuid);\n    if (!finalIsGuid) {\n      const errorMsg = `ID invalide après résolution: \"${actualId}\" (original: \"${id}\"). ` + `L'ID doit être un GUID valide ou un code client existant.`;\n      console.error(errorMsg);\n      return throwError(() => new Error(errorMsg));\n    }\n    // Nettoyer et valider les données\n    const cleanedClient = {};\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\n    const clientData = {\n      ...client\n    };\n    if ('id' in clientData) {\n      delete clientData.id;\n    }\n    // Ajouter seulement les champs non vides et valides\n    if (clientData.code && clientData.code.trim()) {\n      cleanedClient.code = clientData.code.trim();\n      // Validation du code\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\n      }\n    }\n    if (clientData.syntax && clientData.syntax.trim()) {\n      cleanedClient.syntax = clientData.syntax.trim();\n      if (cleanedClient.syntax.length > 100) {\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\n      cleanedClient.matFiscal = clientData.matFiscal.trim();\n      // Validation supprimée - accepter tout format de matricule fiscal\n    }\n    if (clientData.email && clientData.email.trim()) {\n      cleanedClient.email = clientData.email.trim();\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (cleanedClient.email.length > 100) {\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.telephone && clientData.telephone.trim()) {\n      cleanedClient.telephone = clientData.telephone.trim();\n      // Validation du téléphone\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(cleanedClient.telephone)) {\n        return throwError(() => new Error('Format de téléphone invalide'));\n      }\n    }\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\n    }\n    // Validation stricte côté client pour éviter les erreurs serveur\n    const validationResult = this.validateUpdateData(cleanedClient);\n    if (!validationResult.isValid) {\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\n    }\n    console.log('=== MISE À JOUR CLIENT ===');\n    console.log('ID original:', id);\n    console.log('ID à utiliser:', actualId);\n    console.log('Type de l\\'ID:', typeof actualId);\n    console.log('Longueur de l\\'ID:', actualId.length);\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\n    console.log('Données originales:', client);\n    console.log('Données après suppression ID:', clientData);\n    console.log('Données nettoyées à envoyer:', cleanedClient);\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\n    console.log('Headers:', this.getHeaders());\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\n    console.log('========================');\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Si le serveur retourne NoContent (204), créer le client mis à jour localement\n      if (response.status === 204 || !response.body) {\n        const currentData = this.dataChange.value;\n        // Chercher par l'ID original ou l'ID actuel\n        const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\n        if (!existingClient) {\n          throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\n        }\n        // Créer le client mis à jour en combinant les données existantes et les nouvelles\n        const updatedClient = {\n          ...existingClient,\n          ...cleanedClient\n        };\n        return updatedClient;\n      }\n      // Si le serveur retourne le client mis à jour\n      const updatedClient = response.body;\n      return updatedClient;\n    }), tap(updatedClient => {\n      console.log('Client mis à jour avec succès:', updatedClient);\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n      // Mettre à jour le client courant si c'est le même\n      if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\n        this.currentClientSubject.next(updatedClient);\n        localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n      }\n    }), catchError(error => {\n      console.error('Erreur lors de la mise à jour:', error);\n      // Si le serveur n'est pas disponible, simuler la mise à jour localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la mise à jour...');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      // Si erreur de validation persistante après suppression des validations de format,\n      // proposer une mise à jour locale en dernier recours\n      const errorMessage = error.error?.message || error.error?.title || '';\n      if (error.status === 400 && errorMessage.includes('validation errors')) {\n        console.warn('Erreur de validation serveur persistante malgré la suppression des validations de format');\n        console.warn('Basculement en mode simulation locale...');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleUpdateError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    console.log('Tentative de suppression des clients:', ids);\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log('Suppression en masse réussie');\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const updatedData = currentData.filter(client => !ids.includes(client.id));\n      this.dataChange.next(updatedData);\n    }), catchError(error => {\n      console.error('Erreur lors de la suppression en masse:', error);\n      // Si le serveur n'est pas disponible, simuler la suppression localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la suppression...');\n        return this.simulateLocalDeletion(ids);\n      }\n      // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\n      if (error.status === 500 || error.status === 404) {\n        console.log('Tentative de suppression individuelle...');\n        return this.deleteClientsIndividually(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Méthode de fallback pour supprimer les clients individuellement\n  deleteClientsIndividually(ids) {\n    const deleteRequests = ids.map(id => this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error(`Erreur lors de la suppression du client ${id}:`, error);\n      return throwError(() => error);\n    })));\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\n    return new Observable(observer => {\n      let completedCount = 0;\n      let hasError = false;\n      const errors = [];\n      deleteRequests.forEach((request, index) => {\n        request.subscribe({\n          next: () => {\n            completedCount++;\n            if (completedCount === ids.length && !hasError) {\n              // Mettre à jour les données locales\n              const currentData = this.dataChange.value;\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\n              this.dataChange.next(updatedData);\n              observer.next({\n                deletedCount: completedCount\n              });\n              observer.complete();\n            }\n          },\n          error: error => {\n            hasError = true;\n            errors.push({\n              id: ids[index],\n              error\n            });\n            if (completedCount + errors.length === ids.length) {\n              observer.error({\n                message: 'Certains clients n\\'ont pas pu être supprimés',\n                errors,\n                deletedCount: completedCount\n              });\n            }\n          }\n        });\n      });\n    });\n  }\n  // Simuler la suppression locale quand le serveur n'est pas disponible\n  simulateLocalDeletion(ids) {\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\n    // Mettre à jour les données locales\n    const currentData = this.dataChange.value;\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\n    this.dataChange.next(updatedData);\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length,\n          message: 'Suppression simulée localement (serveur non disponible)'\n        });\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\n  simulateLocalUpdate(id, updateData) {\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\n    // Trouver et mettre à jour le client dans les données locales\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\n    }\n    // Créer le client mis à jour\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    // Mettre à jour les données locales\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    // Mettre à jour le client courant si c'est le même\n    if (this.currentClientSubject.value?.id === id) {\n      this.currentClientSubject.next(updatedClient);\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n    }\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Méthode de test pour diagnostiquer les problèmes d'ID\n  testClientId(id) {\n    console.log('=== TEST ID CLIENT ===');\n    console.log('ID fourni:', id);\n    console.log('Type:', typeof id);\n    console.log('Longueur:', id.length);\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\n    const currentData = this.dataChange.value;\n    console.log('Nombre de clients dans les données:', currentData.length);\n    const clientById = currentData.find(c => c.id === id);\n    const clientByCode = currentData.find(c => c.code === id);\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\n    if (clientById) {\n      console.log('Client par ID:', clientById);\n    }\n    if (clientByCode) {\n      console.log('Client par code:', clientByCode);\n    }\n    // Test de résolution d'ID\n    console.log('--- Test de résolution ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const clientByCodeResolution = currentData.find(c => c.code === id);\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\n        actualId = clientByCodeResolution.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    console.log('ID final:', actualId);\n    console.log('ID final est GUID:', finalIsGuid);\n    console.log('===================');\n  }\n  // Méthode de test pour valider une mise à jour sans l'envoyer\n  testUpdateValidation(id, updateData) {\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\n    console.log('ID:', id);\n    console.log('Données:', updateData);\n    const validation = this.validateUpdateData(updateData);\n    console.log('Validation réussie:', validation.isValid);\n    if (!validation.isValid) {\n      console.log('Erreurs:', validation.errors);\n    }\n    console.log('Données nettoyées qui seraient envoyées:');\n    const cleaned = {};\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\n    console.log('Cleaned data:', cleaned);\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\n    console.log('================================');\n  }\n  // Validation stricte pour la mise à jour\n  validateUpdateData(client) {\n    const errors = [];\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\n    // Validation des champs\n    if (client.code !== undefined) {\n      if (!client.code || client.code.trim() === '') {\n        errors.push('Code ne peut pas être vide');\n      } else if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Code doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\n      // Validation de format supprimée - accepter tout format\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email !== undefined && client.email !== null) {\n      if (client.email.trim() !== '') {\n        // Validation de format supprimée - garder seulement la limite de longueur\n        if (client.email.length > 100) {\n          errors.push('Email ne peut pas dépasser 100 caractères');\n        }\n      }\n    }\n    if (client.telephone !== undefined && client.telephone !== null) {\n      if (client.telephone.trim() !== '') {\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n        if (!phonePattern.test(client.telephone)) {\n          errors.push('Format de téléphone invalide');\n        }\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Méthode publique pour tester une mise à jour complète\n  debugUpdate(clientId, updateData) {\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\n    // Test 1: Validation de l'ID\n    this.testClientId(clientId);\n    // Test 2: Validation des données\n    this.testUpdateValidation(clientId, updateData);\n    // Test 3: Simulation de la requête\n    console.log('--- Simulation de la requête ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\n    let actualId = clientId;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === clientId);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Méthode: PUT');\n    console.log('Headers:', this.getHeaders());\n    const cleanedData = {};\n    if (updateData.code) cleanedData.code = updateData.code.trim();\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\n    console.log('================================');\n  }\n  // Méthode pour tester la requête HTTP brute\n  testRawHttpRequest(clientId, updateData) {\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\n    const url = `${this.baseUrl}${clientId}`;\n    const headers = this.getHeaders();\n    console.log('URL:', url);\n    console.log('Headers:', headers);\n    console.log('Body:', JSON.stringify(updateData, null, 2));\n    return this.http.put(url, updateData, {\n      headers: headers,\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('SUCCÈS - Response:', response);\n    }), catchError(error => {\n      console.error('ÉCHEC - Error détaillé:', error);\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      // Retourner l'erreur pour que l'appelant puisse la voir\n      return throwError(() => error);\n    }));\n  }\n  // Test avec des données minimales pour identifier le problème\n  testMinimalUpdate(clientId) {\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\n    // Test 1: Seulement le code\n    const minimalData1 = {\n      code: 'TEST001'\n    };\n    console.log('Test 1 - Seulement code:', minimalData1);\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(catchError(() => {\n      console.log('Test 1 échoué, essai test 2...');\n      // Test 2: Seulement la syntax\n      const minimalData2 = {\n        syntax: 'Test Client'\n      };\n      console.log('Test 2 - Seulement syntax:', minimalData2);\n      return this.testRawHttpRequest(clientId, minimalData2).pipe(catchError(() => {\n        console.log('Test 2 échoué, essai test 3...');\n        // Test 3: Objet vide\n        const minimalData3 = {};\n        console.log('Test 3 - Objet vide:', minimalData3);\n        return this.testRawHttpRequest(clientId, minimalData3);\n      }));\n    }));\n  }\n  // Méthode utilitaire pour tester la validation des données client\n  validateClientData(client) {\n    const errors = [];\n    if (client.code) {\n      if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax && client.syntax.length > 100) {\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.email.length > 100) {\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\n      }\n    }\n    if (client.telephone) {\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(client.telephone)) {\n        errors.push('Format de téléphone invalide');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('=== ERREUR DE MISE À JOUR ===');\n    console.error('Status:', error.status);\n    console.error('StatusText:', error.statusText);\n    console.error('URL:', error.url);\n    console.error('Error object:', error.error);\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\n    console.error('Full error:', error);\n    console.error('============================');\n    // Analyser la structure de l'erreur en détail\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    const traceId = error.error?.traceId;\n    const type = error.error?.type;\n    console.log('API Error:', apiError);\n    console.log('Validation Errors:', validationErrors);\n    console.log('Validation Errors type:', typeof validationErrors);\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\n    console.log('Trace ID:', traceId);\n    console.log('Error Type:', type);\n    // Essayer de capturer d'autres propriétés d'erreur\n    if (error.error) {\n      console.log('Toutes les propriétés de error.error:');\n      for (const key in error.error) {\n        console.log(`  ${key}:`, error.error[key]);\n      }\n    }\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "email", "validateEmail", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "telephone", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "isGuid", "test", "isCode", "console", "log", "actualId", "length", "clientByCode", "find", "c", "warn", "finalIsGuid", "errorMsg", "cleanedClient", "phonePattern", "Object", "keys", "validationResult", "validateUpdateData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "join", "put", "status", "existingClient", "updatedClient", "index", "findIndex", "simulateLocalUpdate", "errorMessage", "message", "title", "includes", "handleUpdateError", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "updatedData", "filter", "simulateLocalDeletion", "deleteClientsIndividually", "deleteRequests", "observer", "completedCount", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "request", "subscribe", "deletedCount", "complete", "push", "setTimeout", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "re", "testClientId", "clientById", "clientByCodeResolution", "testUpdateValidation", "validation", "cleaned", "toUpperCase", "toLowerCase", "undefined", "debugUpdate", "clientId", "cleanedData", "testRawHttpRequest", "url", "testMinimalUpdate", "minimalData1", "minimalData2", "minimalData3", "validateClientData", "statusText", "apiError", "validationErrors", "traceId", "type", "key", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!id || id.trim() === '') {\r\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\r\n    }\r\n\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\r\n\r\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      console.log('Recherche du client par code dans les données:', currentData.length, 'clients');\r\n      const clientByCode = currentData.find(c => c.code === id);\r\n      if (clientByCode) {\r\n        console.log('Client trouvé par code:', clientByCode);\r\n        if (clientByCode.id !== id) {\r\n          console.log('ID trouvé par code:', clientByCode.id);\r\n          actualId = clientByCode.id;\r\n        }\r\n      } else {\r\n        console.warn('Aucun client trouvé avec le code:', id);\r\n      }\r\n    }\r\n\r\n    // Vérifier que l'ID final est valide après résolution\r\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    console.log('ID final après résolution:', actualId, 'Est GUID:', finalIsGuid);\r\n\r\n    if (!finalIsGuid) {\r\n      const errorMsg = `ID invalide après résolution: \"${actualId}\" (original: \"${id}\"). ` +\r\n                      `L'ID doit être un GUID valide ou un code client existant.`;\r\n      console.error(errorMsg);\r\n      return throwError(() => new Error(errorMsg));\r\n    }\r\n\r\n    // Nettoyer et valider les données\r\n    const cleanedClient: UpdateClientDto = {};\r\n\r\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\r\n    const clientData = { ...client };\r\n    if ('id' in clientData) {\r\n      delete (clientData as any).id;\r\n    }\r\n\r\n    // Ajouter seulement les champs non vides et valides\r\n    if (clientData.code && clientData.code.trim()) {\r\n      cleanedClient.code = clientData.code.trim();\r\n      // Validation du code\r\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\r\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.syntax && clientData.syntax.trim()) {\r\n      cleanedClient.syntax = clientData.syntax.trim();\r\n      if (cleanedClient.syntax.length > 100) {\r\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\r\n      cleanedClient.matFiscal = clientData.matFiscal.trim();\r\n      // Validation supprimée - accepter tout format de matricule fiscal\r\n    }\r\n\r\n    if (clientData.email && clientData.email.trim()) {\r\n      cleanedClient.email = clientData.email.trim();\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (cleanedClient.email.length > 100) {\r\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.telephone && clientData.telephone.trim()) {\r\n      cleanedClient.telephone = clientData.telephone.trim();\r\n      // Validation du téléphone\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(cleanedClient.telephone)) {\r\n        return throwError(() => new Error('Format de téléphone invalide'));\r\n      }\r\n    }\r\n\r\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\r\n    }\r\n\r\n    // Validation stricte côté client pour éviter les erreurs serveur\r\n    const validationResult = this.validateUpdateData(cleanedClient);\r\n    if (!validationResult.isValid) {\r\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\r\n    }\r\n\r\n    console.log('=== MISE À JOUR CLIENT ===');\r\n    console.log('ID original:', id);\r\n    console.log('ID à utiliser:', actualId);\r\n    console.log('Type de l\\'ID:', typeof actualId);\r\n    console.log('Longueur de l\\'ID:', actualId.length);\r\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\r\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\r\n    console.log('Données originales:', client);\r\n    console.log('Données après suppression ID:', clientData);\r\n    console.log('Données nettoyées à envoyer:', cleanedClient);\r\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\r\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\r\n    console.log('Headers:', this.getHeaders());\r\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\r\n    console.log('========================');\r\n\r\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Si le serveur retourne NoContent (204), créer le client mis à jour localement\r\n        if (response.status === 204 || !response.body) {\r\n          const currentData = this.dataChange.value;\r\n          // Chercher par l'ID original ou l'ID actuel\r\n          const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\r\n          if (!existingClient) {\r\n            throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\r\n          }\r\n\r\n          // Créer le client mis à jour en combinant les données existantes et les nouvelles\r\n          const updatedClient: Client = {\r\n            ...existingClient,\r\n            ...cleanedClient\r\n          };\r\n          return updatedClient;\r\n        }\r\n\r\n        // Si le serveur retourne le client mis à jour\r\n        const updatedClient: Client = response.body;\r\n        return updatedClient;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        console.log('Client mis à jour avec succès:', updatedClient);\r\n\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n\r\n        // Mettre à jour le client courant si c'est le même\r\n        if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\r\n          this.currentClientSubject.next(updatedClient);\r\n          localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la mise à jour:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la mise à jour localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la mise à jour...');\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n\r\n        // Si erreur de validation persistante après suppression des validations de format,\r\n        // proposer une mise à jour locale en dernier recours\r\n        const errorMessage = error.error?.message || error.error?.title || '';\r\n        if (error.status === 400 && errorMessage.includes('validation errors')) {\r\n          console.warn('Erreur de validation serveur persistante malgré la suppression des validations de format');\r\n          console.warn('Basculement en mode simulation locale...');\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n\r\n        return this.handleUpdateError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    console.log('Tentative de suppression des clients:', ids);\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log('Suppression en masse réussie');\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n        this.dataChange.next(updatedData);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la suppression en masse:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la suppression localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la suppression...');\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n\r\n        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\r\n        if (error.status === 500 || error.status === 404) {\r\n          console.log('Tentative de suppression individuelle...');\r\n          return this.deleteClientsIndividually(ids);\r\n        }\r\n\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode de fallback pour supprimer les clients individuellement\r\n  private deleteClientsIndividually(ids: string[]): Observable<any> {\r\n    const deleteRequests = ids.map(id =>\r\n      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n        catchError(error => {\r\n          console.error(`Erreur lors de la suppression du client ${id}:`, error);\r\n          return throwError(() => error);\r\n        })\r\n      )\r\n    );\r\n\r\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\r\n    return new Observable(observer => {\r\n      let completedCount = 0;\r\n      let hasError = false;\r\n      const errors: any[] = [];\r\n\r\n      deleteRequests.forEach((request, index) => {\r\n        request.subscribe({\r\n          next: () => {\r\n            completedCount++;\r\n            if (completedCount === ids.length && !hasError) {\r\n              // Mettre à jour les données locales\r\n              const currentData = this.dataChange.value;\r\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n              this.dataChange.next(updatedData);\r\n              observer.next({ deletedCount: completedCount });\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            hasError = true;\r\n            errors.push({ id: ids[index], error });\r\n            if (completedCount + errors.length === ids.length) {\r\n              observer.error({\r\n                message: 'Certains clients n\\'ont pas pu être supprimés',\r\n                errors,\r\n                deletedCount: completedCount\r\n              });\r\n            }\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  // Simuler la suppression locale quand le serveur n'est pas disponible\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\r\n\r\n    // Mettre à jour les données locales\r\n    const currentData = this.dataChange.value;\r\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n    this.dataChange.next(updatedData);\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({\r\n          deletedCount: ids.length,\r\n          message: 'Suppression simulée localement (serveur non disponible)'\r\n        });\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\r\n\r\n    // Trouver et mettre à jour le client dans les données locales\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\r\n    }\r\n\r\n    // Créer le client mis à jour\r\n    const updatedClient: Client = {\r\n      ...currentData[index],\r\n      ...updateData\r\n    };\r\n\r\n    // Mettre à jour les données locales\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    // Mettre à jour le client courant si c'est le même\r\n    if (this.currentClientSubject.value?.id === id) {\r\n      this.currentClientSubject.next(updatedClient);\r\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n    }\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Méthode de test pour diagnostiquer les problèmes d'ID\r\n  public testClientId(id: string): void {\r\n    console.log('=== TEST ID CLIENT ===');\r\n    console.log('ID fourni:', id);\r\n    console.log('Type:', typeof id);\r\n    console.log('Longueur:', id.length);\r\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\r\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\r\n\r\n    const currentData = this.dataChange.value;\r\n    console.log('Nombre de clients dans les données:', currentData.length);\r\n\r\n    const clientById = currentData.find(c => c.id === id);\r\n    const clientByCode = currentData.find(c => c.code === id);\r\n\r\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\r\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\r\n\r\n    if (clientById) {\r\n      console.log('Client par ID:', clientById);\r\n    }\r\n    if (clientByCode) {\r\n      console.log('Client par code:', clientByCode);\r\n    }\r\n\r\n    // Test de résolution d'ID\r\n    console.log('--- Test de résolution ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const clientByCodeResolution = currentData.find(c => c.code === id);\r\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\r\n        actualId = clientByCodeResolution.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    console.log('ID final:', actualId);\r\n    console.log('ID final est GUID:', finalIsGuid);\r\n    console.log('===================');\r\n  }\r\n\r\n  // Méthode de test pour valider une mise à jour sans l'envoyer\r\n  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {\r\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\r\n    console.log('ID:', id);\r\n    console.log('Données:', updateData);\r\n\r\n    const validation = this.validateUpdateData(updateData);\r\n    console.log('Validation réussie:', validation.isValid);\r\n    if (!validation.isValid) {\r\n      console.log('Erreurs:', validation.errors);\r\n    }\r\n\r\n    console.log('Données nettoyées qui seraient envoyées:');\r\n    const cleaned: UpdateClientDto = {};\r\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\r\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Cleaned data:', cleaned);\r\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Validation stricte pour la mise à jour\r\n  private validateUpdateData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\r\n\r\n    // Validation des champs\r\n    if (client.code !== undefined) {\r\n      if (!client.code || client.code.trim() === '') {\r\n        errors.push('Code ne peut pas être vide');\r\n      } else if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Code doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\r\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\r\n      // Validation de format supprimée - accepter tout format\r\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email !== undefined && client.email !== null) {\r\n      if (client.email.trim() !== '') {\r\n        // Validation de format supprimée - garder seulement la limite de longueur\r\n        if (client.email.length > 100) {\r\n          errors.push('Email ne peut pas dépasser 100 caractères');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.telephone !== undefined && client.telephone !== null) {\r\n      if (client.telephone.trim() !== '') {\r\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n        if (!phonePattern.test(client.telephone)) {\r\n          errors.push('Format de téléphone invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Méthode publique pour tester une mise à jour complète\r\n  public debugUpdate(clientId: string, updateData: any): void {\r\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\r\n\r\n    // Test 1: Validation de l'ID\r\n    this.testClientId(clientId);\r\n\r\n    // Test 2: Validation des données\r\n    this.testUpdateValidation(clientId, updateData);\r\n\r\n    // Test 3: Simulation de la requête\r\n    console.log('--- Simulation de la requête ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\r\n\r\n    let actualId = clientId;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === clientId);\r\n      if (clientByCode) {\r\n        actualId = clientByCode.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\r\n    console.log('Méthode: PUT');\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const cleanedData: any = {};\r\n    if (updateData.code) cleanedData.code = updateData.code.trim();\r\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\r\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\r\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\r\n    console.log('================================');\r\n  }\r\n\r\n  // Méthode pour tester la requête HTTP brute\r\n  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {\r\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\r\n\r\n    const url = `${this.baseUrl}${clientId}`;\r\n    const headers = this.getHeaders();\r\n\r\n    console.log('URL:', url);\r\n    console.log('Headers:', headers);\r\n    console.log('Body:', JSON.stringify(updateData, null, 2));\r\n\r\n    return this.http.put(url, updateData, {\r\n      headers: headers,\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('SUCCÈS - Response:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('ÉCHEC - Error détaillé:', error);\r\n        console.error('Status:', error.status);\r\n        console.error('Error body:', error.error);\r\n\r\n        // Retourner l'erreur pour que l'appelant puisse la voir\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Test avec des données minimales pour identifier le problème\r\n  public testMinimalUpdate(clientId: string): Observable<any> {\r\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\r\n\r\n    // Test 1: Seulement le code\r\n    const minimalData1 = { code: 'TEST001' };\r\n    console.log('Test 1 - Seulement code:', minimalData1);\r\n\r\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(\r\n      catchError(() => {\r\n        console.log('Test 1 échoué, essai test 2...');\r\n\r\n        // Test 2: Seulement la syntax\r\n        const minimalData2 = { syntax: 'Test Client' };\r\n        console.log('Test 2 - Seulement syntax:', minimalData2);\r\n\r\n        return this.testRawHttpRequest(clientId, minimalData2).pipe(\r\n          catchError(() => {\r\n            console.log('Test 2 échoué, essai test 3...');\r\n\r\n            // Test 3: Objet vide\r\n            const minimalData3 = {};\r\n            console.log('Test 3 - Objet vide:', minimalData3);\r\n\r\n            return this.testRawHttpRequest(clientId, minimalData3);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode utilitaire pour tester la validation des données client\r\n  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (client.code) {\r\n      if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.length > 100) {\r\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.email.length > 100) {\r\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.telephone) {\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(client.telephone)) {\r\n        errors.push('Format de téléphone invalide');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n\r\n    console.error('=== ERREUR DE MISE À JOUR ===');\r\n    console.error('Status:', error.status);\r\n    console.error('StatusText:', error.statusText);\r\n    console.error('URL:', error.url);\r\n    console.error('Error object:', error.error);\r\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\r\n    console.error('Full error:', error);\r\n    console.error('============================');\r\n\r\n    // Analyser la structure de l'erreur en détail\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n    const traceId = error.error?.traceId;\r\n    const type = error.error?.type;\r\n\r\n    console.log('API Error:', apiError);\r\n    console.log('Validation Errors:', validationErrors);\r\n    console.log('Validation Errors type:', typeof validationErrors);\r\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\r\n    console.log('Trace ID:', traceId);\r\n    console.log('Error Type:', type);\r\n\r\n    // Essayer de capturer d'autres propriétés d'erreur\r\n    if (error.error) {\r\n      console.log('Toutes les propriétés de error.error:');\r\n      for (const key in error.error) {\r\n        console.log(`  ${key}:`, error.error[key]);\r\n      }\r\n    }\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACE,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACO,KAAK,IAAI,CAAC,IAAI,CAACC,aAAa,CAACR,MAAM,CAACO,KAAK,CAAC,EAAE;MACrD,OAAOvC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMrB,UAAU,GAA0B;MACxCgB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACU,YAAY,EAAE;MACpCJ,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBI,MAAM,EAAEV,MAAM,CAACU,MAAM,EAAEJ,IAAI,EAAE;MAC7BK,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEL,IAAI,EAAE;MACnCC,KAAK,EAAEP,MAAM,CAACO,KAAK,EAAED,IAAI,EAAE;MAC3BM,SAAS,EAAEZ,MAAM,CAACY,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAAChC,IAAI,CAACuC,IAAI,CAAS,IAAI,CAACrC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIZ,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOY,SAAS;IAClB,CAAC,CAAC,EACF/C,GAAG,CAAE+C,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF9C,UAAU,CAAC,IAAI,CAACkD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAACtB,EAAU,EAAEC,MAAuB;IAC9C;IACA,IAAI,CAACD,EAAE,IAAIA,EAAE,CAACO,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3B,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,yCAAyC,CAAC,CAAC;;IAG/E,IAAI,CAACJ,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E;IACA,MAAMkB,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAACxB,EAAE,CAAC;IACzF,MAAMyB,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAACxB,EAAE,CAAC;IAE1C0B,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEJ,MAAM,EAAE,OAAO,EAAEE,MAAM,CAAC;IAEzD;IACA,IAAIG,QAAQ,GAAG5B,EAAE;IACjB,IAAIyB,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzCM,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAER,WAAW,CAACU,MAAM,EAAE,SAAS,CAAC;MAC5F,MAAMC,YAAY,GAAGX,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,KAAKN,EAAE,CAAC;MACzD,IAAI8B,YAAY,EAAE;QAChBJ,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEG,YAAY,CAAC;QACpD,IAAIA,YAAY,CAAC9B,EAAE,KAAKA,EAAE,EAAE;UAC1B0B,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEG,YAAY,CAAC9B,EAAE,CAAC;UACnD4B,QAAQ,GAAGE,YAAY,CAAC9B,EAAE;;OAE7B,MAAM;QACL0B,OAAO,CAACO,IAAI,CAAC,mCAAmC,EAAEjC,EAAE,CAAC;;;IAIzD;IACA,MAAMkC,WAAW,GAAG,iEAAiE,CAACV,IAAI,CAACI,QAAQ,CAAC;IACpGF,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEC,QAAQ,EAAE,WAAW,EAAEM,WAAW,CAAC;IAE7E,IAAI,CAACA,WAAW,EAAE;MAChB,MAAMC,QAAQ,GAAG,kCAAkCP,QAAQ,iBAAiB5B,EAAE,MAAM,GACpE,2DAA2D;MAC3E0B,OAAO,CAAC7B,KAAK,CAACsC,QAAQ,CAAC;MACvB,OAAOlE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC8B,QAAQ,CAAC,CAAC;;IAG9C;IACA,MAAMC,aAAa,GAAoB,EAAE;IAEzC;IACA,MAAMpD,UAAU,GAAG;MAAE,GAAGiB;IAAM,CAAE;IAChC,IAAI,IAAI,IAAIjB,UAAU,EAAE;MACtB,OAAQA,UAAkB,CAACgB,EAAE;;IAG/B;IACA,IAAIhB,UAAU,CAACsB,IAAI,IAAItB,UAAU,CAACsB,IAAI,CAACC,IAAI,EAAE,EAAE;MAC7C6B,aAAa,CAAC9B,IAAI,GAAGtB,UAAU,CAACsB,IAAI,CAACC,IAAI,EAAE;MAC3C;MACA,IAAI6B,aAAa,CAAC9B,IAAI,CAACuB,MAAM,GAAG,CAAC,IAAIO,aAAa,CAAC9B,IAAI,CAACuB,MAAM,GAAG,EAAE,EAAE;QACnE,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIrB,UAAU,CAAC2B,MAAM,IAAI3B,UAAU,CAAC2B,MAAM,CAACJ,IAAI,EAAE,EAAE;MACjD6B,aAAa,CAACzB,MAAM,GAAG3B,UAAU,CAAC2B,MAAM,CAACJ,IAAI,EAAE;MAC/C,IAAI6B,aAAa,CAACzB,MAAM,CAACkB,MAAM,GAAG,GAAG,EAAE;QACrC,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIrB,UAAU,CAAC4B,SAAS,IAAI5B,UAAU,CAAC4B,SAAS,CAACL,IAAI,EAAE,EAAE;MACvD6B,aAAa,CAACxB,SAAS,GAAG5B,UAAU,CAAC4B,SAAS,CAACL,IAAI,EAAE;MACrD;;IAGF,IAAIvB,UAAU,CAACwB,KAAK,IAAIxB,UAAU,CAACwB,KAAK,CAACD,IAAI,EAAE,EAAE;MAC/C6B,aAAa,CAAC5B,KAAK,GAAGxB,UAAU,CAACwB,KAAK,CAACD,IAAI,EAAE;MAC7C;MACA,IAAI6B,aAAa,CAAC5B,KAAK,CAACqB,MAAM,GAAG,GAAG,EAAE;QACpC,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,8CAA8C,CAAC,CAAC;;;IAItF,IAAIrB,UAAU,CAAC6B,SAAS,IAAI7B,UAAU,CAAC6B,SAAS,CAACN,IAAI,EAAE,EAAE;MACvD6B,aAAa,CAACvB,SAAS,GAAG7B,UAAU,CAAC6B,SAAS,CAACN,IAAI,EAAE;MACrD;MACA,MAAM8B,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACb,IAAI,CAACY,aAAa,CAACvB,SAAS,CAAC,EAAE;QAC/C,OAAO5C,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,8BAA8B,CAAC,CAAC;;;IAItE;IACA,IAAIiC,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACP,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2CAA2C,CAAC,CAAC;;IAGjF;IACA,MAAMmC,gBAAgB,GAAG,IAAI,CAACC,kBAAkB,CAACL,aAAa,CAAC;IAC/D,IAAI,CAACI,gBAAgB,CAACE,OAAO,EAAE;MAC7B,OAAOzE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uBAAuBmC,gBAAgB,CAACG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;IAGjGlB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE3B,EAAE,CAAC;IAC/B0B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,QAAQ,CAAC;IACvCF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOC,QAAQ,CAAC;IAC9CF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEC,QAAQ,CAACC,MAAM,CAAC;IAClDH,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,iEAAiE,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC;IAChHF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,CAACH,IAAI,CAACI,QAAQ,CAAC,CAAC;IACjEF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE1B,MAAM,CAAC;IAC1CyB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE3C,UAAU,CAAC;IACxD0C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAES,aAAa,CAAC;IAC1DV,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,IAAIS,aAAa,GAAG,eAAe,GAAG,UAAU,CAAC;IACjGV,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAG,IAAI,CAAClD,OAAO,GAAGmD,QAAQ,EAAE,CAAC;IAC1DF,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACtC,UAAU,EAAE,CAAC;IAC1CqC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAExC,IAAI,CAACgB,SAAS,CAACiC,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACjEV,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,OAAO,IAAI,CAACpD,IAAI,CAACsE,GAAG,CAAC,GAAG,IAAI,CAACpE,OAAO,GAAGmD,QAAQ,EAAE,EAAEQ,aAAa,EAAE;MAChE3C,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAAC8B,MAAM,KAAK,GAAG,IAAI,CAAC9B,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMC,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;QACzC;QACA,MAAM2B,cAAc,GAAG5B,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAK4B,QAAQ,IAAII,CAAC,CAAChC,EAAE,KAAKA,EAAE,IAAIgC,CAAC,CAAC1B,IAAI,KAAKN,EAAE,CAAC;QAC/F,IAAI,CAAC+C,cAAc,EAAE;UACnB,MAAM,IAAI1C,KAAK,CAAC,mDAAmDuB,QAAQ,eAAe5B,EAAE,GAAG,CAAC;;QAGlG;QACA,MAAMgD,aAAa,GAAW;UAC5B,GAAGD,cAAc;UACjB,GAAGX;SACJ;QACD,OAAOY,aAAa;;MAGtB;MACA,MAAMA,aAAa,GAAWhC,QAAQ,CAACE,IAAI;MAC3C,OAAO8B,aAAa;IACtB,CAAC,CAAC,EACF9E,GAAG,CAAE8E,aAAqB,IAAI;MAC5BtB,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqB,aAAa,CAAC;MAE5D;MACA,MAAM7B,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAM6B,KAAK,GAAG9B,WAAW,CAAC+B,SAAS,CAAClB,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAK4B,QAAQ,IAAII,CAAC,CAAChC,EAAE,KAAKA,EAAE,IAAIgC,CAAC,CAAC1B,IAAI,KAAKN,EAAE,CAAC;MAC3F,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB9B,WAAW,CAAC8B,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAACtE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;;MAGxC;MACA,IAAI,IAAI,CAACvC,oBAAoB,CAACwC,KAAK,EAAEpB,EAAE,KAAK4B,QAAQ,IAAI,IAAI,CAAChD,oBAAoB,CAACwC,KAAK,EAAEpB,EAAE,KAAKA,EAAE,EAAE;QAClG,IAAI,CAACpB,oBAAoB,CAACgB,IAAI,CAACoD,aAAa,CAAC;QAC7C/D,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAAC6C,aAAa,CAAC,CAAC;;IAExE,CAAC,CAAC,EACF7E,UAAU,CAAC0B,KAAK,IAAG;MACjB6B,OAAO,CAAC7B,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAEtD;MACA,IAAIA,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;QACtBpB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACwB,mBAAmB,CAACvB,QAAQ,EAAEQ,aAAa,CAAC;;MAG1D;MACA;MACA,MAAMgB,YAAY,GAAGvD,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAI,EAAE;MACrE,IAAIzD,KAAK,CAACiD,MAAM,KAAK,GAAG,IAAIM,YAAY,CAACG,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QACtE7B,OAAO,CAACO,IAAI,CAAC,0FAA0F,CAAC;QACxGP,OAAO,CAACO,IAAI,CAAC,0CAA0C,CAAC;QACxD,OAAO,IAAI,CAACkB,mBAAmB,CAACvB,QAAQ,EAAEQ,aAAa,CAAC;;MAG1D,OAAO,IAAI,CAACoB,iBAAiB,CAAC3D,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA4D,YAAYA,CAACzD,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAACmF,MAAM,CAAO,GAAG,IAAI,CAACjF,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACyF,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFxF,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA8D,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAAChC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrFqB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkC,GAAG,CAAC;IAEzD,OAAO,IAAI,CAACtF,IAAI,CAACmF,MAAM,CAAC,GAAG,IAAI,CAACjF,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B6B,IAAI,EAAE2C;KACP,CAAC,CAACnE,IAAI,CACLxB,GAAG,CAAC,MAAK;MACPwD,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;MACA,MAAMR,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAM0C,WAAW,GAAG3C,WAAW,CAAC4C,MAAM,CAAC9D,MAAM,IAAI,CAAC4D,GAAG,CAACN,QAAQ,CAACtD,MAAM,CAACD,EAAE,CAAC,CAAC;MAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACkE,WAAW,CAAC;IACnC,CAAC,CAAC,EACF3F,UAAU,CAAC0B,KAAK,IAAG;MACjB6B,OAAO,CAAC7B,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAE/D;MACA,IAAIA,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;QACtBpB,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACqC,qBAAqB,CAACH,GAAG,CAAC;;MAGxC;MACA,IAAIhE,KAAK,CAACiD,MAAM,KAAK,GAAG,IAAIjD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;QAChDpB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI,CAACsC,yBAAyB,CAACJ,GAAG,CAAC;;MAG5C,OAAO,IAAI,CAAC/D,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQoE,yBAAyBA,CAACJ,GAAa;IAC7C,MAAMK,cAAc,GAAGL,GAAG,CAACzF,GAAG,CAAC4B,EAAE,IAC/B,IAAI,CAACzB,IAAI,CAACmF,MAAM,CAAC,GAAG,IAAI,CAACjF,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3EvB,UAAU,CAAC0B,KAAK,IAAG;MACjB6B,OAAO,CAAC7B,KAAK,CAAC,2CAA2CG,EAAE,GAAG,EAAEH,KAAK,CAAC;MACtE,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACF;IAED;IACA,OAAO,IAAI7B,UAAU,CAACmG,QAAQ,IAAG;MAC/B,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,MAAM1B,MAAM,GAAU,EAAE;MAExBuB,cAAc,CAACI,OAAO,CAAC,CAACC,OAAO,EAAEtB,KAAK,KAAI;QACxCsB,OAAO,CAACC,SAAS,CAAC;UAChB5E,IAAI,EAAEA,CAAA,KAAK;YACTwE,cAAc,EAAE;YAChB,IAAIA,cAAc,KAAKP,GAAG,CAAChC,MAAM,IAAI,CAACwC,QAAQ,EAAE;cAC9C;cACA,MAAMlD,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;cACzC,MAAM0C,WAAW,GAAG3C,WAAW,CAAC4C,MAAM,CAAC9D,MAAM,IAAI,CAAC4D,GAAG,CAACN,QAAQ,CAACtD,MAAM,CAACD,EAAE,CAAC,CAAC;cAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACkE,WAAW,CAAC;cACjCK,QAAQ,CAACvE,IAAI,CAAC;gBAAE6E,YAAY,EAAEL;cAAc,CAAE,CAAC;cAC/CD,QAAQ,CAACO,QAAQ,EAAE;;UAEvB,CAAC;UACD7E,KAAK,EAAGA,KAAK,IAAI;YACfwE,QAAQ,GAAG,IAAI;YACf1B,MAAM,CAACgC,IAAI,CAAC;cAAE3E,EAAE,EAAE6D,GAAG,CAACZ,KAAK,CAAC;cAAEpD;YAAK,CAAE,CAAC;YACtC,IAAIuE,cAAc,GAAGzB,MAAM,CAACd,MAAM,KAAKgC,GAAG,CAAChC,MAAM,EAAE;cACjDsC,QAAQ,CAACtE,KAAK,CAAC;gBACbwD,OAAO,EAAE,+CAA+C;gBACxDV,MAAM;gBACN8B,YAAY,EAAEL;eACf,CAAC;;UAEN;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQJ,qBAAqBA,CAACH,GAAa;IACzCnC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEkC,GAAG,CAAC;IAErE;IACA,MAAM1C,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAM0C,WAAW,GAAG3C,WAAW,CAAC4C,MAAM,CAAC9D,MAAM,IAAI,CAAC4D,GAAG,CAACN,QAAQ,CAACtD,MAAM,CAACD,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACkE,WAAW,CAAC;IAEjC;IACA,OAAO,IAAI9F,UAAU,CAACmG,QAAQ,IAAG;MAC/BS,UAAU,CAAC,MAAK;QACdT,QAAQ,CAACvE,IAAI,CAAC;UACZ6E,YAAY,EAAEZ,GAAG,CAAChC,MAAM;UACxBwB,OAAO,EAAE;SACV,CAAC;QACFc,QAAQ,CAACO,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACQvB,mBAAmBA,CAACnD,EAAU,EAAE6E,UAA2B;IACjEnD,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE3B,EAAE,EAAE6E,UAAU,CAAC;IAE9E;IACA,MAAM1D,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAM6B,KAAK,GAAG9B,WAAW,CAAC+B,SAAS,CAACjD,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOhF,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E;IACA,MAAM2C,aAAa,GAAW;MAC5B,GAAG7B,WAAW,CAAC8B,KAAK,CAAC;MACrB,GAAG4B;KACJ;IAED;IACA1D,WAAW,CAAC8B,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAACtE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;IAEtC;IACA,IAAI,IAAI,CAACvC,oBAAoB,CAACwC,KAAK,EAAEpB,EAAE,KAAKA,EAAE,EAAE;MAC9C,IAAI,CAACpB,oBAAoB,CAACgB,IAAI,CAACoD,aAAa,CAAC;MAC7C/D,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAAC6C,aAAa,CAAC,CAAC;;IAGtE;IACA,OAAO,IAAIhF,UAAU,CAASmG,QAAQ,IAAG;MACvCS,UAAU,CAAC,MAAK;QACdT,QAAQ,CAACvE,IAAI,CAACoD,aAAa,CAAC;QAC5BmB,QAAQ,CAACO,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACAf,kBAAkBA,CAAA;IAChB1E,YAAY,CAAC6F,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAClG,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAImF,IAAIA,CAAA;IACN,OAAO,IAAI,CAACrG,UAAU,CAAC0C,KAAK;EAC9B;EAEA4D,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQvE,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACwE,OAAO,CAAC,OAAO,EAAE,UAASlD,CAAC;MACvE,MAAMmD,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGtD,CAAC,KAAK,GAAG,GAAGmD,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQ9E,aAAaA,CAACD,KAAa;IACjC,MAAMgF,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAAChE,IAAI,CAAChB,KAAK,CAAC;EACvB;EAEA;EACOiF,YAAYA,CAACzF,EAAU;IAC5B0B,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE3B,EAAE,CAAC;IAC7B0B,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAO3B,EAAE,CAAC;IAC/B0B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE3B,EAAE,CAAC6B,MAAM,CAAC;IACnCH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,iEAAiE,CAACH,IAAI,CAACxB,EAAE,CAAC,CAAC;IACpG0B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAACH,IAAI,CAACxB,EAAE,CAAC,CAAC;IAErD,MAAMmB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzCM,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAER,WAAW,CAACU,MAAM,CAAC;IAEtE,MAAM6D,UAAU,GAAGvE,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,EAAE,KAAKA,EAAE,CAAC;IACrD,MAAM8B,YAAY,GAAGX,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,KAAKN,EAAE,CAAC;IAEzD0B,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE+D,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;IAChEhE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEG,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IAEpE,IAAI4D,UAAU,EAAE;MACdhE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE+D,UAAU,CAAC;;IAE3C,IAAI5D,YAAY,EAAE;MAChBJ,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEG,YAAY,CAAC;;IAG/C;IACAJ,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,MAAMJ,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAACxB,EAAE,CAAC;IACzF,MAAMyB,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAACxB,EAAE,CAAC;IAE1C,IAAI4B,QAAQ,GAAG5B,EAAE;IACjB,IAAIyB,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMoE,sBAAsB,GAAGxE,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAI2F,sBAAsB,IAAIA,sBAAsB,CAAC3F,EAAE,KAAKA,EAAE,EAAE;QAC9D4B,QAAQ,GAAG+D,sBAAsB,CAAC3F,EAAE;QACpC0B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,QAAQ,CAAC;;;IAIvC,MAAMM,WAAW,GAAG,iEAAiE,CAACV,IAAI,CAACI,QAAQ,CAAC;IACpGF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEC,QAAQ,CAAC;IAClCF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,WAAW,CAAC;IAC9CR,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA;EACOiE,oBAAoBA,CAAC5F,EAAU,EAAE6E,UAA2B;IACjEnD,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,KAAK,EAAE3B,EAAE,CAAC;IACtB0B,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkD,UAAU,CAAC;IAEnC,MAAMgB,UAAU,GAAG,IAAI,CAACpD,kBAAkB,CAACoC,UAAU,CAAC;IACtDnD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEkE,UAAU,CAACnD,OAAO,CAAC;IACtD,IAAI,CAACmD,UAAU,CAACnD,OAAO,EAAE;MACvBhB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEkE,UAAU,CAAClD,MAAM,CAAC;;IAG5CjB,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,MAAMmE,OAAO,GAAoB,EAAE;IACnC,IAAIjB,UAAU,CAACvE,IAAI,IAAIuE,UAAU,CAACvE,IAAI,CAACC,IAAI,EAAE,EAAEuF,OAAO,CAACxF,IAAI,GAAGuE,UAAU,CAACvE,IAAI,CAACC,IAAI,EAAE;IACpF,IAAIsE,UAAU,CAAClE,MAAM,IAAIkE,UAAU,CAAClE,MAAM,CAACJ,IAAI,EAAE,EAAEuF,OAAO,CAACnF,MAAM,GAAGkE,UAAU,CAAClE,MAAM,CAACJ,IAAI,EAAE;IAC5F,IAAIsE,UAAU,CAACjE,SAAS,IAAIiE,UAAU,CAACjE,SAAS,CAACL,IAAI,EAAE,EAAEuF,OAAO,CAAClF,SAAS,GAAGiE,UAAU,CAACjE,SAAS,CAACL,IAAI,EAAE,CAACwF,WAAW,EAAE;IACtH,IAAIlB,UAAU,CAACrE,KAAK,IAAIqE,UAAU,CAACrE,KAAK,CAACD,IAAI,EAAE,EAAEuF,OAAO,CAACtF,KAAK,GAAGqE,UAAU,CAACrE,KAAK,CAACD,IAAI,EAAE,CAACyF,WAAW,EAAE;IACtG,IAAInB,UAAU,CAAChE,SAAS,IAAIgE,UAAU,CAAChE,SAAS,CAACN,IAAI,EAAE,EAAEuF,OAAO,CAACjF,SAAS,GAAGgE,UAAU,CAAChE,SAAS,CAACN,IAAI,EAAE;IAExGmB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEmE,OAAO,CAAC;IACrCpE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAAClD,OAAO,GAAGuB,EAAE,EAAE,CAAC;IAC/D0B,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQc,kBAAkBA,CAACxC,MAAuB;IAChD,MAAM0C,MAAM,GAAa,EAAE;IAE3B;IAEA;IACA,IAAI1C,MAAM,CAACK,IAAI,KAAK2F,SAAS,EAAE;MAC7B,IAAI,CAAChG,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7CoC,MAAM,CAACgC,IAAI,CAAC,4BAA4B,CAAC;OAC1C,MAAM,IAAI1E,MAAM,CAACK,IAAI,CAACuB,MAAM,GAAG,CAAC,IAAI5B,MAAM,CAACK,IAAI,CAACuB,MAAM,GAAG,EAAE,EAAE;QAC5Dc,MAAM,CAACgC,IAAI,CAAC,6CAA6C,CAAC;;;IAI9D,IAAI1E,MAAM,CAACU,MAAM,KAAKsF,SAAS,IAAIhG,MAAM,CAACU,MAAM,KAAK,IAAI,IAAIV,MAAM,CAACU,MAAM,CAACkB,MAAM,GAAG,GAAG,EAAE;MACvFc,MAAM,CAACgC,IAAI,CAAC,oDAAoD,CAAC;;IAGnE,IAAI1E,MAAM,CAACW,SAAS,KAAKqF,SAAS,IAAIhG,MAAM,CAACW,SAAS,KAAK,IAAI,EAAE;MAC/D;MACA,IAAIX,MAAM,CAACW,SAAS,CAACL,IAAI,EAAE,KAAK,EAAE,IAAIN,MAAM,CAACW,SAAS,CAACiB,MAAM,GAAG,EAAE,EAAE;QAClEc,MAAM,CAACgC,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAI1E,MAAM,CAACO,KAAK,KAAKyF,SAAS,IAAIhG,MAAM,CAACO,KAAK,KAAK,IAAI,EAAE;MACvD,IAAIP,MAAM,CAACO,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B;QACA,IAAIN,MAAM,CAACO,KAAK,CAACqB,MAAM,GAAG,GAAG,EAAE;UAC7Bc,MAAM,CAACgC,IAAI,CAAC,2CAA2C,CAAC;;;;IAK9D,IAAI1E,MAAM,CAACY,SAAS,KAAKoF,SAAS,IAAIhG,MAAM,CAACY,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIZ,MAAM,CAACY,SAAS,CAACN,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAM8B,YAAY,GAAG,6BAA6B;QAClD,IAAI,CAACA,YAAY,CAACb,IAAI,CAACvB,MAAM,CAACY,SAAS,CAAC,EAAE;UACxC8B,MAAM,CAACgC,IAAI,CAAC,8BAA8B,CAAC;;;;IAKjD,OAAO;MACLjC,OAAO,EAAEC,MAAM,CAACd,MAAM,KAAK,CAAC;MAC5Bc;KACD;EACH;EAEA;EACOuD,WAAWA,CAACC,QAAgB,EAAEtB,UAAe;IAClDnD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAAC8D,YAAY,CAACU,QAAQ,CAAC;IAE3B;IACA,IAAI,CAACP,oBAAoB,CAACO,QAAQ,EAAEtB,UAAU,CAAC;IAE/C;IACAnD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,MAAMJ,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAAC2E,QAAQ,CAAC;IAC/F,MAAM1E,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAAC2E,QAAQ,CAAC;IAEhD,IAAIvE,QAAQ,GAAGuE,QAAQ;IACvB,IAAI1E,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMU,YAAY,GAAGX,WAAW,CAACY,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1B,IAAI,KAAK6F,QAAQ,CAAC;MAC/D,IAAIrE,YAAY,EAAE;QAChBF,QAAQ,GAAGE,YAAY,CAAC9B,EAAE;QAC1B0B,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEC,QAAQ,CAAC;;;IAIvCF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAAClD,OAAO,GAAGmD,QAAQ,EAAE,CAAC;IACxDF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACtC,UAAU,EAAE,CAAC;IAE1C,MAAM+G,WAAW,GAAQ,EAAE;IAC3B,IAAIvB,UAAU,CAACvE,IAAI,EAAE8F,WAAW,CAAC9F,IAAI,GAAGuE,UAAU,CAACvE,IAAI,CAACC,IAAI,EAAE;IAC9D,IAAIsE,UAAU,CAAClE,MAAM,EAAEyF,WAAW,CAACzF,MAAM,GAAGkE,UAAU,CAAClE,MAAM,CAACJ,IAAI,EAAE;IACpE,IAAIsE,UAAU,CAACjE,SAAS,EAAEwF,WAAW,CAACxF,SAAS,GAAGiE,UAAU,CAACjE,SAAS,CAACL,IAAI,EAAE,CAAC,CAAC;IAC/E,IAAIsE,UAAU,CAACrE,KAAK,EAAE4F,WAAW,CAAC5F,KAAK,GAAGqE,UAAU,CAACrE,KAAK,CAACD,IAAI,EAAE,CAAC,CAAC;IACnE,IAAIsE,UAAU,CAAChE,SAAS,EAAEuF,WAAW,CAACvF,SAAS,GAAGgE,UAAU,CAAChE,SAAS,CAACN,IAAI,EAAE;IAE7EmB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAExC,IAAI,CAACgB,SAAS,CAACiG,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChE1E,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACO0E,kBAAkBA,CAACF,QAAgB,EAAEtB,UAAe;IACzDnD,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,MAAM2E,GAAG,GAAG,GAAG,IAAI,CAAC7H,OAAO,GAAG0H,QAAQ,EAAE;IACxC,MAAM1G,OAAO,GAAG,IAAI,CAACJ,UAAU,EAAE;IAEjCqC,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE2E,GAAG,CAAC;IACxB5E,OAAO,CAACC,GAAG,CAAC,UAAU,EAAElC,OAAO,CAAC;IAChCiC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAExC,IAAI,CAACgB,SAAS,CAAC0E,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO,IAAI,CAACtG,IAAI,CAACsE,GAAG,CAACyD,GAAG,EAAEzB,UAAU,EAAE;MACpCpF,OAAO,EAAEA,OAAO;MAChBsB,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLxB,GAAG,CAAC8C,QAAQ,IAAG;MACbU,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEX,QAAQ,CAAC;IAC7C,CAAC,CAAC,EACF7C,UAAU,CAAC0B,KAAK,IAAG;MACjB6B,OAAO,CAAC7B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C6B,OAAO,CAAC7B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACiD,MAAM,CAAC;MACtCpB,OAAO,CAAC7B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MAEzC;MACA,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACO0G,iBAAiBA,CAACJ,QAAgB;IACvCzE,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAM6E,YAAY,GAAG;MAAElG,IAAI,EAAE;IAAS,CAAE;IACxCoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE6E,YAAY,CAAC;IAErD,OAAO,IAAI,CAACH,kBAAkB,CAACF,QAAQ,EAAEK,YAAY,CAAC,CAAC9G,IAAI,CACzDvB,UAAU,CAAC,MAAK;MACduD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA,MAAM8E,YAAY,GAAG;QAAE9F,MAAM,EAAE;MAAa,CAAE;MAC9Ce,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE8E,YAAY,CAAC;MAEvD,OAAO,IAAI,CAACJ,kBAAkB,CAACF,QAAQ,EAAEM,YAAY,CAAC,CAAC/G,IAAI,CACzDvB,UAAU,CAAC,MAAK;QACduD,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C;QACA,MAAM+E,YAAY,GAAG,EAAE;QACvBhF,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE+E,YAAY,CAAC;QAEjD,OAAO,IAAI,CAACL,kBAAkB,CAACF,QAAQ,EAAEO,YAAY,CAAC;MACxD,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACOC,kBAAkBA,CAAC1G,MAAuB;IAC/C,MAAM0C,MAAM,GAAa,EAAE;IAE3B,IAAI1C,MAAM,CAACK,IAAI,EAAE;MACf,IAAIL,MAAM,CAACK,IAAI,CAACuB,MAAM,GAAG,CAAC,IAAI5B,MAAM,CAACK,IAAI,CAACuB,MAAM,GAAG,EAAE,EAAE;QACrDc,MAAM,CAACgC,IAAI,CAAC,uDAAuD,CAAC;;;IAIxE,IAAI1E,MAAM,CAACU,MAAM,IAAIV,MAAM,CAACU,MAAM,CAACkB,MAAM,GAAG,GAAG,EAAE;MAC/Cc,MAAM,CAACgC,IAAI,CAAC,uDAAuD,CAAC;;IAGtE,IAAI1E,MAAM,CAACW,SAAS,EAAE;MACpB;MACA,IAAIX,MAAM,CAACW,SAAS,CAACiB,MAAM,GAAG,EAAE,EAAE;QAChCc,MAAM,CAACgC,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAI1E,MAAM,CAACO,KAAK,EAAE;MAChB;MACA,IAAIP,MAAM,CAACO,KAAK,CAACqB,MAAM,GAAG,GAAG,EAAE;QAC7Bc,MAAM,CAACgC,IAAI,CAAC,8CAA8C,CAAC;;;IAI/D,IAAI1E,MAAM,CAACY,SAAS,EAAE;MACpB,MAAMwB,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACb,IAAI,CAACvB,MAAM,CAACY,SAAS,CAAC,EAAE;QACxC8B,MAAM,CAACgC,IAAI,CAAC,8BAA8B,CAAC;;;IAI/C,OAAO;MACLjC,OAAO,EAAEC,MAAM,CAACd,MAAM,KAAK,CAAC;MAC5Bc;KACD;EACH;EAEA;EACQa,iBAAiBA,CAAC3D,KAAwB;IAChD,IAAIuD,YAAY,GAAG,yCAAyC;IAE5D1B,OAAO,CAAC7B,KAAK,CAAC,+BAA+B,CAAC;IAC9C6B,OAAO,CAAC7B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACiD,MAAM,CAAC;IACtCpB,OAAO,CAAC7B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC+G,UAAU,CAAC;IAC9ClF,OAAO,CAAC7B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACyG,GAAG,CAAC;IAChC5E,OAAO,CAAC7B,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3C6B,OAAO,CAAC7B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,GAAGyC,MAAM,CAACC,IAAI,CAAC1C,KAAK,CAACA,KAAK,CAAC,GAAG,iBAAiB,CAAC;IACxF6B,OAAO,CAAC7B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnC6B,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,CAAC;IAE7C;IACA,MAAMgH,QAAQ,GAAGhH,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAIzD,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAMiH,gBAAgB,GAAGjH,KAAK,CAACA,KAAK,EAAE8C,MAAM;IAC5C,MAAMoE,OAAO,GAAGlH,KAAK,CAACA,KAAK,EAAEkH,OAAO;IACpC,MAAMC,IAAI,GAAGnH,KAAK,CAACA,KAAK,EAAEmH,IAAI;IAE9BtF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEkF,QAAQ,CAAC;IACnCnF,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmF,gBAAgB,CAAC;IACnDpF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOmF,gBAAgB,CAAC;IAC/DpF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEmF,gBAAgB,GAAGxE,MAAM,CAACC,IAAI,CAACuE,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;IACjHpF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEoF,OAAO,CAAC;IACjCrF,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqF,IAAI,CAAC;IAEhC;IACA,IAAInH,KAAK,CAACA,KAAK,EAAE;MACf6B,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,KAAK,MAAMsF,GAAG,IAAIpH,KAAK,CAACA,KAAK,EAAE;QAC7B6B,OAAO,CAACC,GAAG,CAAC,KAAKsF,GAAG,GAAG,EAAEpH,KAAK,CAACA,KAAK,CAACoH,GAAG,CAAC,CAAC;;;IAI9C,IAAIpH,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MACtBM,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAI0D,gBAAgB,EAAE;QACpB,MAAMI,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOJ,gBAAgB,KAAK,QAAQ,EAAE;UACxCxE,MAAM,CAACC,IAAI,CAACuE,gBAAgB,CAAC,CAACxC,OAAO,CAAC6C,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGN,gBAAgB,CAACK,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAAC9C,OAAO,CAACiD,GAAG,IAAIL,YAAY,CAACvC,IAAI,CAAC,GAAGwC,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAACvC,IAAI,CAAC,GAAGwC,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAACrF,MAAM,GAAG,CAAC,EAAE;UAC3BuB,YAAY,IAAI,iBAAiB8D,YAAY,CAACtE,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAIiE,QAAQ,EAAE;QACnBzD,YAAY,IAAI,eAAeyD,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACtD,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/DH,YAAY,GAAG,0BAA0B;QACzC,IAAIvD,KAAK,CAACA,KAAK,EAAE8C,MAAM,EAAE;UACvBS,YAAY,IAAI,0CAA0C;UAC1Dd,MAAM,CAACC,IAAI,CAAC1C,KAAK,CAACA,KAAK,CAAC8C,MAAM,CAAC,CAAC2B,OAAO,CAAC6C,KAAK,IAAG;YAC9C/D,YAAY,IAAI,KAAK+D,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACL/D,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC5E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3H,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIyD,QAAQ,EAAE;MACnBzD,YAAY,GAAG,0BAA0ByD,QAAQ,EAAE;;IAGrD,OAAO5I,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC+C,YAAY,CAAC,CAAC;EAClD;EAEQ/B,iBAAiBA,CAACxB,KAAwB;IAChD,IAAIuD,YAAY,GAAG,sCAAsC;IACzD,MAAMyD,QAAQ,GAAGhH,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAIzD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAIgH,QAAQ,EAAE;MACZzD,YAAY,GAAG,uBAAuByD,QAAQ,EAAE;KACjD,MAAM,IAAIhH,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BM,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,mBAAmB;MAClC,IAAIvD,KAAK,CAACA,KAAK,EAAE8C,MAAM,EAAE;QACvB,MAAMmE,gBAAgB,GAAGxE,MAAM,CAACmF,MAAM,CAAC5H,KAAK,CAACA,KAAK,CAAC8C,MAAM,CAAC,CAAC+E,IAAI,EAAE;QACjEtE,YAAY,IAAI,aAAa0D,gBAAgB,CAAClE,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAI/C,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC5E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3H,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,mCAAmC;;IAGpD,OAAOnF,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC+C,YAAY,CAAC,CAAC;EAClD;EAEQtD,WAAWA,CAACD,KAAwB;IAC1C,IAAIuD,YAAY,GAAG,mBAAmB;IACtC,MAAMyD,QAAQ,GAAGhH,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK;IAE3D,IAAIuD,QAAQ,EAAE;MACZzD,YAAY,GAAGyD,QAAQ;KACxB,MAAM,IAAIhH,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BM,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,cAAc;MAC7B,IAAI,CAAC5E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3H,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BM,YAAY,GAAG,kCAAkC;;IAGnD,OAAOnF,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC+C,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAuE,CAAA,G;qBAx1BUtJ,aAAa,EAAAuJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb7J,aAAa;IAAA8J,OAAA,EAAb9J,aAAa,CAAA+J,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}