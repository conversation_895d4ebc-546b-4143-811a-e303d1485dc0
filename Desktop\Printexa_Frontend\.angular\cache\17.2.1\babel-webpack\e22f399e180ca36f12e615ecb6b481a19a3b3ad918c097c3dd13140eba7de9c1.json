{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n      console.log('Clients récupérés du serveur:', clients?.length || 0);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      console.error('Erreur lors de la récupération des clients:', error);\n      // En cas d'erreur, on met un tableau vide pour éviter les erreurs dans l'interface\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    return this.http.put(`${this.baseUrl}${id}`, client, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(catchError(this.handleError));\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Gestion des erreurs\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "console", "log", "length", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "email", "validateEmail", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "telephone", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "put", "clearCurrentClient", "deleteClient", "delete", "deleteSelectedClients", "ids", "removeItem", "data", "getDialogData", "dialogData", "replace", "c", "r", "Math", "random", "v", "toString", "re", "test", "errorMessage", "apiError", "message", "title", "status", "errors", "validationErrors", "Object", "values", "flat", "join", "navigate", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n        console.log('Clients récupérés du serveur:', clients?.length || 0);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        console.error('Erreur lors de la récupération des clients:', error);\r\n        // En cas d'erreur, on met un tableau vide pour éviter les erreurs dans l'interface\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\r\n    return this.http.put<void>(`${this.baseUrl}${id}`, client, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIV,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAW,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIZ,eAAe,CAAgB,IAAI,CAACa,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAInB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIuB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;MACnCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,OAAO,EAAEI,MAAM,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC,EACF5B,UAAU,CAAC6B,KAAK,IAAG;MACjB,IAAI,CAACrB,YAAY,GAAG,KAAK;MACzBkB,OAAO,CAACG,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MACnE;MACA,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACK,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAAC5B,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAACkC,MAAM,IAAG;MACXnB,YAAY,CAACoB,OAAO,CAAC,eAAe,EAAElB,IAAI,CAACmB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACxB,oBAAoB,CAACgB,IAAI,CAACQ,MAAM,CAAC;IACxC,CAAC,CAAC,EACFjC,UAAU,CAAC,IAAI,CAAC8B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOnC,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOzC,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACO,KAAK,IAAI,CAAC,IAAI,CAACC,aAAa,CAACR,MAAM,CAACO,KAAK,CAAC,EAAE;MACrD,OAAO1C,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMxB,UAAU,GAA0B;MACxCmB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACU,YAAY,EAAE;MACpCJ,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBI,MAAM,EAAEV,MAAM,CAACU,MAAM,EAAEJ,IAAI,EAAE;MAC7BK,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEL,IAAI,EAAE;MACnCC,KAAK,EAAEP,MAAM,CAACO,KAAK,EAAED,IAAI,EAAE;MAC3BM,SAAS,EAAEZ,MAAM,CAACY,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAACnC,IAAI,CAAC0C,IAAI,CAAS,IAAI,CAACxC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B6B,OAAO,EAAE;KACV,CAAC,CAACxB,IAAI,CACLtB,GAAG,CAAE+C,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIZ,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOY,SAAS;IAClB,CAAC,CAAC,EACFlD,GAAG,CAAEkD,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAAC5C,UAAU,CAAC6C,KAAK;MACzC,IAAI,CAAC7C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG0B,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFjD,UAAU,CAAC,IAAI,CAACqD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAACtB,EAAU,EAAEC,MAAuB;IAC9C,OAAO,IAAI,CAAC7B,IAAI,CAACmD,GAAG,CAAO,GAAG,IAAI,CAACjD,OAAO,GAAG0B,EAAE,EAAE,EAAEC,MAAM,EAAE;MAAEX,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC7FxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACyD,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFxD,UAAU,CAAC,IAAI,CAAC8B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA2B,YAAYA,CAACzB,EAAU;IACrB,OAAO,IAAI,CAAC5B,IAAI,CAACsD,MAAM,CAAO,GAAG,IAAI,CAACpD,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACyD,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFxD,UAAU,CAAC,IAAI,CAAC8B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA6B,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAAChC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO9B,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF,OAAO,IAAI,CAACjC,IAAI,CAACsD,MAAM,CAAC,GAAG,IAAI,CAACpD,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BgC,IAAI,EAAEU;KACP,CAAC,CAACrC,IAAI,CACLvB,UAAU,CAAC,IAAI,CAAC8B,WAAW,CAAC,CAC7B;EACH;EAEA;EACA0B,kBAAkBA,CAAA;IAChB1C,YAAY,CAAC+C,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACpD,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIqC,IAAIA,CAAA;IACN,OAAO,IAAI,CAACvD,UAAU,CAAC6C,KAAK;EAC9B;EAEAW,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQtB,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACuB,OAAO,CAAC,OAAO,EAAE,UAASC,CAAC;MACvE,MAAMC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGJ,CAAC,KAAK,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQ9B,aAAaA,CAACD,KAAa;IACjC,MAAMgC,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAACC,IAAI,CAACjC,KAAK,CAAC;EACvB;EAEA;EACQa,iBAAiBA,CAACxB,KAAwB;IAChD,IAAI6C,YAAY,GAAG,sCAAsC;IACzD,MAAMC,QAAQ,GAAG9C,KAAK,CAACA,KAAK,EAAE+C,OAAO,IAAI/C,KAAK,CAACA,KAAK,EAAEgD,KAAK,IAAIhD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAI8C,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAI9C,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BJ,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAI7C,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,mBAAmB;MAClC,IAAI7C,KAAK,CAACA,KAAK,EAAEkD,MAAM,EAAE;QACvB,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAACrD,KAAK,CAACA,KAAK,CAACkD,MAAM,CAAC,CAACI,IAAI,EAAE;QACjET,YAAY,IAAI,aAAaM,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAIvD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACrE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,mCAAmC;;IAGpD,OAAO5E,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAACqC,YAAY,CAAC,CAAC;EAClD;EAEQ5C,WAAWA,CAACD,KAAwB;IAC1C,IAAI6C,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAG9C,KAAK,CAACA,KAAK,EAAE+C,OAAO,IAAI/C,KAAK,CAACA,KAAK,EAAEgD,KAAK;IAE3D,IAAIF,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAI9C,KAAK,CAACiD,MAAM,KAAK,CAAC,EAAE;MAC7BJ,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAI7C,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAI7C,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACrE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxD,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAI7C,KAAK,CAACiD,MAAM,KAAK,GAAG,EAAE;MAC/BJ,YAAY,GAAG,kCAAkC;;IAGnD,OAAO5E,UAAU,CAAC,MAAM,IAAIuC,KAAK,CAACqC,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAY,CAAA,G;qBA/MUpF,aAAa,EAAAqF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb3F,aAAa;IAAA4F,OAAA,EAAb5F,aAAa,CAAA6F,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}