{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { tap, catchError, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(null);\n    this.currentClient$ = this.currentClientSubject.asObservable();\n    const savedClient = localStorage.getItem('currentClient');\n    if (savedClient) {\n      this.currentClientSubject.next(JSON.parse(savedClient));\n    }\n  }\n  getHeaders() {\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n  handleError(error) {\n    let errorMessage = 'Une erreur est survenue';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders()\n    }).pipe(tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) actualId = clientByCode.id;\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\n    }\n    // Nettoyer les données\n    const cleanedClient = {};\n    Object.keys(client).forEach(key => {\n      if (key !== 'id' && client[key]?.toString().trim()) {\n        cleanedClient[key] = client[key];\n      }\n    });\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = {\n          ...currentData[index],\n          ...cleanedClient\n        };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(error => {\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || error.status === 400 && error.error?.message?.includes('validation')) {\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId);\n    }));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(error => {\n      // Simulation locale en cas d'erreur\n      if (error.status === 0 || error.status === 500) {\n        return this.simulateLocalDeletion(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Simulation locale de mise à jour\n  simulateLocalUpdate(id, updateData) {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Simulation locale de suppression\n  simulateLocalDeletion(ids) {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length\n        });\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "currentClient$", "asObservable", "savedClient", "localStorage", "getItem", "next", "JSON", "parse", "getHeaders", "handleError", "error", "errorMessage", "message", "status", "navigate", "Error", "getAllClients", "get", "headers", "pipe", "clients", "getClientById", "id", "client", "setItem", "stringify", "createClient", "code", "trim", "clientData", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "post", "newClient", "currentData", "value", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "cleanedClient", "Object", "keys", "for<PERSON>ach", "key", "toString", "length", "put", "index", "findIndex", "updatedClient", "includes", "simulateLocalUpdate", "deleteSelectedClients", "ids", "delete", "body", "filter", "simulateLocalDeletion", "deleteClient", "updateData", "observer", "setTimeout", "complete", "deletedCount", "clearCurrentClient", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError } from 'rxjs';\nimport { tap, catchError, map } from 'rxjs/operators';\nimport { Router } from '@angular/router';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private readonly baseUrl = 'https://localhost:5001/api/Client/';\n  \n  dataChange: BehaviorSubject<Client[]> = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n  \n  private currentClientSubject = new BehaviorSubject<Client | null>(null);\n  public currentClient$ = this.currentClientSubject.asObservable();\n\n  constructor(private http: HttpClient, private router: Router) {\n    const savedClient = localStorage.getItem('currentClient');\n    if (savedClient) {\n      this.currentClientSubject.next(JSON.parse(savedClient));\n    }\n  }\n\n  private getHeaders(): HttpHeaders {\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      'Accept': 'application/json'\n    });\n  }\n\n  private handleError(error: HttpErrorResponse) {\n    let errorMessage = 'Une erreur est survenue';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true;\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false;\n        this.dataChange.next(clients || []);\n      }),\n      catchError(error => {\n        this.isTblLoading = false;\n        this.dataChange.next([]);\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n\n    return this.http.post<Client>(this.baseUrl, clientData, { headers: this.getHeaders() }).pipe(\n      tap(newClient => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Mettre à jour un client\nupdateClient(id: string, client: UpdateClientDto): Observable<Client> {\n  if (!id?.trim() || !client) {\n    return throwError(() => new Error('ID et données client requis'));\n  }\n\n  // Résoudre l'ID si c'est un code client\n  let actualId = id;\n  const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n  if (isCode) {\n    const clientByCode = this.dataChange.value.find(c => c.code === id);\n    if (clientByCode) actualId = clientByCode.id;\n  }\n\n  // Valider que l'ID final est un GUID\n  const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n  if (!isGuid) {\n    return throwError(() => new Error(`ID invalide: ${actualId}`));\n  }\n\n  // Nettoyer les données\n  const cleanedClient: UpdateClientDto = {};\n  Object.keys(client).forEach(key => {\n    if (key !== 'id' && client[key as keyof UpdateClientDto]?.toString().trim()) {\n      cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];\n    }\n  });\n\n  if (Object.keys(cleanedClient).length === 0) {\n    return throwError(() => new Error('Aucune donnée à mettre à jour'));\n  }\n\n  return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, { \n    headers: this.getHeaders()\n  }).pipe(\n    tap(() => {\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = { ...currentData[index], ...cleanedClient };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }),\n    catchError(error => {\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || (error.status === 400 && error.error?.message?.includes('validation'))) {\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId)!;\n    })\n  );\n}\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n      }),\n      catchError(error => {\n        // Simulation locale en cas d'erreur\n        if (error.status === 0 || error.status === 500) {\n          return this.simulateLocalDeletion(ids);\n        }\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => client.id !== id));\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Simulation locale de mise à jour\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    \n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n\n    const updatedClient: Client = { ...currentData[index], ...updateData };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    \n    return new Observable<Client>(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Simulation locale de suppression\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    \n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({ deletedCount: ids.length });\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthodes utilitaires\n  private generateGuid(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAA2B,sBAAsB;AACjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;;;;AAOrD,OAAM,MAAOC,aAAa;EAUxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IATnC,KAAAC,OAAO,GAAG,oCAAoC;IAE/D,KAAAC,UAAU,GAA8B,IAAIX,eAAe,CAAW,EAAE,CAAC;IAEzE,KAAAY,YAAY,GAAG,IAAI;IAEX,KAAAC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAAC;IAChE,KAAAc,cAAc,GAAG,IAAI,CAACD,oBAAoB,CAACE,YAAY,EAAE;IAG9D,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACzD,IAAIF,WAAW,EAAE;MACf,IAAI,CAACH,oBAAoB,CAACM,IAAI,CAACC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC,CAAC;;EAE3D;EAEQM,UAAUA,CAAA;IAChB,OAAO,IAAIvB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,QAAQ,EAAE;KACX,CAAC;EACJ;EAEQwB,WAAWA,CAACC,KAAwB;IAC1C,IAAIC,YAAY,GAAG,yBAAyB;IAC5C,IAAID,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAE;MACxBD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACE,OAAO;KACnC,MAAM,IAAIF,KAAK,CAACG,MAAM,KAAK,CAAC,EAAE;MAC7BF,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAID,KAAK,CAACG,MAAM,KAAK,GAAG,EAAE;MAC/BF,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAChB,MAAM,CAACmB,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAElC,OAAO1B,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAACJ,YAAY,CAAC,CAAC;EAClD;EAEA;EACAK,aAAaA,CAAA;IACX,IAAI,CAAClB,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACuB,GAAG,CAAW,IAAI,CAACrB,OAAO,EAAE;MAAEsB,OAAO,EAAE,IAAI,CAACV,UAAU;IAAE,CAAE,CAAC,CAACW,IAAI,CAC/E9B,GAAG,CAAC+B,OAAO,IAAG;MACZ,IAAI,CAACtB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACQ,IAAI,CAACe,OAAO,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,EACF9B,UAAU,CAACoB,KAAK,IAAG;MACjB,IAAI,CAACZ,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACQ,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACI,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAW,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAAC5B,IAAI,CAACuB,GAAG,CAAS,GAAG,IAAI,CAACrB,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEJ,OAAO,EAAE,IAAI,CAACV,UAAU;IAAE,CAAE,CAAC,CAACW,IAAI,CACvF9B,GAAG,CAACkC,MAAM,IAAG;MACXpB,YAAY,CAACqB,OAAO,CAAC,eAAe,EAAElB,IAAI,CAACmB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACxB,oBAAoB,CAACM,IAAI,CAACkB,MAAM,CAAC;IACxC,CAAC,CAAC,EACFjC,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;EACAiB,YAAYA,CAACH,MAA6B;IACxC,IAAI,CAACA,MAAM,EAAEI,IAAI,EAAEC,IAAI,EAAE,EAAE;MACzB,OAAOxC,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D,MAAMc,UAAU,GAAG;MACjBP,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACQ,YAAY,EAAE;MACpCH,IAAI,EAAEJ,MAAM,CAACI,IAAI,CAACC,IAAI,EAAE;MACxBG,MAAM,EAAER,MAAM,CAACQ,MAAM,EAAEH,IAAI,EAAE;MAC7BI,SAAS,EAAET,MAAM,CAACS,SAAS,EAAEJ,IAAI,EAAE;MACnCK,KAAK,EAAEV,MAAM,CAACU,KAAK,EAAEL,IAAI,EAAE;MAC3BM,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAAClC,IAAI,CAACyC,IAAI,CAAS,IAAI,CAACvC,OAAO,EAAEiC,UAAU,EAAE;MAAEX,OAAO,EAAE,IAAI,CAACV,UAAU;IAAE,CAAE,CAAC,CAACW,IAAI,CAC1F9B,GAAG,CAAC+C,SAAS,IAAG;MACd,MAAMC,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;MACzC,IAAI,CAACzC,UAAU,CAACQ,IAAI,CAAC,CAAC,GAAGgC,WAAW,EAAED,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF9C,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;EACF8B,YAAYA,CAACjB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEM,IAAI,EAAE,IAAI,CAACL,MAAM,EAAE;MAC1B,OAAOnC,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIyB,QAAQ,GAAGlB,EAAE;IACjB,MAAMmB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACpB,EAAE,CAAC;IAC1C,IAAImB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAAC9C,UAAU,CAACyC,KAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,IAAI,KAAKL,EAAE,CAAC;MACnE,IAAIqB,YAAY,EAAEH,QAAQ,GAAGG,YAAY,CAACrB,EAAE;;IAG9C;IACA,MAAMwB,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAO1D,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,gBAAgByB,QAAQ,EAAE,CAAC,CAAC;;IAGhE;IACA,MAAMO,aAAa,GAAoB,EAAE;IACzCC,MAAM,CAACC,IAAI,CAAC1B,MAAM,CAAC,CAAC2B,OAAO,CAACC,GAAG,IAAG;MAChC,IAAIA,GAAG,KAAK,IAAI,IAAI5B,MAAM,CAAC4B,GAA4B,CAAC,EAAEC,QAAQ,EAAE,CAACxB,IAAI,EAAE,EAAE;QAC3EmB,aAAa,CAACI,GAA4B,CAAC,GAAG5B,MAAM,CAAC4B,GAA4B,CAAC;;IAEtF,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAOjE,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE,OAAO,IAAI,CAACrB,IAAI,CAAC4D,GAAG,CAAC,GAAG,IAAI,CAAC1D,OAAO,GAAG4C,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChE7B,OAAO,EAAE,IAAI,CAACV,UAAU;KACzB,CAAC,CAACW,IAAI,CACL9B,GAAG,CAAC,MAAK;MACP;MACA,MAAMgD,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;MACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAACX,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKkB,QAAQ,CAAC;MAC3D,IAAIe,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAME,aAAa,GAAG;UAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;UAAE,GAAGR;QAAa,CAAE;QACjEV,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;QAClC,IAAI,CAAC5D,UAAU,CAACQ,IAAI,CAAC,CAAC,GAAGgC,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACF/C,UAAU,CAACoB,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,IAAKH,KAAK,CAACG,MAAM,KAAK,GAAG,IAAIH,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAE8C,QAAQ,CAAC,YAAY,CAAE,EAAE;QAChG,OAAO,IAAI,CAACC,mBAAmB,CAACnB,QAAQ,EAAEO,aAAa,CAAC;;MAE1D,OAAO,IAAI,CAACtC,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC;IACF;IACAnB,GAAG,CAAC,MAAK;MACP,MAAM8C,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;MACzC,OAAOD,WAAW,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKkB,QAAQ,CAAE;IAClD,CAAC,CAAC,CACH;EACH;EACE;EACAoB,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,EAAER,MAAM,EAAE;MAChB,OAAOjE,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,iBAAiB,CAAC,CAAC;;IAGvD,OAAO,IAAI,CAACrB,IAAI,CAACoE,MAAM,CAAC,GAAG,IAAI,CAAClE,OAAO,iBAAiB,EAAE;MACxDsB,OAAO,EAAE,IAAI,CAACV,UAAU,EAAE;MAC1BuD,IAAI,EAAEF;KACP,CAAC,CAAC1C,IAAI,CACL9B,GAAG,CAAC,MAAK;MACP,MAAMgD,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;MACzC,IAAI,CAACzC,UAAU,CAACQ,IAAI,CAACgC,WAAW,CAAC2B,MAAM,CAACzC,MAAM,IAAI,CAACsC,GAAG,CAACH,QAAQ,CAACnC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACFhC,UAAU,CAACoB,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,IAAIH,KAAK,CAACG,MAAM,KAAK,GAAG,EAAE;QAC9C,OAAO,IAAI,CAACoD,qBAAqB,CAACJ,GAAG,CAAC;;MAExC,OAAO,IAAI,CAACpD,WAAW,CAACC,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAwD,YAAYA,CAAC5C,EAAU;IACrB,OAAO,IAAI,CAAC5B,IAAI,CAACoE,MAAM,CAAO,GAAG,IAAI,CAAClE,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEJ,OAAO,EAAE,IAAI,CAACV,UAAU;IAAE,CAAE,CAAC,CAACW,IAAI,CACxF9B,GAAG,CAAC,MAAK;MACP,MAAMgD,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;MACzC,IAAI,CAACzC,UAAU,CAACQ,IAAI,CAACgC,WAAW,CAAC2B,MAAM,CAACzC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACFhC,UAAU,CAAC,IAAI,CAACmB,WAAW,CAAC,CAC7B;EACH;EAEA;EACQkD,mBAAmBA,CAACrC,EAAU,EAAE6C,UAA2B;IACjE,MAAM9B,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;IACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAACjC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIiC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOnE,UAAU,CAAC,MAAM,IAAI2B,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,MAAM0C,aAAa,GAAW;MAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;MAAE,GAAGY;IAAU,CAAE;IACtE9B,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;IAClC,IAAI,CAAC5D,UAAU,CAACQ,IAAI,CAAC,CAAC,GAAGgC,WAAW,CAAC,CAAC;IAEtC,OAAO,IAAIlD,UAAU,CAASiF,QAAQ,IAAG;MACvCC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/D,IAAI,CAACoD,aAAa,CAAC;QAC5BW,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACQL,qBAAqBA,CAACJ,GAAa;IACzC,MAAMxB,WAAW,GAAG,IAAI,CAACxC,UAAU,CAACyC,KAAK;IACzC,IAAI,CAACzC,UAAU,CAACQ,IAAI,CAACgC,WAAW,CAAC2B,MAAM,CAACzC,MAAM,IAAI,CAACsC,GAAG,CAACH,QAAQ,CAACnC,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,IAAInC,UAAU,CAACiF,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAAC/D,IAAI,CAAC;UAAEkE,YAAY,EAAEV,GAAG,CAACR;QAAM,CAAE,CAAC;QAC3Ce,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACAE,kBAAkBA,CAAA;IAChBrE,YAAY,CAACsE,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC1E,oBAAoB,CAACM,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIqE,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC7E,UAAU,CAACyC,KAAK;EAC9B;EAEAqC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQ9C,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAAC+C,OAAO,CAAC,OAAO,EAAE,UAAShC,CAAC;MACvE,MAAMiC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGpC,CAAC,KAAK,GAAG,GAAGiC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAAC7B,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC,QAAA8B,CAAA,G;qBA7OU1F,aAAa,EAAA2F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbjG,aAAa;IAAAkG,OAAA,EAAblG,aAAa,CAAAmG,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}