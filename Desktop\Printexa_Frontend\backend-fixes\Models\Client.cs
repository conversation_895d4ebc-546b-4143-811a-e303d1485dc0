using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Printexa.Models
{
    public class Client
    {
        [Key]
        [Column("id")]
        [StringLength(255)]
        public string Id { get; set; }

        [Column("code")]
        [StringLength(255)]
        public string Code { get; set; }

        [Column("syntax")]
        [StringLength(255)]
        public string Syntax { get; set; }

        [Column("MatFiscal")]
        [StringLength(255)]
        public string MatFiscal { get; set; }

        [Column("email")]
        [StringLength(255)]
        public string Email { get; set; }

        [Column("Telephone")]
        [StringLength(255)]
        public string Telephone { get; set; }

        // Relation 1-* avec Devis
        public ICollection<Devis> Devis { get; set; }
    }
}
