{"ast": null, "code": "export const ADMIN_ROUTE = [{\n  path: 'dashboard',\n  loadChildren: () => import('./dashboard/dashboard.routes').then(m => m.ADMIN_DASHBOARD_ROUTE)\n}, {\n  path: 'projects',\n  loadChildren: () => import('./projects/projects.routes').then(m => m.PROJECT_ROUTE)\n}, {\n  path: 'employees',\n  loadChildren: () => import('./employees/employees.routes').then(m => m.ADMIN_EMPLOYEE_ROUTE)\n}, {\n  path: 'clients',\n  loadChildren: () => import('./clients/clients.routes').then(m => m.ADMIN_CLIENT_ROUTE)\n}, {\n  path: 'leaves',\n  loadChildren: () => import('./leaves/leaves.routes').then(m => m.LEAVE_ROUTE)\n}, {\n  path: 'accounts',\n  loadChildren: () => import('./accounts/accounts.routes').then(m => m.ACCOUNT_ROUTE)\n}, {\n  path: 'holidays',\n  loadChildren: () => import('./holidays/holidays.routes').then(m => m.HOLIDAY_ROUTE)\n}, {\n  path: 'attendance',\n  loadChildren: () => import('./attendance/attendance.routes').then(m => m.ATTENDANCE_ROUTE)\n}, {\n  path: 'payroll',\n  loadChildren: () => import('./payroll/payroll.routes').then(m => m.PAYROLL_ROUTE)\n}, {\n  path: 'leads',\n  loadChildren: () => import('./leads/leads.routes').then(m => m.LEADS_ROUTE)\n}, {\n  path: 'jobs',\n  loadChildren: () => import('./jobs/jobs.routes').then(m => m.JOBS_ROUTE)\n}, {\n  path: 'reports',\n  loadChildren: () => import('./reports/reports.routes').then(m => m.REPORT_ROUTE)\n}, {\n  path: 'gestion-client',\n  loadChildren: () => import('./gestion_Client/client.routes').then(m => m.CLIENT_ROUTES)\n}, {\n  path: 'gestion',\n  loadChildren: () => import('./gestion_Produit/pro.routes').then(m => m.CLIENT_ROUTES)\n}];", "map": {"version": 3, "names": ["ADMIN_ROUTE", "path", "loadChildren", "then", "m", "ADMIN_DASHBOARD_ROUTE", "PROJECT_ROUTE", "ADMIN_EMPLOYEE_ROUTE", "ADMIN_CLIENT_ROUTE", "LEAVE_ROUTE", "ACCOUNT_ROUTE", "HOLIDAY_ROUTE", "ATTENDANCE_ROUTE", "PAYROLL_ROUTE", "LEADS_ROUTE", "JOBS_ROUTE", "REPORT_ROUTE", "CLIENT_ROUTES"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\admin.routes.ts"], "sourcesContent": ["import { Route } from '@angular/router';\r\n\r\nexport const ADMIN_ROUTE: Route[] = [\r\n  {\r\n    path: 'dashboard',\r\n    loadChildren: () =>\r\n      import('./dashboard/dashboard.routes').then((m) => m.ADMIN_DASHBOARD_ROUTE),\r\n  },\r\n  {\r\n    path: 'projects',\r\n    loadChildren: () =>\r\n      import('./projects/projects.routes').then((m) => m.PROJECT_ROUTE),\r\n  },\r\n  {\r\n    path: 'employees',\r\n    loadChildren: () =>\r\n      import('./employees/employees.routes').then((m) => m.ADMIN_EMPLOYEE_ROUTE),\r\n  },\r\n  {\r\n    path: 'clients',\r\n    loadChildren: () =>\r\n      import('./clients/clients.routes').then((m) => m.ADMIN_CLIENT_ROUTE),\r\n  },\r\n  {\r\n    path: 'leaves',\r\n    loadChildren: () =>\r\n      import('./leaves/leaves.routes').then((m) => m.LEAVE_ROUTE),\r\n  },\r\n  {\r\n    path: 'accounts',\r\n    loadChildren: () =>\r\n      import('./accounts/accounts.routes').then((m) => m.ACCOUNT_ROUTE),\r\n  },\r\n  {\r\n    path: 'holidays',\r\n    loadChildren: () =>\r\n      import('./holidays/holidays.routes').then((m) => m.HOLIDAY_ROUTE),\r\n  },\r\n  {\r\n    path: 'attendance',\r\n    loadChildren: () =>\r\n      import('./attendance/attendance.routes').then((m) => m.ATTENDANCE_ROUTE),\r\n  },\r\n  {\r\n    path: 'payroll',\r\n    loadChildren: () =>\r\n      import('./payroll/payroll.routes').then((m) => m.PAYROLL_ROUTE),\r\n  },\r\n  {\r\n    path: 'leads',\r\n    loadChildren: () =>\r\n      import('./leads/leads.routes').then((m) => m.LEADS_ROUTE),\r\n  },\r\n  {\r\n    path: 'jobs',\r\n    loadChildren: () => import('./jobs/jobs.routes').then((m) => m.JOBS_ROUTE),\r\n  },\r\n  {\r\n    path: 'reports',\r\n    loadChildren: () =>\r\n      import('./reports/reports.routes').then((m) => m.REPORT_ROUTE),\r\n  },\r\n  {\r\n    path: 'gestion-client',\r\n    loadChildren: () =>\r\n      import('./gestion_Client/client.routes').then((m) => m.CLIENT_ROUTES),\r\n  },\r\n  {\r\n    path: 'gestion',\r\n    loadChildren: () =>\r\n      import('./gestion_Produit/pro.routes').then((m) => m.CLIENT_ROUTES),\r\n  },\r\n];\r\n\r\n"], "mappings": "AAEA,OAAO,MAAMA,WAAW,GAAY,CAClC;EACEC,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,qBAAqB;CAC7E,EACD;EACEJ,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACE,aAAa;CACnE,EACD;EACEL,IAAI,EAAE,WAAW;EACjBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACG,oBAAoB;CAC5E,EACD;EACEN,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACI,kBAAkB;CACtE,EACD;EACEP,IAAI,EAAE,QAAQ;EACdC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,wBAAwB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACK,WAAW;CAC7D,EACD;EACER,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACM,aAAa;CACnE,EACD;EACET,IAAI,EAAE,UAAU;EAChBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,4BAA4B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACO,aAAa;CACnE,EACD;EACEV,IAAI,EAAE,YAAY;EAClBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACQ,gBAAgB;CAC1E,EACD;EACEX,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACS,aAAa;CACjE,EACD;EACEZ,IAAI,EAAE,OAAO;EACbC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,sBAAsB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACU,WAAW;CAC3D,EACD;EACEb,IAAI,EAAE,MAAM;EACZC,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,oBAAoB,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACW,UAAU;CAC1E,EACD;EACEd,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,0BAA0B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACY,YAAY;CAChE,EACD;EACEf,IAAI,EAAE,gBAAgB;EACtBC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,gCAAgC,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACa,aAAa;CACvE,EACD;EACEhB,IAAI,EAAE,SAAS;EACfC,YAAY,EAAEA,CAAA,KACZ,MAAM,CAAC,8BAA8B,CAAC,CAACC,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACa,aAAa;CACrE,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}