{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialog<PERSON><PERSON><PERSON>, MatDialogContent, MatDialogActions } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/common\";\nfunction BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"email\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1, \"phone\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtemplate(1, BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_1_Template, 2, 0, \"mat-icon\", 24)(2, BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_2_Template, 2, 0, \"mat-icon\", 24);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const client_r3 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", client_r3.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", client_r3.telephone);\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_46_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18)(1, \"div\", 19)(2, \"span\", 20);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 21);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_Template, 3, 2, \"span\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const client_r3 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(client_r3.code);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(client_r3.syntax || \"Sans raison sociale\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", client_r3.email || client_r3.telephone);\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"h5\");\n    i0.ɵɵtext(2, \"Clients \\u00E0 supprimer :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 16);\n    i0.ɵɵtemplate(4, BulkDeleteClientConfirmationComponent_div_46_div_4_Template, 7, 3, \"div\", 17);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.data.selectedClients);\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_47_span_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r8 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"... et \", ctx_r8.data.totalCount - 10, \" autres\");\n  }\n}\nfunction BulkDeleteClientConfirmationComponent_div_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"p\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Trop de clients s\\u00E9lectionn\\u00E9s pour les afficher individuellement. Consultez le r\\u00E9sum\\u00E9 ci-dessus. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 26)(6, \"strong\");\n    i0.ɵɵtext(7, \"Codes des clients :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 27);\n    i0.ɵɵtext(9);\n    i0.ɵɵtemplate(10, BulkDeleteClientConfirmationComponent_div_47_span_10_Template, 2, 1, \"span\", 24);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.getClientCodes().slice(0, 10).join(\", \"), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.data.totalCount > 10);\n  }\n}\nexport class BulkDeleteClientConfirmationComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onNoClick() {\n    this.dialogRef.close(false);\n  }\n  confirmDelete() {\n    this.dialogRef.close(true);\n  }\n  // Obtenir un résumé des clients par type (avec/sans email, avec/sans téléphone)\n  getClientsSummary() {\n    const summary = {\n      withEmail: 0,\n      withPhone: 0,\n      withMatFiscal: 0\n    };\n    this.data.selectedClients.forEach(client => {\n      if (client.email) summary.withEmail++;\n      if (client.telephone) summary.withPhone++;\n      if (client.matFiscal) summary.withMatFiscal++;\n    });\n    return summary;\n  }\n  // Obtenir les codes des clients pour affichage\n  getClientCodes() {\n    return this.data.selectedClients.map(client => client.code);\n  }\n  static #_ = this.ɵfac = function BulkDeleteClientConfirmationComponent_Factory(t) {\n    return new (t || BulkDeleteClientConfirmationComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BulkDeleteClientConfirmationComponent,\n    selectors: [[\"app-bulk-delete-client-confirmation\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 57,\n    vars: 7,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [1, \"warning-icon\"], [\"mat-dialog-content\", \"\"], [1, \"warning-message\"], [1, \"summary-section\"], [1, \"summary-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"clients-preview\", 4, \"ngIf\"], [\"class\", \"large-selection-message\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"delete-btn\", 3, \"click\"], [\"mat-flat-button\", \"\", 1, \"cancel-btn\", 3, \"click\"], [1, \"clients-preview\"], [1, \"client-list\"], [\"class\", \"client-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"client-item\"], [1, \"client-info\"], [1, \"client-code\"], [1, \"client-syntax\"], [\"class\", \"client-contact\", 4, \"ngIf\"], [1, \"client-contact\"], [4, \"ngIf\"], [1, \"large-selection-message\"], [1, \"codes-preview\"], [1, \"codes-list\"]],\n    template: function BulkDeleteClientConfirmationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" Confirmation de suppression en lot \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"p\")(8, \"strong\");\n        i0.ɵɵtext(9, \"Attention !\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Vous \\u00EAtes sur le point de supprimer d\\u00E9finitivement \");\n        i0.ɵɵelementStart(11, \"strong\");\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(13, \". Cette action est irr\\u00E9versible. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"h4\");\n        i0.ɵɵtext(16, \"R\\u00E9sum\\u00E9 de la s\\u00E9lection :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 6)(18, \"div\", 7)(19, \"mat-icon\");\n        i0.ɵɵtext(20, \"people\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"span\", 8);\n        i0.ɵɵtext(22, \"Nombre de clients :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 9);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 7)(26, \"mat-icon\");\n        i0.ɵɵtext(27, \"email\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"span\", 8);\n        i0.ɵɵtext(29, \"Avec email :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"span\", 9);\n        i0.ɵɵtext(31);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(32, \"div\", 7)(33, \"mat-icon\");\n        i0.ɵɵtext(34, \"phone\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(35, \"span\", 8);\n        i0.ɵɵtext(36, \"Avec t\\u00E9l\\u00E9phone :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(37, \"span\", 9);\n        i0.ɵɵtext(38);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(39, \"div\", 7)(40, \"mat-icon\");\n        i0.ɵɵtext(41, \"receipt_long\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(42, \"span\", 8);\n        i0.ɵɵtext(43, \"Avec matricule fiscal :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(44, \"span\", 9);\n        i0.ɵɵtext(45);\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵtemplate(46, BulkDeleteClientConfirmationComponent_div_46_Template, 5, 1, \"div\", 10)(47, BulkDeleteClientConfirmationComponent_div_47_Template, 11, 2, \"div\", 11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(48, \"div\", 12)(49, \"button\", 13);\n        i0.ɵɵlistener(\"click\", function BulkDeleteClientConfirmationComponent_Template_button_click_49_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵelementStart(50, \"mat-icon\");\n        i0.ɵɵtext(51, \"delete_forever\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(52, \" Supprimer tout \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(53, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function BulkDeleteClientConfirmationComponent_Template_button_click_53_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵelementStart(54, \"mat-icon\");\n        i0.ɵɵtext(55, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(56, \" Annuler \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate1(\"\", ctx.data.totalCount, \" client(s)\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(ctx.data.totalCount);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.getClientsSummary().withEmail);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.getClientsSummary().withPhone);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(ctx.getClientsSummary().withMatFiscal);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.data.selectedClients.length <= 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.data.selectedClients.length > 5);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i2.MatButton, MatIconModule, i3.MatIcon, CommonModule, i4.NgForOf, i4.NgIf],\n    styles: [\".container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 600px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #d32f2f;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 28px;\\n  color: #ff9800;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n  color: #856404;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-weight: 500;\\n  line-height: 1.5;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 12px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background-color: white;\\n  border-radius: 6px;\\n  border: 1px solid #dee2e6;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  background-color: white;\\n  border: 1px solid #dee2e6;\\n  border-radius: 6px;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-code[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  min-width: 80px;\\n  text-align: center;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-syntax[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-contact[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 4px;\\n}\\n.container[_ngcontent-%COMP%]   .clients-preview[_ngcontent-%COMP%]   .client-list[_ngcontent-%COMP%]   .client-item[_ngcontent-%COMP%]   .client-info[_ngcontent-%COMP%]   .client-contact[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 16px;\\n  color: #28a745;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border: 1px solid #bbdefb;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0 0 12px 0;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%]   .codes-preview[_ngcontent-%COMP%]   .codes-list[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 14px;\\n  line-height: 1.4;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n  margin-top: 20px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%] {\\n  background-color: #d32f2f;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #b71c1c;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);\\n}\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n.container[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatButtonModule", "MatIconModule", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_1_Template", "BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_mat_icon_2_Template", "ɵɵadvance", "ɵɵproperty", "client_r3", "email", "telephone", "BulkDeleteClientConfirmationComponent_div_46_div_4_span_6_Template", "ɵɵtextInterpolate", "code", "syntax", "BulkDeleteClientConfirmationComponent_div_46_div_4_Template", "ctx_r0", "data", "selectedClients", "ɵɵtextInterpolate1", "ctx_r8", "totalCount", "BulkDeleteClientConfirmationComponent_div_47_span_10_Template", "ctx_r1", "getClientCodes", "slice", "join", "BulkDeleteClientConfirmationComponent", "constructor", "dialogRef", "onNoClick", "close", "confirmDelete", "getClientsSummary", "summary", "withEmail", "withPhone", "withMatFiscal", "for<PERSON>ach", "client", "mat<PERSON><PERSON><PERSON>", "map", "_", "ɵɵdirectiveInject", "i1", "MatDialogRef", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BulkDeleteClientConfirmationComponent_Template", "rf", "ctx", "BulkDeleteClientConfirmationComponent_div_46_Template", "BulkDeleteClientConfirmationComponent_div_47_Template", "ɵɵlistener", "BulkDeleteClientConfirmationComponent_Template_button_click_49_listener", "BulkDeleteClientConfirmationComponent_Template_button_click_53_listener", "length", "i2", "MatButton", "i3", "MatIcon", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\bulk-delete-client-confirmation\\bulk-delete-client-confirmation.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\bulk-delete-client-confirmation\\bulk-delete-client-confirmation.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport { Client } from '../../Model/Client';\n\nexport interface BulkDeleteClientDialogData {\n  selectedClients: Client[];\n  totalCount: number;\n}\n\n@Component({\n  selector: 'app-bulk-delete-client-confirmation',\n  templateUrl: './bulk-delete-client-confirmation.component.html',\n  styleUrls: ['./bulk-delete-client-confirmation.component.scss'],\n  standalone: true,\n  imports: [\n    MatDialogTitle,\n    MatDialogContent,\n    MatDialogActions,\n    MatButtonModule,\n    MatIconModule,\n    CommonModule,\n  ],\n})\nexport class BulkDeleteClientConfirmationComponent {\n  constructor(\n    public dialogRef: MatDialogRef<BulkDeleteClientConfirmationComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: BulkDeleteClientDialogData\n  ) {}\n\n  onNoClick(): void {\n    this.dialogRef.close(false);\n  }\n\n  confirmDelete(): void {\n    this.dialogRef.close(true);\n  }\n\n  // Obtenir un résumé des clients par type (avec/sans email, avec/sans téléphone)\n  getClientsSummary(): { withEmail: number; withPhone: number; withMatFiscal: number } {\n    const summary = {\n      withEmail: 0,\n      withPhone: 0,\n      withMatFiscal: 0\n    };\n\n    this.data.selectedClients.forEach(client => {\n      if (client.email) summary.withEmail++;\n      if (client.telephone) summary.withPhone++;\n      if (client.matFiscal) summary.withMatFiscal++;\n    });\n\n    return summary;\n  }\n\n  // Obtenir les codes des clients pour affichage\n  getClientCodes(): string[] {\n    return this.data.selectedClients.map(client => client.code);\n  }\n}\n", "<div class=\"container\">\n  <h3 mat-dialog-title>\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\n    Confirmation de suppression en lot\n  </h3>\n  \n  <div mat-dialog-content>\n    <div class=\"warning-message\">\n      <p>\n        <strong>Attention !</strong> Vous êtes sur le point de supprimer définitivement \n        <strong>{{data.totalCount}} client(s)</strong>. Cette action est irréversible.\n      </p>\n    </div>\n\n    <div class=\"summary-section\">\n      <h4>Résumé de la sélection :</h4>\n      \n      <div class=\"summary-stats\">\n        <div class=\"stat-item\">\n          <mat-icon>people</mat-icon>\n          <span class=\"stat-label\">Nombre de clients :</span>\n          <span class=\"stat-value\">{{data.totalCount}}</span>\n        </div>\n        \n        <div class=\"stat-item\">\n          <mat-icon>email</mat-icon>\n          <span class=\"stat-label\">Avec email :</span>\n          <span class=\"stat-value\">{{getClientsSummary().withEmail}}</span>\n        </div>\n\n        <div class=\"stat-item\">\n          <mat-icon>phone</mat-icon>\n          <span class=\"stat-label\">Avec téléphone :</span>\n          <span class=\"stat-value\">{{getClientsSummary().withPhone}}</span>\n        </div>\n\n        <div class=\"stat-item\">\n          <mat-icon>receipt_long</mat-icon>\n          <span class=\"stat-label\">Avec matricule fiscal :</span>\n          <span class=\"stat-value\">{{getClientsSummary().withMatFiscal}}</span>\n        </div>\n      </div>\n    </div>\n\n    <!-- Affichage des premiers clients si la liste n'est pas trop longue -->\n    <div class=\"clients-preview\" *ngIf=\"data.selectedClients.length <= 5\">\n      <h5>Clients à supprimer :</h5>\n      <div class=\"client-list\">\n        <div class=\"client-item\" *ngFor=\"let client of data.selectedClients\">\n          <div class=\"client-info\">\n            <span class=\"client-code\">{{client.code}}</span>\n            <span class=\"client-syntax\">{{client.syntax || 'Sans raison sociale'}}</span>\n            <span class=\"client-contact\" *ngIf=\"client.email || client.telephone\">\n              <mat-icon *ngIf=\"client.email\">email</mat-icon>\n              <mat-icon *ngIf=\"client.telephone\">phone</mat-icon>\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message pour les grandes sélections -->\n    <div class=\"large-selection-message\" *ngIf=\"data.selectedClients.length > 5\">\n      <p>\n        <mat-icon>info</mat-icon>\n        Trop de clients sélectionnés pour les afficher individuellement. \n        Consultez le résumé ci-dessus.\n      </p>\n      <div class=\"codes-preview\">\n        <strong>Codes des clients :</strong>\n        <span class=\"codes-list\">{{getClientCodes().slice(0, 10).join(', ')}}\n          <span *ngIf=\"data.totalCount > 10\">... et {{data.totalCount - 10}} autres</span>\n        </span>\n      </div>\n    </div>\n  </div>\n  \n  <div mat-dialog-actions class=\"mb-1\">\n    <button mat-flat-button color=\"warn\" (click)=\"confirmDelete()\" class=\"delete-btn\">\n      <mat-icon>delete_forever</mat-icon>\n      Supprimer tout\n    </button>\n    <button mat-flat-button (click)=\"onNoClick()\" class=\"cancel-btn\">\n      <mat-icon>cancel</mat-icon>\n      Annuler\n    </button>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAQ,0BAA0B;AAE5H,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;ICiDhCC,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAC/CH,EAAA,CAAAC,cAAA,eAAmC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAFrDH,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAI,UAAA,IAAAC,6EAAA,uBAA+C,IAAAC,6EAAA;IAEjDN,EAAA,CAAAG,YAAA,EAAO;;;;IAFMH,EAAA,CAAAO,SAAA,EAAkB;IAAlBP,EAAA,CAAAQ,UAAA,SAAAC,SAAA,CAAAC,KAAA,CAAkB;IAClBV,EAAA,CAAAO,SAAA,EAAsB;IAAtBP,EAAA,CAAAQ,UAAA,SAAAC,SAAA,CAAAE,SAAA,CAAsB;;;;;IANvCX,EAAA,CAAAC,cAAA,cAAqE;IAEvCD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0C;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7EH,EAAA,CAAAI,UAAA,IAAAQ,kEAAA,mBAGO;IACTZ,EAAA,CAAAG,YAAA,EAAM;;;;IANsBH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAa,iBAAA,CAAAJ,SAAA,CAAAK,IAAA,CAAe;IACbd,EAAA,CAAAO,SAAA,GAA0C;IAA1CP,EAAA,CAAAa,iBAAA,CAAAJ,SAAA,CAAAM,MAAA,0BAA0C;IACxCf,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAQ,UAAA,SAAAC,SAAA,CAAAC,KAAA,IAAAD,SAAA,CAAAE,SAAA,CAAsC;;;;;IAP5EX,EAAA,CAAAC,cAAA,cAAsE;IAChED,EAAA,CAAAE,MAAA,iCAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,cAAyB;IACvBD,EAAA,CAAAI,UAAA,IAAAY,2DAAA,kBASM;IACRhB,EAAA,CAAAG,YAAA,EAAM;;;;IAVwCH,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAQ,UAAA,YAAAS,MAAA,CAAAC,IAAA,CAAAC,eAAA,CAAuB;;;;;IAuBjEnB,EAAA,CAAAC,cAAA,WAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA7CH,EAAA,CAAAO,SAAA,EAAsC;IAAtCP,EAAA,CAAAoB,kBAAA,YAAAC,MAAA,CAAAH,IAAA,CAAAI,UAAA,iBAAsC;;;;;IAT/EtB,EAAA,CAAAC,cAAA,cAA6E;IAE/DD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,4HAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,cAA2B;IACjBD,EAAA,CAAAE,MAAA,0BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GACvB;IAAAF,EAAA,CAAAI,UAAA,KAAAmB,6DAAA,mBAAgF;IAClFvB,EAAA,CAAAG,YAAA,EAAO;;;;IAFkBH,EAAA,CAAAO,SAAA,GACvB;IADuBP,EAAA,CAAAoB,kBAAA,KAAAI,MAAA,CAAAC,cAAA,GAAAC,KAAA,QAAAC,IAAA,YACvB;IAAO3B,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAQ,UAAA,SAAAgB,MAAA,CAAAN,IAAA,CAAAI,UAAA,MAA0B;;;AD7C3C,OAAM,MAAOM,qCAAqC;EAChDC,YACSC,SAA8D,EACrCZ,IAAgC;IADzD,KAAAY,SAAS,GAATA,SAAS;IACgB,KAAAZ,IAAI,GAAJA,IAAI;EACnC;EAEHa,SAASA,CAAA;IACP,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC,IAAI,CAAC;EAC5B;EAEA;EACAE,iBAAiBA,CAAA;IACf,MAAMC,OAAO,GAAG;MACdC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,CAAC;MACZC,aAAa,EAAE;KAChB;IAED,IAAI,CAACpB,IAAI,CAACC,eAAe,CAACoB,OAAO,CAACC,MAAM,IAAG;MACzC,IAAIA,MAAM,CAAC9B,KAAK,EAAEyB,OAAO,CAACC,SAAS,EAAE;MACrC,IAAII,MAAM,CAAC7B,SAAS,EAAEwB,OAAO,CAACE,SAAS,EAAE;MACzC,IAAIG,MAAM,CAACC,SAAS,EAAEN,OAAO,CAACG,aAAa,EAAE;IAC/C,CAAC,CAAC;IAEF,OAAOH,OAAO;EAChB;EAEA;EACAV,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACP,IAAI,CAACC,eAAe,CAACuB,GAAG,CAACF,MAAM,IAAIA,MAAM,CAAC1B,IAAI,CAAC;EAC7D;EAAC,QAAA6B,CAAA,G;qBAlCUf,qCAAqC,EAAA5B,EAAA,CAAA4C,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAA9C,EAAA,CAAA4C,iBAAA,CAGtCnD,eAAe;EAAA;EAAA,QAAAsD,EAAA,G;UAHdnB,qCAAqC;IAAAoB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAAlD,EAAA,CAAAmD,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+CAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC1BlDzD,EAAA,CAAAC,cAAA,aAAuB;QAEYD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjDH,EAAA,CAAAE,MAAA,2CACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,aAAwB;QAGVD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,sEAC7B;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,IAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,8CAChD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGNH,EAAA,CAAAC,cAAA,cAA6B;QACvBD,EAAA,CAAAE,MAAA,+CAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEjCH,EAAA,CAAAC,cAAA,cAA2B;QAEbD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACnDH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGrDH,EAAA,CAAAC,cAAA,cAAuB;QACXD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC1BH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAC5CH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAiC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGnEH,EAAA,CAAAC,cAAA,cAAuB;QACXD,EAAA,CAAAE,MAAA,aAAK;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC1BH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,kCAAgB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAChDH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAiC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGnEH,EAAA,CAAAC,cAAA,cAAuB;QACXD,EAAA,CAAAE,MAAA,oBAAY;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjCH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,+BAAuB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACvDH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAqC;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAM3EH,EAAA,CAAAI,UAAA,KAAAuD,qDAAA,kBAcM,KAAAC,qDAAA;QAgBR5D,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAAqC;QACED,EAAA,CAAA6D,UAAA,mBAAAC,wEAAA;UAAA,OAASJ,GAAA,CAAAzB,aAAA,EAAe;QAAA,EAAC;QAC5DjC,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnCH,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiE;QAAzCD,EAAA,CAAA6D,UAAA,mBAAAE,wEAAA;UAAA,OAASL,GAAA,CAAA3B,SAAA,EAAW;QAAA,EAAC;QAC3C/B,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QA3EGH,EAAA,CAAAO,SAAA,IAA6B;QAA7BP,EAAA,CAAAoB,kBAAA,KAAAsC,GAAA,CAAAxC,IAAA,CAAAI,UAAA,eAA6B;QAWVtB,EAAA,CAAAO,SAAA,IAAmB;QAAnBP,EAAA,CAAAa,iBAAA,CAAA6C,GAAA,CAAAxC,IAAA,CAAAI,UAAA,CAAmB;QAMnBtB,EAAA,CAAAO,SAAA,GAAiC;QAAjCP,EAAA,CAAAa,iBAAA,CAAA6C,GAAA,CAAAxB,iBAAA,GAAAE,SAAA,CAAiC;QAMjCpC,EAAA,CAAAO,SAAA,GAAiC;QAAjCP,EAAA,CAAAa,iBAAA,CAAA6C,GAAA,CAAAxB,iBAAA,GAAAG,SAAA,CAAiC;QAMjCrC,EAAA,CAAAO,SAAA,GAAqC;QAArCP,EAAA,CAAAa,iBAAA,CAAA6C,GAAA,CAAAxB,iBAAA,GAAAI,aAAA,CAAqC;QAMtCtC,EAAA,CAAAO,SAAA,EAAsC;QAAtCP,EAAA,CAAAQ,UAAA,SAAAkD,GAAA,CAAAxC,IAAA,CAAAC,eAAA,CAAA6C,MAAA,MAAsC;QAiB9BhE,EAAA,CAAAO,SAAA,EAAqC;QAArCP,EAAA,CAAAQ,UAAA,SAAAkD,GAAA,CAAAxC,IAAA,CAAAC,eAAA,CAAA6C,MAAA,KAAqC;;;mBD5C3EtE,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,eAAe,EAAAoE,EAAA,CAAAC,SAAA,EACfpE,aAAa,EAAAqE,EAAA,CAAAC,OAAA,EACbrE,YAAY,EAAAsE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}