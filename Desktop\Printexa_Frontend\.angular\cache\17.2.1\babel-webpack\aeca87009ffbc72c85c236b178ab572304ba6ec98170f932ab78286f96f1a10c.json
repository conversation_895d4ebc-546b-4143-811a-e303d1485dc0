{"ast": null, "code": "export class ClientModel {\n  constructor(client) {\n    this.id = client.id || this.getRandomID();\n    this.code = client.code || '';\n    this.syntax = client.syntax || '';\n    this.matFiscal = client.matFiscal || '';\n    this.email = client.email || '';\n    this.telephone = client.telephone || '';\n    this.devis = client.devis || [];\n  }\n}", "map": {"version": 3, "names": ["ClientModel", "constructor", "client", "id", "getRandomID", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "devis"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\Model\\Client.ts"], "sourcesContent": ["export interface Client {\r\n  id: string;\r\n  code: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n  devis?: any[]; // À remplacer par l'interface Devis si disponible\r\n}\r\n\r\nexport interface CreateClientSimpleDto {\r\n  id?: string;\r\n  code: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n}\r\n\r\nexport interface UpdateClientDto {\r\n  code?: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n}\r\n\r\nexport class ClientModel {\r\n  id: string;\r\n  code: string;\r\n  syntax?: string;\r\n  matFiscal?: string;\r\n  email?: string;\r\n  telephone?: string;\r\n  devis?: any[];\r\n\r\n  constructor(client: Partial<Client>) {\r\n    this.id = client.id || this.getRandomID();\r\n    this.code = client.code || '';\r\n    this.syntax = client.syntax || '';\r\n    this.matFiscal = client.matFiscal || '';\r\n    this.email = client.email || '';\r\n    this.telephone = client.telephone || '';\r\n    this.devis = client.devis || [];\r\n  }\r\n\r\n}"], "mappings": "AA2BA,OAAM,MAAOA,WAAW;EAStBC,YAAYC,MAAuB;IACjC,IAAI,CAACC,EAAE,GAAGD,MAAM,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;IACzC,IAAI,CAACC,IAAI,GAAGH,MAAM,CAACG,IAAI,IAAI,EAAE;IAC7B,IAAI,CAACC,MAAM,GAAGJ,MAAM,CAACI,MAAM,IAAI,EAAE;IACjC,IAAI,CAACC,SAAS,GAAGL,MAAM,CAACK,SAAS,IAAI,EAAE;IACvC,IAAI,CAACC,KAAK,GAAGN,MAAM,CAACM,KAAK,IAAI,EAAE;IAC/B,IAAI,CAACC,SAAS,GAAGP,MAAM,CAACO,SAAS,IAAI,EAAE;IACvC,IAAI,CAACC,KAAK,GAAGR,MAAM,CAACQ,KAAK,IAAI,EAAE;EACjC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}