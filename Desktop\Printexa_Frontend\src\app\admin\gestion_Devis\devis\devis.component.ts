import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule, FormControl } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatTableModule } from '@angular/material/table';
import { MatIconModule } from '@angular/material/icon';
import { MatCardModule } from '@angular/material/card';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatChipsModule } from '@angular/material/chips';
import { Subject, takeUntil, debounceTime, distinctUntilChanged } from 'rxjs';

import { DevisService } from '../../services/devis.service';
import { Devi<PERSON> } from '../../Model/Devis';

@Component({
  selector: 'app-devis',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatTableModule,
    MatIconModule,
    MatCardModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule,
    MatChipsModule
  ],
  templateUrl: './devis.component.html',
  styleUrl: './devis.component.scss'
})
export class DevisComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  // Form controls
  referenceControl = new FormControl('');

  // Data
  allDevis: Devis[] = [];
  filteredDevis: Devis[] = [];
  selectedDevis: Devis | null = null;

  // UI State
  isLoading = false;
  isSearching = false;

  // Table columns
  displayedColumns: string[] = [
    'reference',
    'dateCreation',
    'status',
    'periodeProd',
    'totalHT',
    'totalTTC',
    'clientId',
    'actions'
  ];

  constructor(
    private devisService: DevisService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadAllDevis();
    this.setupReferenceSearch();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private setupReferenceSearch(): void {
    this.referenceControl.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(reference => {
        if (reference && reference.trim()) {
          this.searchDevisByReference(reference.trim());
        } else {
          this.filteredDevis = [...this.allDevis];
        }
      });
  }

  private loadAllDevis(): void {
    this.isLoading = true;
    this.devisService.getAllDevis()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (devis) => {
          this.allDevis = devis;
          this.filteredDevis = [...devis];
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Erreur lors du chargement des devis:', error);
          this.showNotification('Erreur lors du chargement des devis', 'error');
          this.isLoading = false;
        }
      });
  }

  private searchDevisByReference(reference: string): void {
    this.isSearching = true;
    this.devisService.searchDevisByReference(reference)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (devis) => {
          this.filteredDevis = devis;
          this.isSearching = false;

          if (devis.length === 0) {
            this.showNotification(`Aucun devis trouvé avec la référence "${reference}"`, 'info');
          }
        },
        error: (error) => {
          console.error('Erreur lors de la recherche:', error);
          this.showNotification('Erreur lors de la recherche', 'error');
          this.isSearching = false;
        }
      });
  }

  onSelectDevis(devis: Devis): void {
    this.selectedDevis = devis;
    this.showNotification(`Devis ${devis.reference} sélectionné`, 'success');
  }

  onClearSearch(): void {
    this.referenceControl.setValue('');
    this.filteredDevis = [...this.allDevis];
    this.selectedDevis = null;
  }

  onRefresh(): void {
    this.loadAllDevis();
    this.onClearSearch();
  }

  private showNotification(message: string, type: 'success' | 'error' | 'info' = 'info'): void {
    this.snackBar.open(message, 'Fermer', {
      duration: 3000,
      panelClass: [`snackbar-${type}`]
    });
  }

  // Méthodes utilitaires pour l'affichage
  formatDate(date: Date | string): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toLocaleDateString('fr-FR');
  }

  formatCurrency(amount: number): string {
    if (amount == null) return '0,00 €';
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  }

  getStatusColor(status: string): string {
    switch (status?.toLowerCase()) {
      case 'brouillon': return 'warn';
      case 'envoyé': return 'primary';
      case 'accepté': return 'accent';
      case 'refusé': return 'warn';
      default: return 'primary';
    }
  }
}
