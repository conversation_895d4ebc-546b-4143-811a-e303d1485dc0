export interface LigneDevisAvecCodeDTO {
  id: number;
  codeProd: string;
  quantite: number;
  prixUTTC: number;
  prixTotTTC: number;
  remise: number;
  TVAProd: number;
  designationProduit: string;
  devisId: string;
  devisReference?: string;
}

export interface CreateLigneDevisAvecCodeDTO {
  codeProd: string;
  quantite: number;
  remise: number;
  devisId: string;
}

export interface UpdateLigneDevisDTO {
  quantite: number;
  remise: number;
  codeProd?: string;
}