{"ast": null, "code": "import { MAT_DIALOG_DATA, MatDialogT<PERSON>le, MatDialogContent, MatDialogActions } from '@angular/material/dialog';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/icon\";\nimport * as i4 from \"@angular/common\";\nfunction BulkDeleteConfirmationComponent_div_33_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19)(1, \"span\", 20);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const type_r4 = ctx.$implicit;\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(type_r4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r3.getProductTypesSummary()[type_r4], \" produit(s)\");\n  }\n}\nfunction BulkDeleteConfirmationComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"h5\");\n    i0.ɵɵtext(2, \"R\\u00E9partition par type :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 17);\n    i0.ɵɵtemplate(4, BulkDeleteConfirmationComponent_div_33_div_4_Template, 5, 2, \"div\", 18);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.getProductTypesKeys());\n  }\n}\nfunction BulkDeleteConfirmationComponent_div_34_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 29);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"currency\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const product_r6 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r6.type);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(product_r6.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(8, 3, product_r6.prixUnitaireTTC, \"EUR\", \"symbol\", \"1.2-2\"));\n  }\n}\nfunction BulkDeleteConfirmationComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"h5\");\n    i0.ɵɵtext(2, \"Produits \\u00E0 supprimer :\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 23);\n    i0.ɵɵtemplate(4, BulkDeleteConfirmationComponent_div_34_div_4_Template, 9, 8, \"div\", 24);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.data.selectedProducts);\n  }\n}\nfunction BulkDeleteConfirmationComponent_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"p\")(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Trop de produits s\\u00E9lectionn\\u00E9s pour les afficher individuellement. Consultez le r\\u00E9sum\\u00E9 ci-dessus. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class BulkDeleteConfirmationComponent {\n  constructor(dialogRef, data) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n  }\n  onNoClick() {\n    this.dialogRef.close(false);\n  }\n  confirmDelete() {\n    this.dialogRef.close(true);\n  }\n  // Calculer le total des prix des produits sélectionnés\n  getTotalValue() {\n    return this.data.selectedProducts.reduce((total, product) => {\n      return total + product.prixUnitaireTTC;\n    }, 0);\n  }\n  // Obtenir un résumé des types de produits\n  getProductTypesSummary() {\n    const summary = {};\n    this.data.selectedProducts.forEach(product => {\n      summary[product.type] = (summary[product.type] || 0) + 1;\n    });\n    return summary;\n  }\n  // Obtenir les clés du résumé des types\n  getProductTypesKeys() {\n    return Object.keys(this.getProductTypesSummary());\n  }\n  static #_ = this.ɵfac = function BulkDeleteConfirmationComponent_Factory(t) {\n    return new (t || BulkDeleteConfirmationComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: BulkDeleteConfirmationComponent,\n    selectors: [[\"app-bulk-delete-confirmation\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 45,\n    vars: 11,\n    consts: [[1, \"container\"], [\"mat-dialog-title\", \"\"], [1, \"warning-icon\"], [\"mat-dialog-content\", \"\"], [1, \"warning-message\"], [1, \"summary-section\"], [1, \"summary-stats\"], [1, \"stat-item\"], [1, \"stat-label\"], [1, \"stat-value\"], [\"class\", \"types-summary\", 4, \"ngIf\"], [\"class\", \"products-preview\", 4, \"ngIf\"], [\"class\", \"large-selection-message\", 4, \"ngIf\"], [\"mat-dialog-actions\", \"\", 1, \"mb-1\"], [\"mat-flat-button\", \"\", \"color\", \"warn\", 1, \"delete-btn\", 3, \"click\"], [\"mat-flat-button\", \"\", 1, \"cancel-btn\", 3, \"click\"], [1, \"types-summary\"], [1, \"type-list\"], [\"class\", \"type-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"type-item\"], [1, \"type-name\"], [1, \"type-count\"], [1, \"products-preview\"], [1, \"product-list\"], [\"class\", \"product-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"product-item\"], [1, \"product-info\"], [1, \"product-type\"], [1, \"product-description\"], [1, \"product-price\"], [1, \"large-selection-message\"]],\n    template: function BulkDeleteConfirmationComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h3\", 1)(2, \"mat-icon\", 2);\n        i0.ɵɵtext(3, \"warning\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(4, \" Confirmation de suppression en lot \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(5, \"div\", 3)(6, \"div\", 4)(7, \"p\")(8, \"strong\");\n        i0.ɵɵtext(9, \"Attention !\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(10, \" Vous \\u00EAtes sur le point de supprimer d\\u00E9finitivement \");\n        i0.ɵɵelementStart(11, \"strong\");\n        i0.ɵɵtext(12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(13, \". Cette action est irr\\u00E9versible. \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(14, \"div\", 5)(15, \"h4\");\n        i0.ɵɵtext(16, \"R\\u00E9sum\\u00E9 de la s\\u00E9lection :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(17, \"div\", 6)(18, \"div\", 7)(19, \"mat-icon\");\n        i0.ɵɵtext(20, \"inventory_2\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"span\", 8);\n        i0.ɵɵtext(22, \"Nombre de produits :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(23, \"span\", 9);\n        i0.ɵɵtext(24);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(25, \"div\", 7)(26, \"mat-icon\");\n        i0.ɵɵtext(27, \"euro\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"span\", 8);\n        i0.ɵɵtext(29, \"Valeur totale TTC :\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"span\", 9);\n        i0.ɵɵtext(31);\n        i0.ɵɵpipe(32, \"currency\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(33, BulkDeleteConfirmationComponent_div_33_Template, 5, 1, \"div\", 10);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(34, BulkDeleteConfirmationComponent_div_34_Template, 5, 1, \"div\", 11)(35, BulkDeleteConfirmationComponent_div_35_Template, 5, 0, \"div\", 12);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"div\", 13)(37, \"button\", 14);\n        i0.ɵɵlistener(\"click\", function BulkDeleteConfirmationComponent_Template_button_click_37_listener() {\n          return ctx.confirmDelete();\n        });\n        i0.ɵɵelementStart(38, \"mat-icon\");\n        i0.ɵɵtext(39, \"delete_forever\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(40, \" Supprimer tout \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(41, \"button\", 15);\n        i0.ɵɵlistener(\"click\", function BulkDeleteConfirmationComponent_Template_button_click_41_listener() {\n          return ctx.onNoClick();\n        });\n        i0.ɵɵelementStart(42, \"mat-icon\");\n        i0.ɵɵtext(43, \"cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(44, \" Annuler \");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate1(\"\", ctx.data.totalCount, \" produit(s)\");\n        i0.ɵɵadvance(12);\n        i0.ɵɵtextInterpolate(ctx.data.totalCount);\n        i0.ɵɵadvance(7);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind4(32, 6, ctx.getTotalValue(), \"EUR\", \"symbol\", \"1.2-2\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.getProductTypesKeys().length > 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.data.selectedProducts.length <= 5);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.data.selectedProducts.length > 5);\n      }\n    },\n    dependencies: [MatDialogTitle, MatDialogContent, MatDialogActions, MatButtonModule, i2.MatButton, MatIconModule, i3.MatIcon, CommonModule, i4.NgForOf, i4.NgIf, i4.CurrencyPipe],\n    styles: [\".container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 600px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  color: #d32f2f;\\n  font-weight: 600;\\n  margin-bottom: 20px;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   h3[mat-dialog-title][_ngcontent-%COMP%]   .warning-icon[_ngcontent-%COMP%] {\\n  margin-right: 12px;\\n  font-size: 28px;\\n  color: #ff9800;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 24px;\\n  color: #856404;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-weight: 500;\\n  line-height: 1.5;\\n}\\n.container[_ngcontent-%COMP%]   .warning-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 20px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 18px;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  margin-top: 16px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 8px 12px;\\n  background-color: white;\\n  border-radius: 6px;\\n  border: 1px solid #dee2e6;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n  flex: 1;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-value[_ngcontent-%COMP%] {\\n  color: #d32f2f;\\n  font-weight: 600;\\n  font-size: 16px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 8px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%]   .type-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  background-color: white;\\n  border: 1px solid #dee2e6;\\n  border-radius: 20px;\\n  padding: 6px 12px;\\n  min-width: 120px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%]   .type-item[_ngcontent-%COMP%]   .type-name[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-weight: 500;\\n  margin-right: 8px;\\n}\\n.container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%]   .type-item[_ngcontent-%COMP%]   .type-count[_ngcontent-%COMP%] {\\n  background-color: #e9ecef;\\n  color: #6c757d;\\n  font-size: 12px;\\n  font-weight: 600;\\n  padding: 2px 8px;\\n  border-radius: 10px;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #e9ecef;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  color: #495057;\\n  font-size: 16px;\\n  font-weight: 600;\\n  margin-bottom: 12px;\\n  margin-top: 0;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%] {\\n  max-height: 200px;\\n  overflow-y: auto;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%] {\\n  padding: 8px 12px;\\n  margin-bottom: 8px;\\n  background-color: white;\\n  border: 1px solid #dee2e6;\\n  border-radius: 6px;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-type[_ngcontent-%COMP%] {\\n  background-color: #007bff;\\n  color: white;\\n  padding: 2px 8px;\\n  border-radius: 12px;\\n  font-size: 12px;\\n  font-weight: 600;\\n  min-width: 80px;\\n  text-align: center;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-description[_ngcontent-%COMP%] {\\n  flex: 1;\\n  color: #495057;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .products-preview[_ngcontent-%COMP%]   .product-list[_ngcontent-%COMP%]   .product-item[_ngcontent-%COMP%]   .product-info[_ngcontent-%COMP%]   .product-price[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-weight: 600;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  border: 1px solid #bbdefb;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin-bottom: 20px;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  margin: 0;\\n  color: #1976d2;\\n  font-weight: 500;\\n}\\n.container[_ngcontent-%COMP%]   .large-selection-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 20px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-end;\\n  gap: 12px;\\n  padding-top: 20px;\\n  border-top: 1px solid #e9ecef;\\n  margin-top: 20px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  min-width: 140px;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n  font-size: 18px;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%] {\\n  background-color: #d32f2f;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #b71c1c;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.delete-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.3);\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%] {\\n  background-color: #6c757d;\\n  color: white;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268;\\n}\\n.container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button.cancel-btn[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 3px rgba(108, 117, 125, 0.3);\\n}\\n\\n@media (max-width: 768px) {\\n  .container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .summary-stats[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   .stat-label[_ngcontent-%COMP%] {\\n    flex: none;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  .container[_ngcontent-%COMP%]   .summary-section[_ngcontent-%COMP%]   .types-summary[_ngcontent-%COMP%]   .type-list[_ngcontent-%COMP%]   .type-item[_ngcontent-%COMP%] {\\n    min-width: auto;\\n    width: 100%;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .container[_ngcontent-%COMP%]   div[mat-dialog-actions][_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n    min-width: auto;\\n  }\\n}\\n.container[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_slideIn 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_slideIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(-20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "MatDialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatDialogActions", "MatButtonModule", "MatIconModule", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "type_r4", "ɵɵtextInterpolate1", "ctx_r3", "getProductTypesSummary", "ɵɵtemplate", "BulkDeleteConfirmationComponent_div_33_div_4_Template", "ɵɵproperty", "ctx_r0", "getProductTypesKeys", "product_r6", "type", "description", "ɵɵpipeBind4", "prixUnitaireTTC", "BulkDeleteConfirmationComponent_div_34_div_4_Template", "ctx_r1", "data", "selectedProducts", "BulkDeleteConfirmationComponent", "constructor", "dialogRef", "onNoClick", "close", "confirmDelete", "getTotalValue", "reduce", "total", "product", "summary", "for<PERSON>ach", "Object", "keys", "_", "ɵɵdirectiveInject", "i1", "MatDialogRef", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "BulkDeleteConfirmationComponent_Template", "rf", "ctx", "BulkDeleteConfirmationComponent_div_33_Template", "BulkDeleteConfirmationComponent_div_34_Template", "BulkDeleteConfirmationComponent_div_35_Template", "ɵɵlistener", "BulkDeleteConfirmationComponent_Template_button_click_37_listener", "BulkDeleteConfirmationComponent_Template_button_click_41_listener", "totalCount", "length", "i2", "MatButton", "i3", "MatIcon", "i4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "C<PERSON><PERSON>cyPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\bulk-delete-confirmation\\bulk-delete-confirmation.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\bulk-delete-confirmation\\bulk-delete-confirmation.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';\nimport { Component, Inject } from '@angular/core';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { CommonModule } from '@angular/common';\nimport { Produit } from '../../Model/Produit';\n\nexport interface BulkDeleteDialogData {\n  selectedProducts: Produit[];\n  totalCount: number;\n}\n\n@Component({\n  selector: 'app-bulk-delete-confirmation',\n  templateUrl: './bulk-delete-confirmation.component.html',\n  styleUrls: ['./bulk-delete-confirmation.component.scss'],\n  standalone: true,\n  imports: [\n    MatDialogTitle,\n    MatDialogContent,\n    MatDialogActions,\n    MatButtonModule,\n    MatIconModule,\n    MatDialogClose,\n    CommonModule,\n  ],\n})\nexport class BulkDeleteConfirmationComponent {\n  constructor(\n    public dialogRef: MatDialogRef<BulkDeleteConfirmationComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: BulkDeleteDialogData\n  ) {}\n\n  onNoClick(): void {\n    this.dialogRef.close(false);\n  }\n\n  confirmDelete(): void {\n    this.dialogRef.close(true);\n  }\n\n  // Calculer le total des prix des produits sélectionnés\n  getTotalValue(): number {\n    return this.data.selectedProducts.reduce((total, product) => {\n      return total + product.prixUnitaireTTC;\n    }, 0);\n  }\n\n  // Obtenir un résumé des types de produits\n  getProductTypesSummary(): { [key: string]: number } {\n    const summary: { [key: string]: number } = {};\n    this.data.selectedProducts.forEach(product => {\n      summary[product.type] = (summary[product.type] || 0) + 1;\n    });\n    return summary;\n  }\n\n  // Obtenir les clés du résumé des types\n  getProductTypesKeys(): string[] {\n    return Object.keys(this.getProductTypesSummary());\n  }\n}\n", "<div class=\"container\">\n  <h3 mat-dialog-title>\n    <mat-icon class=\"warning-icon\">warning</mat-icon>\n    Confirmation de suppression en lot\n  </h3>\n  \n  <div mat-dialog-content>\n    <div class=\"warning-message\">\n      <p>\n        <strong>Attention !</strong> Vous êtes sur le point de supprimer définitivement \n        <strong>{{data.totalCount}} produit(s)</strong>. Cette action est irréversible.\n      </p>\n    </div>\n\n    <div class=\"summary-section\">\n      <h4>Résumé de la sélection :</h4>\n      \n      <div class=\"summary-stats\">\n        <div class=\"stat-item\">\n          <mat-icon>inventory_2</mat-icon>\n          <span class=\"stat-label\">Nombre de produits :</span>\n          <span class=\"stat-value\">{{data.totalCount}}</span>\n        </div>\n        \n        <div class=\"stat-item\">\n          <mat-icon>euro</mat-icon>\n          <span class=\"stat-label\">Valeur totale TTC :</span>\n          <span class=\"stat-value\">{{getTotalValue() | currency:'EUR':'symbol':'1.2-2'}}</span>\n        </div>\n      </div>\n\n      <div class=\"types-summary\" *ngIf=\"getProductTypesKeys().length > 0\">\n        <h5>Répartition par type :</h5>\n        <div class=\"type-list\">\n          <div class=\"type-item\" *ngFor=\"let type of getProductTypesKeys()\">\n            <span class=\"type-name\">{{type}}</span>\n            <span class=\"type-count\">{{getProductTypesSummary()[type]}} produit(s)</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Affichage des premiers produits si la liste n'est pas trop longue -->\n    <div class=\"products-preview\" *ngIf=\"data.selectedProducts.length <= 5\">\n      <h5>Produits à supprimer :</h5>\n      <div class=\"product-list\">\n        <div class=\"product-item\" *ngFor=\"let product of data.selectedProducts\">\n          <div class=\"product-info\">\n            <span class=\"product-type\">{{product.type}}</span>\n            <span class=\"product-description\">{{product.description}}</span>\n            <span class=\"product-price\">{{product.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- Message pour les grandes sélections -->\n    <div class=\"large-selection-message\" *ngIf=\"data.selectedProducts.length > 5\">\n      <p>\n        <mat-icon>info</mat-icon>\n        Trop de produits sélectionnés pour les afficher individuellement. \n        Consultez le résumé ci-dessus.\n      </p>\n    </div>\n  </div>\n  \n  <div mat-dialog-actions class=\"mb-1\">\n    <button mat-flat-button color=\"warn\" (click)=\"confirmDelete()\" class=\"delete-btn\">\n      <mat-icon>delete_forever</mat-icon>\n      Supprimer tout\n    </button>\n    <button mat-flat-button (click)=\"onNoClick()\" class=\"cancel-btn\">\n      <mat-icon>cancel</mat-icon>\n      Annuler\n    </button>\n  </div>\n</div>\n"], "mappings": "AAAA,SAASA,eAAe,EAAgBC,cAAc,EAAEC,gBAAgB,EAAEC,gBAAgB,QAAwB,0BAA0B;AAE5I,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;IC8BpCC,EAAA,CAAAC,cAAA,cAAkE;IACxCD,EAAA,CAAAE,MAAA,GAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADrDH,EAAA,CAAAI,SAAA,GAAQ;IAARJ,EAAA,CAAAK,iBAAA,CAAAC,OAAA,CAAQ;IACPN,EAAA,CAAAI,SAAA,GAA6C;IAA7CJ,EAAA,CAAAO,kBAAA,KAAAC,MAAA,CAAAC,sBAAA,GAAAH,OAAA,iBAA6C;;;;;IAL5EN,EAAA,CAAAC,cAAA,cAAoE;IAC9DD,EAAA,CAAAE,MAAA,kCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,cAAuB;IACrBD,EAAA,CAAAU,UAAA,IAAAC,qDAAA,kBAGM;IACRX,EAAA,CAAAG,YAAA,EAAM;;;;IAJoCH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,UAAA,YAAAC,MAAA,CAAAC,mBAAA,GAAwB;;;;;IAYlEd,EAAA,CAAAC,cAAA,cAAwE;IAEzCD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChEH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA6D;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAFrEH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAU,UAAA,CAAAC,IAAA,CAAgB;IACThB,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAK,iBAAA,CAAAU,UAAA,CAAAE,WAAA,CAAuB;IAC7BjB,EAAA,CAAAI,SAAA,GAA6D;IAA7DJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkB,WAAA,OAAAH,UAAA,CAAAI,eAAA,4BAA6D;;;;;IAPjGnB,EAAA,CAAAC,cAAA,cAAwE;IAClED,EAAA,CAAAE,MAAA,kCAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,cAA0B;IACxBD,EAAA,CAAAU,UAAA,IAAAU,qDAAA,kBAMM;IACRpB,EAAA,CAAAG,YAAA,EAAM;;;;IAP0CH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,UAAA,YAAAS,MAAA,CAAAC,IAAA,CAAAC,gBAAA,CAAwB;;;;;IAW1EvB,EAAA,CAAAC,cAAA,cAA8E;IAEhED,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,6HAEF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;ADnCV,OAAM,MAAOqB,+BAA+B;EAC1CC,YACSC,SAAwD,EAC/BJ,IAA0B;IADnD,KAAAI,SAAS,GAATA,SAAS;IACgB,KAAAJ,IAAI,GAAJA,IAAI;EACnC;EAEHK,SAASA,CAAA;IACP,IAAI,CAACD,SAAS,CAACE,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAC,aAAaA,CAAA;IACX,IAAI,CAACH,SAAS,CAACE,KAAK,CAAC,IAAI,CAAC;EAC5B;EAEA;EACAE,aAAaA,CAAA;IACX,OAAO,IAAI,CAACR,IAAI,CAACC,gBAAgB,CAACQ,MAAM,CAAC,CAACC,KAAK,EAAEC,OAAO,KAAI;MAC1D,OAAOD,KAAK,GAAGC,OAAO,CAACd,eAAe;IACxC,CAAC,EAAE,CAAC,CAAC;EACP;EAEA;EACAV,sBAAsBA,CAAA;IACpB,MAAMyB,OAAO,GAA8B,EAAE;IAC7C,IAAI,CAACZ,IAAI,CAACC,gBAAgB,CAACY,OAAO,CAACF,OAAO,IAAG;MAC3CC,OAAO,CAACD,OAAO,CAACjB,IAAI,CAAC,GAAG,CAACkB,OAAO,CAACD,OAAO,CAACjB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;IAC1D,CAAC,CAAC;IACF,OAAOkB,OAAO;EAChB;EAEA;EACApB,mBAAmBA,CAAA;IACjB,OAAOsB,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC5B,sBAAsB,EAAE,CAAC;EACnD;EAAC,QAAA6B,CAAA,G;qBAjCUd,+BAA+B,EAAAxB,EAAA,CAAAuC,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAzC,EAAA,CAAAuC,iBAAA,CAGhC9C,eAAe;EAAA;EAAA,QAAAiD,EAAA,G;UAHdlB,+BAA+B;IAAAmB,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA7C,EAAA,CAAA8C,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QC3B5CpD,EAAA,CAAAC,cAAA,aAAuB;QAEYD,EAAA,CAAAE,MAAA,cAAO;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACjDH,EAAA,CAAAE,MAAA,2CACF;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAELH,EAAA,CAAAC,cAAA,aAAwB;QAGVD,EAAA,CAAAE,MAAA,kBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAACH,EAAA,CAAAE,MAAA,sEAC7B;QAAAF,EAAA,CAAAC,cAAA,cAAQ;QAAAD,EAAA,CAAAE,MAAA,IAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,8CACjD;QAAAF,EAAA,CAAAG,YAAA,EAAI;QAGNH,EAAA,CAAAC,cAAA,cAA6B;QACvBD,EAAA,CAAAE,MAAA,+CAAwB;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAEjCH,EAAA,CAAAC,cAAA,cAA2B;QAEbD,EAAA,CAAAE,MAAA,mBAAW;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAChCH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,4BAAoB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACpDH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAGrDH,EAAA,CAAAC,cAAA,cAAuB;QACXD,EAAA,CAAAE,MAAA,YAAI;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACzBH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,2BAAmB;QAAAF,EAAA,CAAAG,YAAA,EAAO;QACnDH,EAAA,CAAAC,cAAA,eAAyB;QAAAD,EAAA,CAAAE,MAAA,IAAqD;;QAAAF,EAAA,CAAAG,YAAA,EAAO;QAIzFH,EAAA,CAAAU,UAAA,KAAA4C,+CAAA,kBAQM;QACRtD,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAU,UAAA,KAAA6C,+CAAA,kBAWM,KAAAC,+CAAA;QAURxD,EAAA,CAAAG,YAAA,EAAM;QAENH,EAAA,CAAAC,cAAA,eAAqC;QACED,EAAA,CAAAyD,UAAA,mBAAAC,kEAAA;UAAA,OAASL,GAAA,CAAAxB,aAAA,EAAe;QAAA,EAAC;QAC5D7B,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,sBAAc;QAAAF,EAAA,CAAAG,YAAA,EAAW;QACnCH,EAAA,CAAAE,MAAA,wBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAC,cAAA,kBAAiE;QAAzCD,EAAA,CAAAyD,UAAA,mBAAAE,kEAAA;UAAA,OAASN,GAAA,CAAA1B,SAAA,EAAW;QAAA,EAAC;QAC3C3B,EAAA,CAAAC,cAAA,gBAAU;QAAAD,EAAA,CAAAE,MAAA,cAAM;QAAAF,EAAA,CAAAG,YAAA,EAAW;QAC3BH,EAAA,CAAAE,MAAA,iBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;;;QAhEGH,EAAA,CAAAI,SAAA,IAA8B;QAA9BJ,EAAA,CAAAO,kBAAA,KAAA8C,GAAA,CAAA/B,IAAA,CAAAsC,UAAA,gBAA8B;QAWX5D,EAAA,CAAAI,SAAA,IAAmB;QAAnBJ,EAAA,CAAAK,iBAAA,CAAAgD,GAAA,CAAA/B,IAAA,CAAAsC,UAAA,CAAmB;QAMnB5D,EAAA,CAAAI,SAAA,GAAqD;QAArDJ,EAAA,CAAAK,iBAAA,CAAAL,EAAA,CAAAkB,WAAA,QAAAmC,GAAA,CAAAvB,aAAA,8BAAqD;QAItD9B,EAAA,CAAAI,SAAA,GAAsC;QAAtCJ,EAAA,CAAAY,UAAA,SAAAyC,GAAA,CAAAvC,mBAAA,GAAA+C,MAAA,KAAsC;QAYrC7D,EAAA,CAAAI,SAAA,EAAuC;QAAvCJ,EAAA,CAAAY,UAAA,SAAAyC,GAAA,CAAA/B,IAAA,CAAAC,gBAAA,CAAAsC,MAAA,MAAuC;QAchC7D,EAAA,CAAAI,SAAA,EAAsC;QAAtCJ,EAAA,CAAAY,UAAA,SAAAyC,GAAA,CAAA/B,IAAA,CAAAC,gBAAA,CAAAsC,MAAA,KAAsC;;;mBDvC5EnE,cAAc,EACdC,gBAAgB,EAChBC,gBAAgB,EAChBC,eAAe,EAAAiE,EAAA,CAAAC,SAAA,EACfjE,aAAa,EAAAkE,EAAA,CAAAC,OAAA,EAEblE,YAAY,EAAAmE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,YAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}