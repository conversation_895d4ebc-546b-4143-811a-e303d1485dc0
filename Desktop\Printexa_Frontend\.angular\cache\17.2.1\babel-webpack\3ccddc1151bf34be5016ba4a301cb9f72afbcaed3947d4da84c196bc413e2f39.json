{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n      console.log('Clients récupérés du serveur:', clients?.length || 0);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      console.error('Erreur lors de la récupération des clients:', error);\n      // En cas d'erreur de connexion, utiliser des données de test\n      if (error.status === 0) {\n        console.log('Serveur non disponible, utilisation de données de test');\n        const testClients = [{\n          id: '1',\n          code: 'CLI001',\n          syntax: 'Client Test 1',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '12345678'\n        }, {\n          id: '2',\n          code: 'CLI002',\n          syntax: 'Client Test 2',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '87654321'\n        }];\n        this.dataChange.next(testClients);\n        return new Observable(observer => {\n          observer.next(testClients);\n          observer.complete();\n        });\n      }\n      // Pour les autres erreurs, mettre un tableau vide\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    // Validation des données d'entrée\n    if (!id || id.trim() === '') {\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\n    }\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      console.log('Recherche du client par code dans les données:', currentData.length, 'clients');\n      const clientByCode = currentData.find(c => c.code === id);\n      if (clientByCode) {\n        console.log('Client trouvé par code:', clientByCode);\n        if (clientByCode.id !== id) {\n          console.log('ID trouvé par code:', clientByCode.id);\n          actualId = clientByCode.id;\n        }\n      } else {\n        console.warn('Aucun client trouvé avec le code:', id);\n      }\n    }\n    // Vérifier que l'ID final est valide après résolution\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    console.log('ID final après résolution:', actualId, 'Est GUID:', finalIsGuid);\n    if (!finalIsGuid) {\n      const errorMsg = `ID invalide après résolution: \"${actualId}\" (original: \"${id}\"). ` + `L'ID doit être un GUID valide ou un code client existant.`;\n      console.error(errorMsg);\n      return throwError(() => new Error(errorMsg));\n    }\n    // Nettoyer et valider les données\n    const cleanedClient = {};\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\n    const clientData = {\n      ...client\n    };\n    if ('id' in clientData) {\n      delete clientData.id;\n    }\n    // Ajouter seulement les champs non vides et valides\n    if (clientData.code && clientData.code.trim()) {\n      cleanedClient.code = clientData.code.trim();\n      // Validation du code\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\n      }\n    }\n    if (clientData.syntax && clientData.syntax.trim()) {\n      cleanedClient.syntax = clientData.syntax.trim();\n      if (cleanedClient.syntax.length > 100) {\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\n      cleanedClient.matFiscal = clientData.matFiscal.trim();\n      // Validation supprimée - accepter tout format de matricule fiscal\n    }\n    if (clientData.email && clientData.email.trim()) {\n      cleanedClient.email = clientData.email.trim();\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (cleanedClient.email.length > 100) {\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.telephone && clientData.telephone.trim()) {\n      cleanedClient.telephone = clientData.telephone.trim();\n      // Validation du téléphone\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(cleanedClient.telephone)) {\n        return throwError(() => new Error('Format de téléphone invalide'));\n      }\n    }\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\n    }\n    // Validation stricte côté client pour éviter les erreurs serveur\n    const validationResult = this.validateUpdateData(cleanedClient);\n    if (!validationResult.isValid) {\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\n    }\n    console.log('=== MISE À JOUR CLIENT ===');\n    console.log('ID original:', id);\n    console.log('ID à utiliser:', actualId);\n    console.log('Type de l\\'ID:', typeof actualId);\n    console.log('Longueur de l\\'ID:', actualId.length);\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\n    console.log('Données originales:', client);\n    console.log('Données après suppression ID:', clientData);\n    console.log('Données nettoyées à envoyer:', cleanedClient);\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\n    console.log('Headers:', this.getHeaders());\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\n    console.log('========================');\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Si le serveur retourne NoContent (204), créer le client mis à jour localement\n      if (response.status === 204 || !response.body) {\n        const currentData = this.dataChange.value;\n        // Chercher par l'ID original ou l'ID actuel\n        const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\n        if (!existingClient) {\n          throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\n        }\n        // Créer le client mis à jour en combinant les données existantes et les nouvelles\n        const updatedClient = {\n          ...existingClient,\n          ...cleanedClient\n        };\n        return updatedClient;\n      }\n      // Si le serveur retourne le client mis à jour\n      const updatedClient = response.body;\n      return updatedClient;\n    }), tap(updatedClient => {\n      console.log('Client mis à jour avec succès:', updatedClient);\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n      // Mettre à jour le client courant si c'est le même\n      if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\n        this.currentClientSubject.next(updatedClient);\n        localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n      }\n    }), catchError(error => {\n      console.error('Erreur lors de la mise à jour:', error);\n      // Si le serveur n'est pas disponible, simuler la mise à jour localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la mise à jour...');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      // Si erreur de validation persistante après suppression des validations de format,\n      // proposer une mise à jour locale en dernier recours\n      const errorMessage = error.error?.message || error.error?.title || '';\n      if (error.status === 400 && errorMessage.includes('validation errors')) {\n        console.warn('Erreur de validation serveur persistante malgré la suppression des validations de format');\n        console.warn('Basculement en mode simulation locale...');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleUpdateError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    console.log('Tentative de suppression des clients:', ids);\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log('Suppression en masse réussie');\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const updatedData = currentData.filter(client => !ids.includes(client.id));\n      this.dataChange.next(updatedData);\n    }), catchError(error => {\n      console.error('Erreur lors de la suppression en masse:', error);\n      // Si le serveur n'est pas disponible, simuler la suppression localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la suppression...');\n        return this.simulateLocalDeletion(ids);\n      }\n      // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\n      if (error.status === 500 || error.status === 404) {\n        console.log('Tentative de suppression individuelle...');\n        return this.deleteClientsIndividually(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Méthode de fallback pour supprimer les clients individuellement\n  deleteClientsIndividually(ids) {\n    const deleteRequests = ids.map(id => this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error(`Erreur lors de la suppression du client ${id}:`, error);\n      return throwError(() => error);\n    })));\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\n    return new Observable(observer => {\n      let completedCount = 0;\n      let hasError = false;\n      const errors = [];\n      deleteRequests.forEach((request, index) => {\n        request.subscribe({\n          next: () => {\n            completedCount++;\n            if (completedCount === ids.length && !hasError) {\n              // Mettre à jour les données locales\n              const currentData = this.dataChange.value;\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\n              this.dataChange.next(updatedData);\n              observer.next({\n                deletedCount: completedCount\n              });\n              observer.complete();\n            }\n          },\n          error: error => {\n            hasError = true;\n            errors.push({\n              id: ids[index],\n              error\n            });\n            if (completedCount + errors.length === ids.length) {\n              observer.error({\n                message: 'Certains clients n\\'ont pas pu être supprimés',\n                errors,\n                deletedCount: completedCount\n              });\n            }\n          }\n        });\n      });\n    });\n  }\n  // Simuler la suppression locale quand le serveur n'est pas disponible\n  simulateLocalDeletion(ids) {\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\n    // Mettre à jour les données locales\n    const currentData = this.dataChange.value;\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\n    this.dataChange.next(updatedData);\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length,\n          message: 'Suppression simulée localement (serveur non disponible)'\n        });\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\n  simulateLocalUpdate(id, updateData) {\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\n    // Trouver et mettre à jour le client dans les données locales\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\n    }\n    // Créer le client mis à jour\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    // Mettre à jour les données locales\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    // Mettre à jour le client courant si c'est le même\n    if (this.currentClientSubject.value?.id === id) {\n      this.currentClientSubject.next(updatedClient);\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n    }\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Méthode de test pour diagnostiquer les problèmes d'ID\n  testClientId(id) {\n    console.log('=== TEST ID CLIENT ===');\n    console.log('ID fourni:', id);\n    console.log('Type:', typeof id);\n    console.log('Longueur:', id.length);\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\n    const currentData = this.dataChange.value;\n    const clientById = currentData.find(c => c.id === id);\n    const clientByCode = currentData.find(c => c.code === id);\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\n    if (clientByCode) {\n      console.log('Client par code:', clientByCode);\n    }\n    console.log('===================');\n  }\n  // Méthode de test pour valider une mise à jour sans l'envoyer\n  testUpdateValidation(id, updateData) {\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\n    console.log('ID:', id);\n    console.log('Données:', updateData);\n    const validation = this.validateUpdateData(updateData);\n    console.log('Validation réussie:', validation.isValid);\n    if (!validation.isValid) {\n      console.log('Erreurs:', validation.errors);\n    }\n    console.log('Données nettoyées qui seraient envoyées:');\n    const cleaned = {};\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\n    console.log('Cleaned data:', cleaned);\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\n    console.log('================================');\n  }\n  // Validation stricte pour la mise à jour\n  validateUpdateData(client) {\n    const errors = [];\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\n    // Validation des champs\n    if (client.code !== undefined) {\n      if (!client.code || client.code.trim() === '') {\n        errors.push('Code ne peut pas être vide');\n      } else if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Code doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\n      // Validation de format supprimée - accepter tout format\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email !== undefined && client.email !== null) {\n      if (client.email.trim() !== '') {\n        // Validation de format supprimée - garder seulement la limite de longueur\n        if (client.email.length > 100) {\n          errors.push('Email ne peut pas dépasser 100 caractères');\n        }\n      }\n    }\n    if (client.telephone !== undefined && client.telephone !== null) {\n      if (client.telephone.trim() !== '') {\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n        if (!phonePattern.test(client.telephone)) {\n          errors.push('Format de téléphone invalide');\n        }\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Méthode publique pour tester une mise à jour complète\n  debugUpdate(clientId, updateData) {\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\n    // Test 1: Validation de l'ID\n    this.testClientId(clientId);\n    // Test 2: Validation des données\n    this.testUpdateValidation(clientId, updateData);\n    // Test 3: Simulation de la requête\n    console.log('--- Simulation de la requête ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\n    let actualId = clientId;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === clientId);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Méthode: PUT');\n    console.log('Headers:', this.getHeaders());\n    const cleanedData = {};\n    if (updateData.code) cleanedData.code = updateData.code.trim();\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\n    console.log('================================');\n  }\n  // Méthode pour tester la requête HTTP brute\n  testRawHttpRequest(clientId, updateData) {\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\n    const url = `${this.baseUrl}${clientId}`;\n    const headers = this.getHeaders();\n    console.log('URL:', url);\n    console.log('Headers:', headers);\n    console.log('Body:', JSON.stringify(updateData, null, 2));\n    return this.http.put(url, updateData, {\n      headers: headers,\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('SUCCÈS - Response:', response);\n    }), catchError(error => {\n      console.error('ÉCHEC - Error détaillé:', error);\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      // Retourner l'erreur pour que l'appelant puisse la voir\n      return throwError(() => error);\n    }));\n  }\n  // Test avec des données minimales pour identifier le problème\n  testMinimalUpdate(clientId) {\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\n    // Test 1: Seulement le code\n    const minimalData1 = {\n      code: 'TEST001'\n    };\n    console.log('Test 1 - Seulement code:', minimalData1);\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(catchError(() => {\n      console.log('Test 1 échoué, essai test 2...');\n      // Test 2: Seulement la syntax\n      const minimalData2 = {\n        syntax: 'Test Client'\n      };\n      console.log('Test 2 - Seulement syntax:', minimalData2);\n      return this.testRawHttpRequest(clientId, minimalData2).pipe(catchError(() => {\n        console.log('Test 2 échoué, essai test 3...');\n        // Test 3: Objet vide\n        const minimalData3 = {};\n        console.log('Test 3 - Objet vide:', minimalData3);\n        return this.testRawHttpRequest(clientId, minimalData3);\n      }));\n    }));\n  }\n  // Méthode utilitaire pour tester la validation des données client\n  validateClientData(client) {\n    const errors = [];\n    if (client.code) {\n      if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax && client.syntax.length > 100) {\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.email.length > 100) {\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\n      }\n    }\n    if (client.telephone) {\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(client.telephone)) {\n        errors.push('Format de téléphone invalide');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('=== ERREUR DE MISE À JOUR ===');\n    console.error('Status:', error.status);\n    console.error('StatusText:', error.statusText);\n    console.error('URL:', error.url);\n    console.error('Error object:', error.error);\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\n    console.error('Full error:', error);\n    console.error('============================');\n    // Analyser la structure de l'erreur en détail\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    const traceId = error.error?.traceId;\n    const type = error.error?.type;\n    console.log('API Error:', apiError);\n    console.log('Validation Errors:', validationErrors);\n    console.log('Validation Errors type:', typeof validationErrors);\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\n    console.log('Trace ID:', traceId);\n    console.log('Error Type:', type);\n    // Essayer de capturer d'autres propriétés d'erreur\n    if (error.error) {\n      console.log('Toutes les propriétés de error.error:');\n      for (const key in error.error) {\n        console.log(`  ${key}:`, error.error[key]);\n      }\n    }\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "console", "log", "length", "error", "status", "testClients", "id", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "observer", "complete", "handleError", "getClientById", "client", "setItem", "stringify", "createClient", "Error", "trim", "validateEmail", "generateGuid", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "isGuid", "test", "isCode", "actualId", "clientByCode", "find", "c", "warn", "finalIsGuid", "errorMsg", "cleanedClient", "phonePattern", "Object", "keys", "validationResult", "validateUpdateData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "join", "put", "existingClient", "updatedClient", "index", "findIndex", "simulateLocalUpdate", "errorMessage", "message", "title", "includes", "handleUpdateError", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "updatedData", "filter", "simulateLocalDeletion", "deleteClientsIndividually", "deleteRequests", "completedCount", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "request", "subscribe", "deletedCount", "push", "setTimeout", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "re", "testClientId", "clientById", "testUpdateValidation", "validation", "cleaned", "toUpperCase", "toLowerCase", "undefined", "debugUpdate", "clientId", "cleanedData", "testRawHttpRequest", "url", "testMinimalUpdate", "minimalData1", "minimalData2", "minimalData3", "validateClientData", "statusText", "apiError", "validationErrors", "traceId", "type", "key", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n        console.log('Clients récupérés du serveur:', clients?.length || 0);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        console.error('Erreur lors de la récupération des clients:', error);\r\n\r\n        // En cas d'erreur de connexion, utiliser des données de test\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, utilisation de données de test');\r\n          const testClients: Client[] = [\r\n            {\r\n              id: '1',\r\n              code: 'CLI001',\r\n              syntax: 'Client Test 1',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '12345678'\r\n            },\r\n            {\r\n              id: '2',\r\n              code: 'CLI002',\r\n              syntax: 'Client Test 2',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '87654321'\r\n            }\r\n          ];\r\n          this.dataChange.next(testClients);\r\n          return new Observable<Client[]>(observer => {\r\n            observer.next(testClients);\r\n            observer.complete();\r\n          });\r\n        }\r\n\r\n        // Pour les autres erreurs, mettre un tableau vide\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!id || id.trim() === '') {\r\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\r\n    }\r\n\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\r\n\r\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      console.log('Recherche du client par code dans les données:', currentData.length, 'clients');\r\n      const clientByCode = currentData.find(c => c.code === id);\r\n      if (clientByCode) {\r\n        console.log('Client trouvé par code:', clientByCode);\r\n        if (clientByCode.id !== id) {\r\n          console.log('ID trouvé par code:', clientByCode.id);\r\n          actualId = clientByCode.id;\r\n        }\r\n      } else {\r\n        console.warn('Aucun client trouvé avec le code:', id);\r\n      }\r\n    }\r\n\r\n    // Vérifier que l'ID final est valide après résolution\r\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    console.log('ID final après résolution:', actualId, 'Est GUID:', finalIsGuid);\r\n\r\n    if (!finalIsGuid) {\r\n      const errorMsg = `ID invalide après résolution: \"${actualId}\" (original: \"${id}\"). ` +\r\n                      `L'ID doit être un GUID valide ou un code client existant.`;\r\n      console.error(errorMsg);\r\n      return throwError(() => new Error(errorMsg));\r\n    }\r\n\r\n    // Nettoyer et valider les données\r\n    const cleanedClient: UpdateClientDto = {};\r\n\r\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\r\n    const clientData = { ...client };\r\n    if ('id' in clientData) {\r\n      delete (clientData as any).id;\r\n    }\r\n\r\n    // Ajouter seulement les champs non vides et valides\r\n    if (clientData.code && clientData.code.trim()) {\r\n      cleanedClient.code = clientData.code.trim();\r\n      // Validation du code\r\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\r\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.syntax && clientData.syntax.trim()) {\r\n      cleanedClient.syntax = clientData.syntax.trim();\r\n      if (cleanedClient.syntax.length > 100) {\r\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\r\n      cleanedClient.matFiscal = clientData.matFiscal.trim();\r\n      // Validation supprimée - accepter tout format de matricule fiscal\r\n    }\r\n\r\n    if (clientData.email && clientData.email.trim()) {\r\n      cleanedClient.email = clientData.email.trim();\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (cleanedClient.email.length > 100) {\r\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.telephone && clientData.telephone.trim()) {\r\n      cleanedClient.telephone = clientData.telephone.trim();\r\n      // Validation du téléphone\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(cleanedClient.telephone)) {\r\n        return throwError(() => new Error('Format de téléphone invalide'));\r\n      }\r\n    }\r\n\r\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\r\n    }\r\n\r\n    // Validation stricte côté client pour éviter les erreurs serveur\r\n    const validationResult = this.validateUpdateData(cleanedClient);\r\n    if (!validationResult.isValid) {\r\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\r\n    }\r\n\r\n    console.log('=== MISE À JOUR CLIENT ===');\r\n    console.log('ID original:', id);\r\n    console.log('ID à utiliser:', actualId);\r\n    console.log('Type de l\\'ID:', typeof actualId);\r\n    console.log('Longueur de l\\'ID:', actualId.length);\r\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\r\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\r\n    console.log('Données originales:', client);\r\n    console.log('Données après suppression ID:', clientData);\r\n    console.log('Données nettoyées à envoyer:', cleanedClient);\r\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\r\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\r\n    console.log('Headers:', this.getHeaders());\r\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\r\n    console.log('========================');\r\n\r\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Si le serveur retourne NoContent (204), créer le client mis à jour localement\r\n        if (response.status === 204 || !response.body) {\r\n          const currentData = this.dataChange.value;\r\n          // Chercher par l'ID original ou l'ID actuel\r\n          const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\r\n          if (!existingClient) {\r\n            throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\r\n          }\r\n\r\n          // Créer le client mis à jour en combinant les données existantes et les nouvelles\r\n          const updatedClient: Client = {\r\n            ...existingClient,\r\n            ...cleanedClient\r\n          };\r\n          return updatedClient;\r\n        }\r\n\r\n        // Si le serveur retourne le client mis à jour\r\n        const updatedClient: Client = response.body;\r\n        return updatedClient;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        console.log('Client mis à jour avec succès:', updatedClient);\r\n\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n\r\n        // Mettre à jour le client courant si c'est le même\r\n        if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\r\n          this.currentClientSubject.next(updatedClient);\r\n          localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la mise à jour:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la mise à jour localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la mise à jour...');\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n\r\n        // Si erreur de validation persistante après suppression des validations de format,\r\n        // proposer une mise à jour locale en dernier recours\r\n        const errorMessage = error.error?.message || error.error?.title || '';\r\n        if (error.status === 400 && errorMessage.includes('validation errors')) {\r\n          console.warn('Erreur de validation serveur persistante malgré la suppression des validations de format');\r\n          console.warn('Basculement en mode simulation locale...');\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n\r\n        return this.handleUpdateError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    console.log('Tentative de suppression des clients:', ids);\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log('Suppression en masse réussie');\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n        this.dataChange.next(updatedData);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la suppression en masse:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la suppression localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la suppression...');\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n\r\n        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\r\n        if (error.status === 500 || error.status === 404) {\r\n          console.log('Tentative de suppression individuelle...');\r\n          return this.deleteClientsIndividually(ids);\r\n        }\r\n\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode de fallback pour supprimer les clients individuellement\r\n  private deleteClientsIndividually(ids: string[]): Observable<any> {\r\n    const deleteRequests = ids.map(id =>\r\n      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n        catchError(error => {\r\n          console.error(`Erreur lors de la suppression du client ${id}:`, error);\r\n          return throwError(() => error);\r\n        })\r\n      )\r\n    );\r\n\r\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\r\n    return new Observable(observer => {\r\n      let completedCount = 0;\r\n      let hasError = false;\r\n      const errors: any[] = [];\r\n\r\n      deleteRequests.forEach((request, index) => {\r\n        request.subscribe({\r\n          next: () => {\r\n            completedCount++;\r\n            if (completedCount === ids.length && !hasError) {\r\n              // Mettre à jour les données locales\r\n              const currentData = this.dataChange.value;\r\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n              this.dataChange.next(updatedData);\r\n              observer.next({ deletedCount: completedCount });\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            hasError = true;\r\n            errors.push({ id: ids[index], error });\r\n            if (completedCount + errors.length === ids.length) {\r\n              observer.error({\r\n                message: 'Certains clients n\\'ont pas pu être supprimés',\r\n                errors,\r\n                deletedCount: completedCount\r\n              });\r\n            }\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  // Simuler la suppression locale quand le serveur n'est pas disponible\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\r\n\r\n    // Mettre à jour les données locales\r\n    const currentData = this.dataChange.value;\r\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n    this.dataChange.next(updatedData);\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({\r\n          deletedCount: ids.length,\r\n          message: 'Suppression simulée localement (serveur non disponible)'\r\n        });\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\r\n\r\n    // Trouver et mettre à jour le client dans les données locales\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\r\n    }\r\n\r\n    // Créer le client mis à jour\r\n    const updatedClient: Client = {\r\n      ...currentData[index],\r\n      ...updateData\r\n    };\r\n\r\n    // Mettre à jour les données locales\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    // Mettre à jour le client courant si c'est le même\r\n    if (this.currentClientSubject.value?.id === id) {\r\n      this.currentClientSubject.next(updatedClient);\r\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n    }\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Méthode de test pour diagnostiquer les problèmes d'ID\r\n  public testClientId(id: string): void {\r\n    console.log('=== TEST ID CLIENT ===');\r\n    console.log('ID fourni:', id);\r\n    console.log('Type:', typeof id);\r\n    console.log('Longueur:', id.length);\r\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\r\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\r\n\r\n    const currentData = this.dataChange.value;\r\n    const clientById = currentData.find(c => c.id === id);\r\n    const clientByCode = currentData.find(c => c.code === id);\r\n\r\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\r\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\r\n\r\n    if (clientByCode) {\r\n      console.log('Client par code:', clientByCode);\r\n    }\r\n    console.log('===================');\r\n  }\r\n\r\n  // Méthode de test pour valider une mise à jour sans l'envoyer\r\n  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {\r\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\r\n    console.log('ID:', id);\r\n    console.log('Données:', updateData);\r\n\r\n    const validation = this.validateUpdateData(updateData);\r\n    console.log('Validation réussie:', validation.isValid);\r\n    if (!validation.isValid) {\r\n      console.log('Erreurs:', validation.errors);\r\n    }\r\n\r\n    console.log('Données nettoyées qui seraient envoyées:');\r\n    const cleaned: UpdateClientDto = {};\r\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\r\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Cleaned data:', cleaned);\r\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Validation stricte pour la mise à jour\r\n  private validateUpdateData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\r\n\r\n    // Validation des champs\r\n    if (client.code !== undefined) {\r\n      if (!client.code || client.code.trim() === '') {\r\n        errors.push('Code ne peut pas être vide');\r\n      } else if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Code doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\r\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\r\n      // Validation de format supprimée - accepter tout format\r\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email !== undefined && client.email !== null) {\r\n      if (client.email.trim() !== '') {\r\n        // Validation de format supprimée - garder seulement la limite de longueur\r\n        if (client.email.length > 100) {\r\n          errors.push('Email ne peut pas dépasser 100 caractères');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.telephone !== undefined && client.telephone !== null) {\r\n      if (client.telephone.trim() !== '') {\r\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n        if (!phonePattern.test(client.telephone)) {\r\n          errors.push('Format de téléphone invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Méthode publique pour tester une mise à jour complète\r\n  public debugUpdate(clientId: string, updateData: any): void {\r\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\r\n\r\n    // Test 1: Validation de l'ID\r\n    this.testClientId(clientId);\r\n\r\n    // Test 2: Validation des données\r\n    this.testUpdateValidation(clientId, updateData);\r\n\r\n    // Test 3: Simulation de la requête\r\n    console.log('--- Simulation de la requête ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\r\n\r\n    let actualId = clientId;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === clientId);\r\n      if (clientByCode) {\r\n        actualId = clientByCode.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\r\n    console.log('Méthode: PUT');\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const cleanedData: any = {};\r\n    if (updateData.code) cleanedData.code = updateData.code.trim();\r\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\r\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\r\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\r\n    console.log('================================');\r\n  }\r\n\r\n  // Méthode pour tester la requête HTTP brute\r\n  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {\r\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\r\n\r\n    const url = `${this.baseUrl}${clientId}`;\r\n    const headers = this.getHeaders();\r\n\r\n    console.log('URL:', url);\r\n    console.log('Headers:', headers);\r\n    console.log('Body:', JSON.stringify(updateData, null, 2));\r\n\r\n    return this.http.put(url, updateData, {\r\n      headers: headers,\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('SUCCÈS - Response:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('ÉCHEC - Error détaillé:', error);\r\n        console.error('Status:', error.status);\r\n        console.error('Error body:', error.error);\r\n\r\n        // Retourner l'erreur pour que l'appelant puisse la voir\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Test avec des données minimales pour identifier le problème\r\n  public testMinimalUpdate(clientId: string): Observable<any> {\r\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\r\n\r\n    // Test 1: Seulement le code\r\n    const minimalData1 = { code: 'TEST001' };\r\n    console.log('Test 1 - Seulement code:', minimalData1);\r\n\r\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(\r\n      catchError(() => {\r\n        console.log('Test 1 échoué, essai test 2...');\r\n\r\n        // Test 2: Seulement la syntax\r\n        const minimalData2 = { syntax: 'Test Client' };\r\n        console.log('Test 2 - Seulement syntax:', minimalData2);\r\n\r\n        return this.testRawHttpRequest(clientId, minimalData2).pipe(\r\n          catchError(() => {\r\n            console.log('Test 2 échoué, essai test 3...');\r\n\r\n            // Test 3: Objet vide\r\n            const minimalData3 = {};\r\n            console.log('Test 3 - Objet vide:', minimalData3);\r\n\r\n            return this.testRawHttpRequest(clientId, minimalData3);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode utilitaire pour tester la validation des données client\r\n  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (client.code) {\r\n      if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.length > 100) {\r\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.email.length > 100) {\r\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.telephone) {\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(client.telephone)) {\r\n        errors.push('Format de téléphone invalide');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n\r\n    console.error('=== ERREUR DE MISE À JOUR ===');\r\n    console.error('Status:', error.status);\r\n    console.error('StatusText:', error.statusText);\r\n    console.error('URL:', error.url);\r\n    console.error('Error object:', error.error);\r\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\r\n    console.error('Full error:', error);\r\n    console.error('============================');\r\n\r\n    // Analyser la structure de l'erreur en détail\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n    const traceId = error.error?.traceId;\r\n    const type = error.error?.type;\r\n\r\n    console.log('API Error:', apiError);\r\n    console.log('Validation Errors:', validationErrors);\r\n    console.log('Validation Errors type:', typeof validationErrors);\r\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\r\n    console.log('Trace ID:', traceId);\r\n    console.log('Error Type:', type);\r\n\r\n    // Essayer de capturer d'autres propriétés d'erreur\r\n    if (error.error) {\r\n      console.log('Toutes les propriétés de error.error:');\r\n      for (const key in error.error) {\r\n        console.log(`  ${key}:`, error.error[key]);\r\n      }\r\n    }\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;MACnCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,OAAO,EAAEI,MAAM,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC,EACF5B,UAAU,CAAC6B,KAAK,IAAG;MACjB,IAAI,CAACrB,YAAY,GAAG,KAAK;MACzBkB,OAAO,CAACG,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MAEnE;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMI,WAAW,GAAa,CAC5B;UACEC,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,EACD;UACEL,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,CACF;QACD,IAAI,CAAC9B,UAAU,CAACkB,IAAI,CAACM,WAAW,CAAC;QACjC,OAAO,IAAIlC,UAAU,CAAWyC,QAAQ,IAAG;UACzCA,QAAQ,CAACb,IAAI,CAACM,WAAW,CAAC;UAC1BO,QAAQ,CAACC,QAAQ,EAAE;QACrB,CAAC,CAAC;;MAGJ;MACA,IAAI,CAAChC,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACe,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAY,aAAaA,CAACT,EAAU;IACtB,OAAO,IAAI,CAAC5B,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC2C,MAAM,IAAG;MACX5B,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACjC,oBAAoB,CAACgB,IAAI,CAACiB,MAAM,CAAC;IACxC,CAAC,CAAC,EACF1C,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACN,KAAK,IAAI,CAAC,IAAI,CAACY,aAAa,CAACN,MAAM,CAACN,KAAK,CAAC,EAAE;MACrD,OAAOtC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMjC,UAAU,GAA0B;MACxCmB,EAAE,EAAEU,MAAM,CAACV,EAAE,IAAI,IAAI,CAACiB,YAAY,EAAE;MACpChB,IAAI,EAAES,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE;MACxBb,MAAM,EAAEQ,MAAM,CAACR,MAAM,EAAEa,IAAI,EAAE;MAC7BZ,SAAS,EAAEO,MAAM,CAACP,SAAS,EAAEY,IAAI,EAAE;MACnCX,KAAK,EAAEM,MAAM,CAACN,KAAK,EAAEW,IAAI,EAAE;MAC3BV,SAAS,EAAEK,MAAM,CAACL,SAAS,EAAEU,IAAI;KAClC;IAED,OAAO,IAAI,CAAC3C,IAAI,CAAC8C,IAAI,CAAS,IAAI,CAAC5C,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIP,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOO,SAAS;IAClB,CAAC,CAAC,EACFtD,GAAG,CAAEsD,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,IAAI,CAACjD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFrD,UAAU,CAAC,IAAI,CAACyD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAAC1B,EAAU,EAAEU,MAAuB;IAC9C;IACA,IAAI,CAACV,EAAE,IAAIA,EAAE,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3B,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,yCAAyC,CAAC,CAAC;;IAG/E,IAAI,CAACJ,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E;IACA,MAAMa,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAAC5B,EAAE,CAAC;IACzF,MAAM6B,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAAC5B,EAAE,CAAC;IAE1CN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgC,MAAM,EAAE,OAAO,EAAEE,MAAM,CAAC;IAEzD;IACA,IAAIC,QAAQ,GAAG9B,EAAE;IACjB,IAAI6B,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC9B,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE4B,WAAW,CAAC3B,MAAM,EAAE,SAAS,CAAC;MAC5F,MAAMmC,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;MACzD,IAAI+B,YAAY,EAAE;QAChBrC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoC,YAAY,CAAC;QACpD,IAAIA,YAAY,CAAC/B,EAAE,KAAKA,EAAE,EAAE;UAC1BN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoC,YAAY,CAAC/B,EAAE,CAAC;UACnD8B,QAAQ,GAAGC,YAAY,CAAC/B,EAAE;;OAE7B,MAAM;QACLN,OAAO,CAACwC,IAAI,CAAC,mCAAmC,EAAElC,EAAE,CAAC;;;IAIzD;IACA,MAAMmC,WAAW,GAAG,iEAAiE,CAACP,IAAI,CAACE,QAAQ,CAAC;IACpGpC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEmC,QAAQ,EAAE,WAAW,EAAEK,WAAW,CAAC;IAE7E,IAAI,CAACA,WAAW,EAAE;MAChB,MAAMC,QAAQ,GAAG,kCAAkCN,QAAQ,iBAAiB9B,EAAE,MAAM,GACpE,2DAA2D;MAC3EN,OAAO,CAACG,KAAK,CAACuC,QAAQ,CAAC;MACvB,OAAOtE,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACsB,QAAQ,CAAC,CAAC;;IAG9C;IACA,MAAMC,aAAa,GAAoB,EAAE;IAEzC;IACA,MAAMxD,UAAU,GAAG;MAAE,GAAG6B;IAAM,CAAE;IAChC,IAAI,IAAI,IAAI7B,UAAU,EAAE;MACtB,OAAQA,UAAkB,CAACmB,EAAE;;IAG/B;IACA,IAAInB,UAAU,CAACoB,IAAI,IAAIpB,UAAU,CAACoB,IAAI,CAACc,IAAI,EAAE,EAAE;MAC7CsB,aAAa,CAACpC,IAAI,GAAGpB,UAAU,CAACoB,IAAI,CAACc,IAAI,EAAE;MAC3C;MACA,IAAIsB,aAAa,CAACpC,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIyC,aAAa,CAACpC,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QACnE,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIjC,UAAU,CAACqB,MAAM,IAAIrB,UAAU,CAACqB,MAAM,CAACa,IAAI,EAAE,EAAE;MACjDsB,aAAa,CAACnC,MAAM,GAAGrB,UAAU,CAACqB,MAAM,CAACa,IAAI,EAAE;MAC/C,IAAIsB,aAAa,CAACnC,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;QACrC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIjC,UAAU,CAACsB,SAAS,IAAItB,UAAU,CAACsB,SAAS,CAACY,IAAI,EAAE,EAAE;MACvDsB,aAAa,CAAClC,SAAS,GAAGtB,UAAU,CAACsB,SAAS,CAACY,IAAI,EAAE;MACrD;;IAGF,IAAIlC,UAAU,CAACuB,KAAK,IAAIvB,UAAU,CAACuB,KAAK,CAACW,IAAI,EAAE,EAAE;MAC/CsB,aAAa,CAACjC,KAAK,GAAGvB,UAAU,CAACuB,KAAK,CAACW,IAAI,EAAE;MAC7C;MACA,IAAIsB,aAAa,CAACjC,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;QACpC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8CAA8C,CAAC,CAAC;;;IAItF,IAAIjC,UAAU,CAACwB,SAAS,IAAIxB,UAAU,CAACwB,SAAS,CAACU,IAAI,EAAE,EAAE;MACvDsB,aAAa,CAAChC,SAAS,GAAGxB,UAAU,CAACwB,SAAS,CAACU,IAAI,EAAE;MACrD;MACA,MAAMuB,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAACS,aAAa,CAAChC,SAAS,CAAC,EAAE;QAC/C,OAAOvC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8BAA8B,CAAC,CAAC;;;IAItE;IACA,IAAIyB,MAAM,CAACC,IAAI,CAACH,aAAa,CAAC,CAACzC,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2CAA2C,CAAC,CAAC;;IAGjF;IACA,MAAM2B,gBAAgB,GAAG,IAAI,CAACC,kBAAkB,CAACL,aAAa,CAAC;IAC/D,IAAI,CAACI,gBAAgB,CAACE,OAAO,EAAE;MAC7B,OAAO7E,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uBAAuB2B,gBAAgB,CAACG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;IAGjGnD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,EAAE,CAAC;IAC/BN,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmC,QAAQ,CAAC;IACvCpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOmC,QAAQ,CAAC;IAC9CpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmC,QAAQ,CAAClC,MAAM,CAAC;IAClDF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,iEAAiE,CAACiC,IAAI,CAACE,QAAQ,CAAC,CAAC;IAChHpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,CAACiC,IAAI,CAACE,QAAQ,CAAC,CAAC;IACjEpC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,MAAM,CAAC;IAC1ChB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEd,UAAU,CAAC;IACxDa,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE0C,aAAa,CAAC;IAC1D3C,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,IAAI0C,aAAa,GAAG,eAAe,GAAG,UAAU,CAAC;IACjG3C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAGwD,QAAQ,EAAE,CAAC;IAC1DpC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACT,UAAU,EAAE,CAAC;IAC1CQ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEX,IAAI,CAAC4B,SAAS,CAACyB,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACjE3C,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,OAAO,IAAI,CAACvB,IAAI,CAAC0E,GAAG,CAAC,GAAG,IAAI,CAACxE,OAAO,GAAGwD,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChE/C,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAACtB,MAAM,KAAK,GAAG,IAAI,CAACsB,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;QACzC;QACA,MAAMuB,cAAc,GAAGxB,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAK8B,QAAQ,IAAIG,CAAC,CAACjC,EAAE,KAAKA,EAAE,IAAIiC,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;QAC/F,IAAI,CAAC+C,cAAc,EAAE;UACnB,MAAM,IAAIjC,KAAK,CAAC,mDAAmDgB,QAAQ,eAAe9B,EAAE,GAAG,CAAC;;QAGlG;QACA,MAAMgD,aAAa,GAAW;UAC5B,GAAGD,cAAc;UACjB,GAAGV;SACJ;QACD,OAAOW,aAAa;;MAGtB;MACA,MAAMA,aAAa,GAAW5B,QAAQ,CAACE,IAAI;MAC3C,OAAO0B,aAAa;IACtB,CAAC,CAAC,EACFjF,GAAG,CAAEiF,aAAqB,IAAI;MAC5BtD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqD,aAAa,CAAC;MAE5D;MACA,MAAMzB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMyB,KAAK,GAAG1B,WAAW,CAAC2B,SAAS,CAACjB,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAK8B,QAAQ,IAAIG,CAAC,CAACjC,EAAE,KAAKA,EAAE,IAAIiC,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;MAC3F,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB1B,WAAW,CAAC0B,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAACzE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;;MAGxC;MACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAK8B,QAAQ,IAAI,IAAI,CAACrD,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;QAClG,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAACuD,aAAa,CAAC;QAC7ClE,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACoC,aAAa,CAAC,CAAC;;IAExE,CAAC,CAAC,EACFhF,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAEtD;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACwD,mBAAmB,CAACrB,QAAQ,EAAEO,aAAa,CAAC;;MAG1D;MACA;MACA,MAAMe,YAAY,GAAGvD,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAI,EAAE;MACrE,IAAIzD,KAAK,CAACC,MAAM,KAAK,GAAG,IAAIsD,YAAY,CAACG,QAAQ,CAAC,mBAAmB,CAAC,EAAE;QACtE7D,OAAO,CAACwC,IAAI,CAAC,0FAA0F,CAAC;QACxGxC,OAAO,CAACwC,IAAI,CAAC,0CAA0C,CAAC;QACxD,OAAO,IAAI,CAACiB,mBAAmB,CAACrB,QAAQ,EAAEO,aAAa,CAAC;;MAG1D,OAAO,IAAI,CAACmB,iBAAiB,CAAC3D,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA4D,YAAYA,CAACzD,EAAU;IACrB,OAAO,IAAI,CAAC5B,IAAI,CAACsF,MAAM,CAAO,GAAG,IAAI,CAACpF,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAAC4F,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF3F,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAoD,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACjE,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrFpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEkE,GAAG,CAAC;IAEzD,OAAO,IAAI,CAACzF,IAAI,CAACsF,MAAM,CAAC,GAAG,IAAI,CAACpF,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BoC,IAAI,EAAEuC;KACP,CAAC,CAACtE,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP2B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;MACA,MAAM4B,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMsC,WAAW,GAAGvC,WAAW,CAACwC,MAAM,CAACrD,MAAM,IAAI,CAACmD,GAAG,CAACN,QAAQ,CAAC7C,MAAM,CAACV,EAAE,CAAC,CAAC;MAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACqE,WAAW,CAAC;IACnC,CAAC,CAAC,EACF9F,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAE/D;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACqE,qBAAqB,CAACH,GAAG,CAAC;;MAGxC;MACA,IAAIhE,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QAChDJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI,CAACsE,yBAAyB,CAACJ,GAAG,CAAC;;MAG5C,OAAO,IAAI,CAACrD,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQoE,yBAAyBA,CAACJ,GAAa;IAC7C,MAAMK,cAAc,GAAGL,GAAG,CAAC5F,GAAG,CAAC+B,EAAE,IAC/B,IAAI,CAAC5B,IAAI,CAACsF,MAAM,CAAC,GAAG,IAAI,CAACpF,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3EvB,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,2CAA2CG,EAAE,GAAG,EAAEH,KAAK,CAAC;MACtE,OAAO/B,UAAU,CAAC,MAAM+B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACF;IAED;IACA,OAAO,IAAIhC,UAAU,CAACyC,QAAQ,IAAG;MAC/B,IAAI6D,cAAc,GAAG,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,MAAMxB,MAAM,GAAU,EAAE;MAExBsB,cAAc,CAACG,OAAO,CAAC,CAACC,OAAO,EAAErB,KAAK,KAAI;QACxCqB,OAAO,CAACC,SAAS,CAAC;UAChB9E,IAAI,EAAEA,CAAA,KAAK;YACT0E,cAAc,EAAE;YAChB,IAAIA,cAAc,KAAKN,GAAG,CAACjE,MAAM,IAAI,CAACwE,QAAQ,EAAE;cAC9C;cACA,MAAM7C,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;cACzC,MAAMsC,WAAW,GAAGvC,WAAW,CAACwC,MAAM,CAACrD,MAAM,IAAI,CAACmD,GAAG,CAACN,QAAQ,CAAC7C,MAAM,CAACV,EAAE,CAAC,CAAC;cAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACqE,WAAW,CAAC;cACjCxD,QAAQ,CAACb,IAAI,CAAC;gBAAE+E,YAAY,EAAEL;cAAc,CAAE,CAAC;cAC/C7D,QAAQ,CAACC,QAAQ,EAAE;;UAEvB,CAAC;UACDV,KAAK,EAAGA,KAAK,IAAI;YACfuE,QAAQ,GAAG,IAAI;YACfxB,MAAM,CAAC6B,IAAI,CAAC;cAAEzE,EAAE,EAAE6D,GAAG,CAACZ,KAAK,CAAC;cAAEpD;YAAK,CAAE,CAAC;YACtC,IAAIsE,cAAc,GAAGvB,MAAM,CAAChD,MAAM,KAAKiE,GAAG,CAACjE,MAAM,EAAE;cACjDU,QAAQ,CAACT,KAAK,CAAC;gBACbwD,OAAO,EAAE,+CAA+C;gBACxDT,MAAM;gBACN4B,YAAY,EAAEL;eACf,CAAC;;UAEN;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQH,qBAAqBA,CAACH,GAAa;IACzCnE,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEkE,GAAG,CAAC;IAErE;IACA,MAAMtC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMsC,WAAW,GAAGvC,WAAW,CAACwC,MAAM,CAACrD,MAAM,IAAI,CAACmD,GAAG,CAACN,QAAQ,CAAC7C,MAAM,CAACV,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACqE,WAAW,CAAC;IAEjC;IACA,OAAO,IAAIjG,UAAU,CAACyC,QAAQ,IAAG;MAC/BoE,UAAU,CAAC,MAAK;QACdpE,QAAQ,CAACb,IAAI,CAAC;UACZ+E,YAAY,EAAEX,GAAG,CAACjE,MAAM;UACxByD,OAAO,EAAE;SACV,CAAC;QACF/C,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACQ4C,mBAAmBA,CAACnD,EAAU,EAAE2E,UAA2B;IACjEjF,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,EAAE,EAAE2E,UAAU,CAAC;IAE9E;IACA,MAAMpD,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMyB,KAAK,GAAG1B,WAAW,CAAC2B,SAAS,CAACxC,MAAM,IAAIA,MAAM,CAACV,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOnF,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E;IACA,MAAMkC,aAAa,GAAW;MAC5B,GAAGzB,WAAW,CAAC0B,KAAK,CAAC;MACrB,GAAG0B;KACJ;IAED;IACApD,WAAW,CAAC0B,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAACzE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;IAEtC;IACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;MAC9C,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAACuD,aAAa,CAAC;MAC7ClE,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACoC,aAAa,CAAC,CAAC;;IAGtE;IACA,OAAO,IAAInF,UAAU,CAASyC,QAAQ,IAAG;MACvCoE,UAAU,CAAC,MAAK;QACdpE,QAAQ,CAACb,IAAI,CAACuD,aAAa,CAAC;QAC5B1C,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACAoD,kBAAkBA,CAAA;IAChB7E,YAAY,CAAC8F,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACnG,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIoF,IAAIA,CAAA;IACN,OAAO,IAAI,CAACtG,UAAU,CAACiD,KAAK;EAC9B;EAEAsD,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQ9D,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAAC+D,OAAO,CAAC,OAAO,EAAE,UAAS/C,CAAC;MACvE,MAAMgD,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGnD,CAAC,KAAK,GAAG,GAAGgD,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQrE,aAAaA,CAACZ,KAAa;IACjC,MAAMkF,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAAC1D,IAAI,CAACxB,KAAK,CAAC;EACvB;EAEA;EACOmF,YAAYA,CAACvF,EAAU;IAC5BN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,EAAE,CAAC;IAC7BN,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAOK,EAAE,CAAC;IAC/BN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,EAAE,CAACJ,MAAM,CAAC;IACnCF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,iEAAiE,CAACiC,IAAI,CAAC5B,EAAE,CAAC,CAAC;IACpGN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAACiC,IAAI,CAAC5B,EAAE,CAAC,CAAC;IAErD,MAAMuB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMgE,UAAU,GAAGjE,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC;IACrD,MAAM+B,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;IAEzDN,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE6F,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;IAChE9F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoC,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IAEpE,IAAIA,YAAY,EAAE;MAChBrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEoC,YAAY,CAAC;;IAE/CrC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA;EACO8F,oBAAoBA,CAACzF,EAAU,EAAE2E,UAA2B;IACjEjF,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEK,EAAE,CAAC;IACtBN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgF,UAAU,CAAC;IAEnC,MAAMe,UAAU,GAAG,IAAI,CAAChD,kBAAkB,CAACiC,UAAU,CAAC;IACtDjF,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE+F,UAAU,CAAC/C,OAAO,CAAC;IACtD,IAAI,CAAC+C,UAAU,CAAC/C,OAAO,EAAE;MACvBjD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE+F,UAAU,CAAC9C,MAAM,CAAC;;IAG5ClD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,MAAMgG,OAAO,GAAoB,EAAE;IACnC,IAAIhB,UAAU,CAAC1E,IAAI,IAAI0E,UAAU,CAAC1E,IAAI,CAACc,IAAI,EAAE,EAAE4E,OAAO,CAAC1F,IAAI,GAAG0E,UAAU,CAAC1E,IAAI,CAACc,IAAI,EAAE;IACpF,IAAI4D,UAAU,CAACzE,MAAM,IAAIyE,UAAU,CAACzE,MAAM,CAACa,IAAI,EAAE,EAAE4E,OAAO,CAACzF,MAAM,GAAGyE,UAAU,CAACzE,MAAM,CAACa,IAAI,EAAE;IAC5F,IAAI4D,UAAU,CAACxE,SAAS,IAAIwE,UAAU,CAACxE,SAAS,CAACY,IAAI,EAAE,EAAE4E,OAAO,CAACxF,SAAS,GAAGwE,UAAU,CAACxE,SAAS,CAACY,IAAI,EAAE,CAAC6E,WAAW,EAAE;IACtH,IAAIjB,UAAU,CAACvE,KAAK,IAAIuE,UAAU,CAACvE,KAAK,CAACW,IAAI,EAAE,EAAE4E,OAAO,CAACvF,KAAK,GAAGuE,UAAU,CAACvE,KAAK,CAACW,IAAI,EAAE,CAAC8E,WAAW,EAAE;IACtG,IAAIlB,UAAU,CAACtE,SAAS,IAAIsE,UAAU,CAACtE,SAAS,CAACU,IAAI,EAAE,EAAE4E,OAAO,CAACtF,SAAS,GAAGsE,UAAU,CAACtE,SAAS,CAACU,IAAI,EAAE;IAExGrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEgG,OAAO,CAAC;IACrCjG,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAG0B,EAAE,EAAE,CAAC;IAC/DN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQ+C,kBAAkBA,CAAChC,MAAuB;IAChD,MAAMkC,MAAM,GAAa,EAAE;IAE3B;IAEA;IACA,IAAIlC,MAAM,CAACT,IAAI,KAAK6F,SAAS,EAAE;MAC7B,IAAI,CAACpF,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7C6B,MAAM,CAAC6B,IAAI,CAAC,4BAA4B,CAAC;OAC1C,MAAM,IAAI/D,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIc,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QAC5DgD,MAAM,CAAC6B,IAAI,CAAC,6CAA6C,CAAC;;;IAI9D,IAAI/D,MAAM,CAACR,MAAM,KAAK4F,SAAS,IAAIpF,MAAM,CAACR,MAAM,KAAK,IAAI,IAAIQ,MAAM,CAACR,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;MACvFgD,MAAM,CAAC6B,IAAI,CAAC,oDAAoD,CAAC;;IAGnE,IAAI/D,MAAM,CAACP,SAAS,KAAK2F,SAAS,IAAIpF,MAAM,CAACP,SAAS,KAAK,IAAI,EAAE;MAC/D;MACA,IAAIO,MAAM,CAACP,SAAS,CAACY,IAAI,EAAE,KAAK,EAAE,IAAIL,MAAM,CAACP,SAAS,CAACP,MAAM,GAAG,EAAE,EAAE;QAClEgD,MAAM,CAAC6B,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAI/D,MAAM,CAACN,KAAK,KAAK0F,SAAS,IAAIpF,MAAM,CAACN,KAAK,KAAK,IAAI,EAAE;MACvD,IAAIM,MAAM,CAACN,KAAK,CAACW,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B;QACA,IAAIL,MAAM,CAACN,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;UAC7BgD,MAAM,CAAC6B,IAAI,CAAC,2CAA2C,CAAC;;;;IAK9D,IAAI/D,MAAM,CAACL,SAAS,KAAKyF,SAAS,IAAIpF,MAAM,CAACL,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIK,MAAM,CAACL,SAAS,CAACU,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAMuB,YAAY,GAAG,6BAA6B;QAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAAClB,MAAM,CAACL,SAAS,CAAC,EAAE;UACxCuC,MAAM,CAAC6B,IAAI,CAAC,8BAA8B,CAAC;;;;IAKjD,OAAO;MACL9B,OAAO,EAAEC,MAAM,CAAChD,MAAM,KAAK,CAAC;MAC5BgD;KACD;EACH;EAEA;EACOmD,WAAWA,CAACC,QAAgB,EAAErB,UAAe;IAClDjF,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAAC4F,YAAY,CAACS,QAAQ,CAAC;IAE3B;IACA,IAAI,CAACP,oBAAoB,CAACO,QAAQ,EAAErB,UAAU,CAAC;IAE/C;IACAjF,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,MAAMgC,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAACoE,QAAQ,CAAC;IAC/F,MAAMnE,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAACoE,QAAQ,CAAC;IAEhD,IAAIlE,QAAQ,GAAGkE,QAAQ;IACvB,IAAInE,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMO,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAK+F,QAAQ,CAAC;MAC/D,IAAIjE,YAAY,EAAE;QAChBD,QAAQ,GAAGC,YAAY,CAAC/B,EAAE;QAC1BN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmC,QAAQ,CAAC;;;IAIvCpC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAGwD,QAAQ,EAAE,CAAC;IACxDpC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACT,UAAU,EAAE,CAAC;IAE1C,MAAM+G,WAAW,GAAQ,EAAE;IAC3B,IAAItB,UAAU,CAAC1E,IAAI,EAAEgG,WAAW,CAAChG,IAAI,GAAG0E,UAAU,CAAC1E,IAAI,CAACc,IAAI,EAAE;IAC9D,IAAI4D,UAAU,CAACzE,MAAM,EAAE+F,WAAW,CAAC/F,MAAM,GAAGyE,UAAU,CAACzE,MAAM,CAACa,IAAI,EAAE;IACpE,IAAI4D,UAAU,CAACxE,SAAS,EAAE8F,WAAW,CAAC9F,SAAS,GAAGwE,UAAU,CAACxE,SAAS,CAACY,IAAI,EAAE,CAAC,CAAC;IAC/E,IAAI4D,UAAU,CAACvE,KAAK,EAAE6F,WAAW,CAAC7F,KAAK,GAAGuE,UAAU,CAACvE,KAAK,CAACW,IAAI,EAAE,CAAC,CAAC;IACnE,IAAI4D,UAAU,CAACtE,SAAS,EAAE4F,WAAW,CAAC5F,SAAS,GAAGsE,UAAU,CAACtE,SAAS,CAACU,IAAI,EAAE;IAE7ErB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEX,IAAI,CAAC4B,SAAS,CAACqF,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChEvG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACOuG,kBAAkBA,CAACF,QAAgB,EAAErB,UAAe;IACzDjF,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,MAAMwG,GAAG,GAAG,GAAG,IAAI,CAAC7H,OAAO,GAAG0H,QAAQ,EAAE;IACxC,MAAM1G,OAAO,GAAG,IAAI,CAACJ,UAAU,EAAE;IAEjCQ,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEwG,GAAG,CAAC;IACxBzG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEL,OAAO,CAAC;IAChCI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEX,IAAI,CAAC4B,SAAS,CAAC+D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO,IAAI,CAACvG,IAAI,CAAC0E,GAAG,CAACqD,GAAG,EAAExB,UAAU,EAAE;MACpCrF,OAAO,EAAEA,OAAO;MAChB6B,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLxB,GAAG,CAACqD,QAAQ,IAAG;MACb1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyB,QAAQ,CAAC;IAC7C,CAAC,CAAC,EACFpD,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CH,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACC,MAAM,CAAC;MACtCJ,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MAEzC;MACA,OAAO/B,UAAU,CAAC,MAAM+B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACOuG,iBAAiBA,CAACJ,QAAgB;IACvCtG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAM0G,YAAY,GAAG;MAAEpG,IAAI,EAAE;IAAS,CAAE;IACxCP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE0G,YAAY,CAAC;IAErD,OAAO,IAAI,CAACH,kBAAkB,CAACF,QAAQ,EAAEK,YAAY,CAAC,CAAC9G,IAAI,CACzDvB,UAAU,CAAC,MAAK;MACd0B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA,MAAM2G,YAAY,GAAG;QAAEpG,MAAM,EAAE;MAAa,CAAE;MAC9CR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE2G,YAAY,CAAC;MAEvD,OAAO,IAAI,CAACJ,kBAAkB,CAACF,QAAQ,EAAEM,YAAY,CAAC,CAAC/G,IAAI,CACzDvB,UAAU,CAAC,MAAK;QACd0B,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C;QACA,MAAM4G,YAAY,GAAG,EAAE;QACvB7G,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE4G,YAAY,CAAC;QAEjD,OAAO,IAAI,CAACL,kBAAkB,CAACF,QAAQ,EAAEO,YAAY,CAAC;MACxD,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACOC,kBAAkBA,CAAC9F,MAAuB;IAC/C,MAAMkC,MAAM,GAAa,EAAE;IAE3B,IAAIlC,MAAM,CAACT,IAAI,EAAE;MACf,IAAIS,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIc,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QACrDgD,MAAM,CAAC6B,IAAI,CAAC,uDAAuD,CAAC;;;IAIxE,IAAI/D,MAAM,CAACR,MAAM,IAAIQ,MAAM,CAACR,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;MAC/CgD,MAAM,CAAC6B,IAAI,CAAC,uDAAuD,CAAC;;IAGtE,IAAI/D,MAAM,CAACP,SAAS,EAAE;MACpB;MACA,IAAIO,MAAM,CAACP,SAAS,CAACP,MAAM,GAAG,EAAE,EAAE;QAChCgD,MAAM,CAAC6B,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAI/D,MAAM,CAACN,KAAK,EAAE;MAChB;MACA,IAAIM,MAAM,CAACN,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;QAC7BgD,MAAM,CAAC6B,IAAI,CAAC,8CAA8C,CAAC;;;IAI/D,IAAI/D,MAAM,CAACL,SAAS,EAAE;MACpB,MAAMiC,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAAClB,MAAM,CAACL,SAAS,CAAC,EAAE;QACxCuC,MAAM,CAAC6B,IAAI,CAAC,8BAA8B,CAAC;;;IAI/C,OAAO;MACL9B,OAAO,EAAEC,MAAM,CAAChD,MAAM,KAAK,CAAC;MAC5BgD;KACD;EACH;EAEA;EACQY,iBAAiBA,CAAC3D,KAAwB;IAChD,IAAIuD,YAAY,GAAG,yCAAyC;IAE5D1D,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;IAC9CH,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACC,MAAM,CAAC;IACtCJ,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC4G,UAAU,CAAC;IAC9C/G,OAAO,CAACG,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACsG,GAAG,CAAC;IAChCzG,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CH,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,GAAG0C,MAAM,CAACC,IAAI,CAAC3C,KAAK,CAACA,KAAK,CAAC,GAAG,iBAAiB,CAAC;IACxFH,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnCH,OAAO,CAACG,KAAK,CAAC,8BAA8B,CAAC;IAE7C;IACA,MAAM6G,QAAQ,GAAG7G,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAIzD,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAM8G,gBAAgB,GAAG9G,KAAK,CAACA,KAAK,EAAE+C,MAAM;IAC5C,MAAMgE,OAAO,GAAG/G,KAAK,CAACA,KAAK,EAAE+G,OAAO;IACpC,MAAMC,IAAI,GAAGhH,KAAK,CAACA,KAAK,EAAEgH,IAAI;IAE9BnH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+G,QAAQ,CAAC;IACnChH,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgH,gBAAgB,CAAC;IACnDjH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOgH,gBAAgB,CAAC;IAC/DjH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgH,gBAAgB,GAAGpE,MAAM,CAACC,IAAI,CAACmE,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;IACjHjH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiH,OAAO,CAAC;IACjClH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkH,IAAI,CAAC;IAEhC;IACA,IAAIhH,KAAK,CAACA,KAAK,EAAE;MACfH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,KAAK,MAAMmH,GAAG,IAAIjH,KAAK,CAACA,KAAK,EAAE;QAC7BH,OAAO,CAACC,GAAG,CAAC,KAAKmH,GAAG,GAAG,EAAEjH,KAAK,CAACA,KAAK,CAACiH,GAAG,CAAC,CAAC;;;IAI9C,IAAIjH,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtBsD,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAIuD,gBAAgB,EAAE;QACpB,MAAMI,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOJ,gBAAgB,KAAK,QAAQ,EAAE;UACxCpE,MAAM,CAACC,IAAI,CAACmE,gBAAgB,CAAC,CAACtC,OAAO,CAAC2C,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGN,gBAAgB,CAACK,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAAC5C,OAAO,CAAC+C,GAAG,IAAIL,YAAY,CAACtC,IAAI,CAAC,GAAGuC,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAACtC,IAAI,CAAC,GAAGuC,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAACnH,MAAM,GAAG,CAAC,EAAE;UAC3BwD,YAAY,IAAI,iBAAiB2D,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAI6D,QAAQ,EAAE;QACnBtD,YAAY,IAAI,eAAesD,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACnD,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/DH,YAAY,GAAG,0BAA0B;QACzC,IAAIvD,KAAK,CAACA,KAAK,EAAE+C,MAAM,EAAE;UACvBQ,YAAY,IAAI,0CAA0C;UAC1Db,MAAM,CAACC,IAAI,CAAC3C,KAAK,CAACA,KAAK,CAAC+C,MAAM,CAAC,CAACyB,OAAO,CAAC2C,KAAK,IAAG;YAC9C5D,YAAY,IAAI,KAAK4D,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACL5D,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC/E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIsD,QAAQ,EAAE;MACnBtD,YAAY,GAAG,0BAA0BsD,QAAQ,EAAE;;IAGrD,OAAO5I,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACsC,YAAY,CAAC,CAAC;EAClD;EAEQ3B,iBAAiBA,CAAC5B,KAAwB;IAChD,IAAIuD,YAAY,GAAG,sCAAsC;IACzD,MAAMsD,QAAQ,GAAG7G,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK,IAAIzD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAI6G,QAAQ,EAAE;MACZtD,YAAY,GAAG,uBAAuBsD,QAAQ,EAAE;KACjD,MAAM,IAAI7G,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7BsD,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,mBAAmB;MAClC,IAAIvD,KAAK,CAACA,KAAK,EAAE+C,MAAM,EAAE;QACvB,MAAM+D,gBAAgB,GAAGpE,MAAM,CAAC+E,MAAM,CAACzH,KAAK,CAACA,KAAK,CAAC+C,MAAM,CAAC,CAAC2E,IAAI,EAAE;QACjEnE,YAAY,IAAI,aAAauD,gBAAgB,CAAC9D,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAIhD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC/E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,mCAAmC;;IAGpD,OAAOtF,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACsC,YAAY,CAAC,CAAC;EAClD;EAEQ5C,WAAWA,CAACX,KAAwB;IAC1C,IAAIuD,YAAY,GAAG,mBAAmB;IACtC,MAAMsD,QAAQ,GAAG7G,KAAK,CAACA,KAAK,EAAEwD,OAAO,IAAIxD,KAAK,CAACA,KAAK,EAAEyD,KAAK;IAE3D,IAAIoD,QAAQ,EAAE;MACZtD,YAAY,GAAGsD,QAAQ;KACxB,MAAM,IAAI7G,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7BsD,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,cAAc;MAC7B,IAAI,CAAC/E,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAIvD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/BsD,YAAY,GAAG,kCAAkC;;IAGnD,OAAOtF,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACsC,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAoE,CAAA,G;qBAj2BUtJ,aAAa,EAAAuJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb7J,aAAa;IAAA8J,OAAA,EAAb9J,aAAa,CAAA+J,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}