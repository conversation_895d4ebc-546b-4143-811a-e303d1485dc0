{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    // Préparer les données à envoyer - nettoyer les valeurs undefined\n    const clientData = {\n      code: client.code.trim()\n    };\n    // Ajouter seulement les champs non vides\n    if (client.syntax && client.syntax.trim()) {\n      clientData.syntax = client.syntax.trim();\n    }\n    if (client.matFiscal && client.matFiscal.trim()) {\n      clientData.matFiscal = client.matFiscal.trim();\n    }\n    if (client.email && client.email.trim()) {\n      clientData.email = client.email.trim();\n    }\n    if (client.telephone && client.telephone.trim()) {\n      clientData.telephone = client.telephone.trim();\n    }\n    console.log('=== CRÉATION CLIENT ===');\n    console.log('Données originales:', client);\n    console.log('Données nettoyées:', clientData);\n    console.log('URL:', this.baseUrl);\n    console.log('Headers:', this.getHeaders());\n    console.log('=======================');\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders()\n    }).pipe(tap(newClient => {\n      console.log('Client créé avec succès:', newClient);\n      // Mettre à jour le cache local\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(error => {\n      console.error('=== ERREUR CRÉATION CLIENT ===');\n      console.error('Status:', error.status);\n      console.error('Status Text:', error.statusText);\n      console.error('Error Body:', error.error);\n      console.error('Message:', error.message);\n      console.error('URL:', error.url);\n      console.error('Données envoyées:', clientData);\n      console.error('Headers envoyés:', this.getHeaders());\n      console.error('===============================');\n      return this.handleCreateError(error);\n    }));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client (format CLT-XXX)\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log(`Code client ${id} résolu vers ID: ${actualId}`);\n      } else {\n        return throwError(() => new Error(`Client avec le code ${id} non trouvé`));\n      }\n    }\n    // Validation flexible de l'ID (accepter GUID ou ID numérique)\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    const isNumericId = /^\\d+$/.test(actualId);\n    if (!isGuid && !isNumericId) {\n      return throwError(() => new Error(`ID invalide: ${actualId}. L'ID doit être un GUID ou un ID numérique.`));\n    }\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('ID original:', id);\n    console.log('ID résolu:', actualId);\n    console.log('Type d\\'ID:', isGuid ? 'GUID' : 'Numérique');\n    console.log('Données à envoyer:', client);\n    console.log('==============================');\n    return this.http.put(`${this.baseUrl}${actualId}`, client, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Client mis à jour avec succès');\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Client supprimé avec succès');\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Méthode pour supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log(`${ids.length} clients supprimés avec succès`);\n    }), catchError(this.handleError));\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthode de debug pour tester la connexion\n  testConnection() {\n    console.log('Test de connexion vers:', this.baseUrl);\n    console.log('Headers utilisés:', this.getHeaders());\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('Test de connexion réussi:', response);\n    }), catchError(error => {\n      console.error('Test de connexion échoué:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Gestionnaire d'erreur spécialisé pour la création\n  handleCreateError(error) {\n    console.error('Erreur lors de la création du client:', error);\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un client.';\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ces caractéristiques existe déjà.';\n    } else if (error.status === 422) {\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    console.error('ClientService error:', error);\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "console", "log", "post", "newClient", "currentData", "value", "status", "statusText", "message", "url", "handleCreateError", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "isNumericId", "put", "clearCurrentClient", "deleteClient", "delete", "deleteSelectedClients", "ids", "length", "body", "removeItem", "data", "getDialogData", "dialogData", "testConnection", "observe", "response", "errorMessage", "apiError", "title", "errors", "validationErrors", "Object", "values", "flat", "join", "navigate", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\n  private currentClientSubject: BehaviorSubject<Client | null>;\n  public currentClient$: Observable<Client | null>;\n  dataChange = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n\n  constructor(private http: HttpClient, private router: Router) {\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n\n  private getClientFromStorage(): Client | null {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\n    });\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false; // Désactivation du loading\n        this.dataChange.next(clients);\n      }),\n      catchError(error => {\n        this.isTblLoading = false; // Désactivation en cas d'erreur\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n\n    // Préparer les données à envoyer - nettoyer les valeurs undefined\n    const clientData: any = {\n      code: client.code.trim()\n    };\n\n    // Ajouter seulement les champs non vides\n    if (client.syntax && client.syntax.trim()) {\n      clientData.syntax = client.syntax.trim();\n    }\n    if (client.matFiscal && client.matFiscal.trim()) {\n      clientData.matFiscal = client.matFiscal.trim();\n    }\n    if (client.email && client.email.trim()) {\n      clientData.email = client.email.trim();\n    }\n    if (client.telephone && client.telephone.trim()) {\n      clientData.telephone = client.telephone.trim();\n    }\n\n    console.log('=== CRÉATION CLIENT ===');\n    console.log('Données originales:', client);\n    console.log('Données nettoyées:', clientData);\n    console.log('URL:', this.baseUrl);\n    console.log('Headers:', this.getHeaders());\n    console.log('=======================');\n\n    return this.http.post<Client>(this.baseUrl, clientData, { headers: this.getHeaders() }).pipe(\n      tap((newClient: Client) => {\n        console.log('Client créé avec succès:', newClient);\n        // Mettre à jour le cache local\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError((error: HttpErrorResponse) => {\n        console.error('=== ERREUR CRÉATION CLIENT ===');\n        console.error('Status:', error.status);\n        console.error('Status Text:', error.statusText);\n        console.error('Error Body:', error.error);\n        console.error('Message:', error.message);\n        console.error('URL:', error.url);\n        console.error('Données envoyées:', clientData);\n        console.error('Headers envoyés:', this.getHeaders());\n        console.error('===============================');\n        return this.handleCreateError(error);\n      })\n    );\n  }\n\n  // Mettre à jour un client\n  updateClient(id: string, client: UpdateClientDto): Observable<void> {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n\n    // Résoudre l'ID si c'est un code client (format CLT-XXX)\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log(`Code client ${id} résolu vers ID: ${actualId}`);\n      } else {\n        return throwError(() => new Error(`Client avec le code ${id} non trouvé`));\n      }\n    }\n\n    // Validation flexible de l'ID (accepter GUID ou ID numérique)\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    const isNumericId = /^\\d+$/.test(actualId);\n\n    if (!isGuid && !isNumericId) {\n      return throwError(() => new Error(`ID invalide: ${actualId}. L'ID doit être un GUID ou un ID numérique.`));\n    }\n\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('ID original:', id);\n    console.log('ID résolu:', actualId);\n    console.log('Type d\\'ID:', isGuid ? 'GUID' : 'Numérique');\n    console.log('Données à envoyer:', client);\n    console.log('==============================');\n\n    return this.http.put<void>(`${this.baseUrl}${actualId}`, client, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        console.log('Client mis à jour avec succès');\n        this.clearCurrentClient();\n      }),\n      catchError(this.handleError)\n    );\n  }\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        console.log('Client supprimé avec succès');\n        this.clearCurrentClient();\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Méthode pour supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        console.log(`${ids.length} clients supprimés avec succès`);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthode de debug pour tester la connexion\n  testConnection(): Observable<any> {\n    console.log('Test de connexion vers:', this.baseUrl);\n    console.log('Headers utilisés:', this.getHeaders());\n\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(\n      tap(response => {\n        console.log('Test de connexion réussi:', response);\n      }),\n      catchError(error => {\n        console.error('Test de connexion échoué:', error);\n        return throwError(() => error);\n      })\n    );\n  }\n\n  // Gestionnaire d'erreur spécialisé pour la création\n  private handleCreateError(error: HttpErrorResponse) {\n    console.error('Erreur lors de la création du client:', error);\n\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un client.';\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec ces caractéristiques existe déjà.';\n    } else if (error.status === 422) {\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n\n  private handleError(error: HttpErrorResponse) {\n    console.error('ClientService error:', error);\n\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,QAAa,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIT,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAU,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIX,eAAe,CAAgB,IAAI,CAACY,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIlB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIsB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/EvB,GAAG,CAACwB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFvB,UAAU,CAACyB,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFvB,GAAG,CAAC8B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAO/B,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOrC,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE;IACA,MAAMrB,UAAU,GAAQ;MACtBsB,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI;KACvB;IAED;IACA,IAAIN,MAAM,CAACO,MAAM,IAAIP,MAAM,CAACO,MAAM,CAACD,IAAI,EAAE,EAAE;MACzCvB,UAAU,CAACwB,MAAM,GAAGP,MAAM,CAACO,MAAM,CAACD,IAAI,EAAE;;IAE1C,IAAIN,MAAM,CAACQ,SAAS,IAAIR,MAAM,CAACQ,SAAS,CAACF,IAAI,EAAE,EAAE;MAC/CvB,UAAU,CAACyB,SAAS,GAAGR,MAAM,CAACQ,SAAS,CAACF,IAAI,EAAE;;IAEhD,IAAIN,MAAM,CAACS,KAAK,IAAIT,MAAM,CAACS,KAAK,CAACH,IAAI,EAAE,EAAE;MACvCvB,UAAU,CAAC0B,KAAK,GAAGT,MAAM,CAACS,KAAK,CAACH,IAAI,EAAE;;IAExC,IAAIN,MAAM,CAACU,SAAS,IAAIV,MAAM,CAACU,SAAS,CAACJ,IAAI,EAAE,EAAE;MAC/CvB,UAAU,CAAC2B,SAAS,GAAGV,MAAM,CAACU,SAAS,CAACJ,IAAI,EAAE;;IAGhDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IACtCD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEZ,MAAM,CAAC;IAC1CW,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE7B,UAAU,CAAC;IAC7C4B,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE,IAAI,CAACpC,OAAO,CAAC;IACjCmC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACxB,UAAU,EAAE,CAAC;IAC1CuB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;IAEtC,OAAO,IAAI,CAACtC,IAAI,CAACuC,IAAI,CAAS,IAAI,CAACrC,OAAO,EAAEO,UAAU,EAAE;MAAES,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC1FvB,GAAG,CAAE4C,SAAiB,IAAI;MACxBH,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEE,SAAS,CAAC;MAClD;MACA,MAAMC,WAAW,GAAG,IAAI,CAACtC,UAAU,CAACuC,KAAK;MACzC,IAAI,CAACvC,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGoB,WAAW,EAAED,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF3C,UAAU,CAAEyB,KAAwB,IAAI;MACtCe,OAAO,CAACf,KAAK,CAAC,gCAAgC,CAAC;MAC/Ce,OAAO,CAACf,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACqB,MAAM,CAAC;MACtCN,OAAO,CAACf,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACsB,UAAU,CAAC;MAC/CP,OAAO,CAACf,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MACzCe,OAAO,CAACf,KAAK,CAAC,UAAU,EAAEA,KAAK,CAACuB,OAAO,CAAC;MACxCR,OAAO,CAACf,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACwB,GAAG,CAAC;MAChCT,OAAO,CAACf,KAAK,CAAC,mBAAmB,EAAEb,UAAU,CAAC;MAC9C4B,OAAO,CAACf,KAAK,CAAC,kBAAkB,EAAE,IAAI,CAACR,UAAU,EAAE,CAAC;MACpDuB,OAAO,CAACf,KAAK,CAAC,iCAAiC,CAAC;MAChD,OAAO,IAAI,CAACyB,iBAAiB,CAACzB,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA0B,YAAYA,CAACvB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEO,IAAI,EAAE,IAAI,CAACN,MAAM,EAAE;MAC1B,OAAO/B,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAImB,QAAQ,GAAGxB,EAAE;IACjB,MAAMyB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAAC1B,EAAE,CAAC;IAC1C,IAAIyB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAACjD,UAAU,CAACuC,KAAK,CAACW,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAI2B,YAAY,EAAE;QAChBH,QAAQ,GAAGG,YAAY,CAAC3B,EAAE;QAC1BY,OAAO,CAACC,GAAG,CAAC,eAAeb,EAAE,oBAAoBwB,QAAQ,EAAE,CAAC;OAC7D,MAAM;QACL,OAAOtD,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,uBAAuBL,EAAE,aAAa,CAAC,CAAC;;;IAI9E;IACA,MAAM8B,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,MAAMO,WAAW,GAAG,OAAO,CAACL,IAAI,CAACF,QAAQ,CAAC;IAE1C,IAAI,CAACM,MAAM,IAAI,CAACC,WAAW,EAAE;MAC3B,OAAO7D,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,gBAAgBmB,QAAQ,8CAA8C,CAAC,CAAC;;IAG5GZ,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEb,EAAE,CAAC;IAC/BY,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEW,QAAQ,CAAC;IACnCZ,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiB,MAAM,GAAG,MAAM,GAAG,WAAW,CAAC;IACzDlB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEZ,MAAM,CAAC;IACzCW,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAACtC,IAAI,CAACyD,GAAG,CAAO,GAAG,IAAI,CAACvD,OAAO,GAAG+C,QAAQ,EAAE,EAAEvB,MAAM,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACnGvB,GAAG,CAAC,MAAK;MACPyC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C,IAAI,CAACoB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF7D,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EACA;EACAoC,YAAYA,CAAClC,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAAC4D,MAAM,CAAO,GAAG,IAAI,CAAC1D,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFvB,GAAG,CAAC,MAAK;MACPyC,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;MAC1C,IAAI,CAACoB,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF7D,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAsC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOpE,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF,OAAO,IAAI,CAAC9B,IAAI,CAAC4D,MAAM,CAAC,GAAG,IAAI,CAAC1D,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BkD,IAAI,EAAEF;KACP,CAAC,CAAC3C,IAAI,CACLvB,GAAG,CAAC,MAAK;MACPyC,OAAO,CAACC,GAAG,CAAC,GAAGwB,GAAG,CAACC,MAAM,gCAAgC,CAAC;IAC5D,CAAC,CAAC,EACFlE,UAAU,CAAC,IAAI,CAAC0B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmC,kBAAkBA,CAAA;IAChBhD,YAAY,CAACuD,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC5D,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI6C,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC/D,UAAU,CAACuC,KAAK;EAC9B;EAEAyB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACAC,cAAcA,CAAA;IACZhC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACpC,OAAO,CAAC;IACpDmC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACxB,UAAU,EAAE,CAAC;IAEnD,OAAO,IAAI,CAACd,IAAI,CAACiB,GAAG,CAAC,IAAI,CAACf,OAAO,EAAE;MACjCgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BwD,OAAO,EAAE;KACV,CAAC,CAACnD,IAAI,CACLvB,GAAG,CAAC2E,QAAQ,IAAG;MACblC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiC,QAAQ,CAAC;IACpD,CAAC,CAAC,EACF1E,UAAU,CAACyB,KAAK,IAAG;MACjBe,OAAO,CAACf,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO3B,UAAU,CAAC,MAAM2B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQyB,iBAAiBA,CAACzB,KAAwB;IAChDe,OAAO,CAACf,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;IAE7D,IAAIkD,YAAY,GAAG,sCAAsC;IACzD,MAAMC,QAAQ,GAAGnD,KAAK,CAACA,KAAK,EAAEuB,OAAO,IAAIvB,KAAK,CAACA,KAAK,EAAEoD,KAAK,IAAIpD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAImD,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAInD,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;MAC7B6B,YAAY,GAAG,2EAA2E;KAC3F,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,uDAAuD;MACtE,IAAIlD,KAAK,CAACA,KAAK,EAAEqD,MAAM,EAAE;QACvB,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAACxD,KAAK,CAACA,KAAK,CAACqD,MAAM,CAAC,CAACI,IAAI,EAAE;QACjEP,YAAY,IAAI,aAAaI,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAI1D,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,6CAA6C;MAC5D,IAAI,CAACvE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3D,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,kDAAkD;KAClE,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,6DAA6D;KAC7E,MAAM,IAAIlD,KAAK,CAACqB,MAAM,IAAI,GAAG,EAAE;MAC9B6B,YAAY,GAAG,kDAAkD;;IAGnE,OAAO7E,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC0C,YAAY,CAAC,CAAC;EAClD;EAEQjD,WAAWA,CAACD,KAAwB;IAC1Ce,OAAO,CAACf,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAE5C,IAAIkD,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAGnD,KAAK,CAACA,KAAK,EAAEuB,OAAO,IAAIvB,KAAK,CAACA,KAAK,EAAEoD,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAInD,KAAK,CAACqB,MAAM,KAAK,CAAC,EAAE;MAC7B6B,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACvE,MAAM,CAACgF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3D,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,WAAW;KAC3B,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAIlD,KAAK,CAACqB,MAAM,KAAK,GAAG,EAAE;MAC/B6B,YAAY,GAAG,kCAAkC;KAClD,MAAM,IAAIlD,KAAK,CAACqB,MAAM,IAAI,GAAG,EAAE;MAC9B6B,YAAY,GAAG,cAAc;;IAG/B,OAAO7E,UAAU,CAAC,MAAM,IAAImC,KAAK,CAAC0C,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAU,CAAA,G;qBAjRUpF,aAAa,EAAAqF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb3F,aAAa;IAAA4F,OAAA,EAAb5F,aAAa,CAAA6F,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}