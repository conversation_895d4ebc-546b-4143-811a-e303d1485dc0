{"ast": null, "code": "import { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { UntypedFormControl, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { ProduitModel } from '../../Model/Produit';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatSelectModule } from '@angular/material/select';\nimport { CommonModule } from '@angular/common';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/material/dialog\";\nimport * as i2 from \"../../services/produit.service\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/form-field\";\nimport * as i7 from \"@angular/material/input\";\nimport * as i8 from \"@angular/material/select\";\nimport * as i9 from \"@angular/material/core\";\nimport * as i10 from \"@angular/common\";\nfunction FormProduitComponent_Conditional_1_mat_option_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const type_r10 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", type_r10.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", type_r10.label, \" \");\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le type est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La description est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le prix HT est requis \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" Le prix doit \\u00EAtre positif \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La TVA est requise \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La TVA doit \\u00EAtre positive \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_mat_error_52_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-error\");\n    i0.ɵɵtext(1, \" La TVA ne peut pas d\\u00E9passer 100% \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction FormProduitComponent_Conditional_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"mat-icon\", 6);\n    i0.ɵɵtext(5, \"inventory_2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function FormProduitComponent_Conditional_1_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.dialogRef.close());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"form\", 10);\n    i0.ɵɵlistener(\"ngSubmit\", function FormProduitComponent_Conditional_1_Template_form_ngSubmit_12_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.submit());\n    });\n    i0.ɵɵelementStart(13, \"div\", 11)(14, \"div\", 12)(15, \"mat-form-field\", 13)(16, \"mat-label\");\n    i0.ɵɵtext(17, \"Type de produit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"mat-select\", 14);\n    i0.ɵɵtemplate(19, FormProduitComponent_Conditional_1_mat_option_19_Template, 2, 2, \"mat-option\", 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(20, FormProduitComponent_Conditional_1_mat_error_20_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 12)(22, \"mat-form-field\", 13)(23, \"mat-label\");\n    i0.ɵɵtext(24, \"Code produit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(25, \"input\", 17);\n    i0.ɵɵelementStart(26, \"mat-icon\", 18);\n    i0.ɵɵtext(27, \"qr_code\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 19)(29, \"mat-form-field\", 13)(30, \"mat-label\");\n    i0.ɵɵtext(31, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(32, \"textarea\", 20);\n    i0.ɵɵtemplate(33, FormProduitComponent_Conditional_1_mat_error_33_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 21)(35, \"mat-form-field\", 13)(36, \"mat-label\");\n    i0.ɵɵtext(37, \"Prix unitaire HT\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"input\", 22);\n    i0.ɵɵelementStart(39, \"span\", 18);\n    i0.ɵɵtext(40, \"\\u20AC\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(41, FormProduitComponent_Conditional_1_mat_error_41_Template, 2, 0, \"mat-error\", 16)(42, FormProduitComponent_Conditional_1_mat_error_42_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 21)(44, \"mat-form-field\", 13)(45, \"mat-label\");\n    i0.ɵɵtext(46, \"TVA (%)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 23);\n    i0.ɵɵelementStart(48, \"span\", 18);\n    i0.ɵɵtext(49, \"%\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, FormProduitComponent_Conditional_1_mat_error_50_Template, 2, 0, \"mat-error\", 16)(51, FormProduitComponent_Conditional_1_mat_error_51_Template, 2, 0, \"mat-error\", 16)(52, FormProduitComponent_Conditional_1_mat_error_52_Template, 2, 0, \"mat-error\", 16);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"div\", 21)(54, \"mat-form-field\", 13)(55, \"mat-label\");\n    i0.ɵɵtext(56, \"Prix unitaire TTC\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(57, \"input\", 24);\n    i0.ɵɵpipe(58, \"number\");\n    i0.ɵɵelementStart(59, \"span\", 18);\n    i0.ɵɵtext(60, \"\\u20AC\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-hint\");\n    i0.ɵɵtext(62, \"Calcul\\u00E9 automatiquement\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(63, \"div\", 11)(64, \"div\", 19)(65, \"div\", 25)(66, \"button\", 26)(67, \"mat-icon\");\n    i0.ɵɵtext(68, \"save\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(70, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function FormProduitComponent_Conditional_1_Template_button_click_70_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r14 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r14.onNoClick());\n    });\n    i0.ɵɵelementStart(71, \"mat-icon\");\n    i0.ɵɵtext(72, \"cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(73, \" Annuler \");\n    i0.ɵɵelementEnd()()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_3_0;\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.dialogTitle, \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.produitForm);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.typesProduits);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.produitForm.get(\"type\")) == null ? null : tmp_3_0.hasError(\"required\"));\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", (tmp_4_0 = ctx_r0.produitForm.get(\"description\")) == null ? null : tmp_4_0.hasError(\"required\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_5_0 = ctx_r0.produitForm.get(\"prixUnitaireHT\")) == null ? null : tmp_5_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_6_0 = ctx_r0.produitForm.get(\"prixUnitaireHT\")) == null ? null : tmp_6_0.hasError(\"min\"));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", (tmp_7_0 = ctx_r0.produitForm.get(\"tva\")) == null ? null : tmp_7_0.hasError(\"required\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_8_0 = ctx_r0.produitForm.get(\"tva\")) == null ? null : tmp_8_0.hasError(\"min\"));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_9_0 = ctx_r0.produitForm.get(\"tva\")) == null ? null : tmp_9_0.hasError(\"max\"));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"value\", i0.ɵɵpipeBind2(58, 13, ctx_r0.produit.prixUnitaireTTC, \"1.2-2\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"disabled\", !ctx_r0.produitForm.valid);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.action === \"add\" ? \"Ajouter\" : \"Modifier\", \" \");\n  }\n}\nfunction FormProduitComponent_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"mat-icon\", 6);\n    i0.ɵɵtext(5, \"inventory_2\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"button\", 8);\n    i0.ɵɵlistener(\"click\", function FormProduitComponent_Conditional_2_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r15 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r15.dialogRef.close());\n    });\n    i0.ɵɵelementStart(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 30)(13, \"div\", 11)(14, \"div\", 31)(15, \"div\", 32)(16, \"strong\");\n    i0.ɵɵtext(17, \"Type:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 32)(20, \"strong\");\n    i0.ɵɵtext(21, \"Code produit:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"strong\");\n    i0.ɵɵtext(25, \"Prix HT:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(26);\n    i0.ɵɵpipe(27, \"currency\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 31)(29, \"div\", 32)(30, \"strong\");\n    i0.ɵɵtext(31, \"TVA:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"div\", 32)(34, \"strong\");\n    i0.ɵɵtext(35, \"Prix TTC:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36);\n    i0.ɵɵpipe(37, \"currency\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"div\", 11)(39, \"div\", 33)(40, \"div\", 32)(41, \"strong\");\n    i0.ɵɵtext(42, \"Description:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\");\n    i0.ɵɵtext(44);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(45, \"div\", 11)(46, \"div\", 33)(47, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function FormProduitComponent_Conditional_2_Template_button_click_47_listener() {\n      i0.ɵɵrestoreView(_r16);\n      const ctx_r17 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r17.onNoClick());\n    });\n    i0.ɵɵelementStart(48, \"mat-icon\");\n    i0.ɵɵtext(49, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" Fermer \");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" D\\u00E9tails du produit: \", ctx_r1.produit.type, \" \");\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.produit.type, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.produit.codeProd || \"Non d\\u00E9fini\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(27, 7, ctx_r1.produit.prixUnitaireHT, \"EUR\", \"symbol\", \"1.2-2\"), \" \");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.produit.tva, \"% \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind4(37, 12, ctx_r1.produit.prixUnitaireTTC, \"EUR\", \"symbol\", \"1.2-2\"), \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.produit.description);\n  }\n}\nexport class FormProduitComponent {\n  constructor(dialogRef, data, produitService, fb) {\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.produitService = produitService;\n    this.fb = fb;\n    this.isDetails = false;\n    // Types de produits disponibles\n    this.typesProduits = [{\n      value: 'Service',\n      label: 'Service'\n    }, {\n      value: 'Produit',\n      label: 'Produit'\n    }, {\n      value: 'Matériel',\n      label: 'Matériel'\n    }, {\n      value: 'Logiciel',\n      label: 'Logiciel'\n    }, {\n      value: 'Formation',\n      label: 'Formation'\n    }, {\n      value: 'Consultation',\n      label: 'Consultation'\n    }];\n    this.formControl = new UntypedFormControl('', [Validators.required]);\n    // Set the defaults\n    this.action = data.action;\n    if (this.action === 'edit') {\n      this.isDetails = false;\n      this.dialogTitle = `Modifier: ${data.produit.type}`;\n      this.produit = new ProduitModel(data.produit);\n      this.produitForm = this.createProduitForm();\n    } else if (this.action === 'details') {\n      this.produit = new ProduitModel(data.produit);\n      this.isDetails = true;\n    } else {\n      this.isDetails = false;\n      this.dialogTitle = 'Nouveau Produit';\n      const blankObject = {};\n      this.produit = new ProduitModel(blankObject);\n      this.produitForm = this.createProduitForm();\n    }\n  }\n  getErrorMessage() {\n    return this.formControl.hasError('required') ? 'Champ requis' : this.formControl.hasError('min') ? 'La valeur doit être positive' : '';\n  }\n  createProduitForm() {\n    const form = this.fb.group({\n      id: [this.produit.id],\n      type: [this.produit.type, [Validators.required]],\n      description: [this.produit.description, [Validators.required]],\n      prixUnitaireHT: [this.produit.prixUnitaireHT, [Validators.required, Validators.min(0)]],\n      tva: [this.produit.tva, [Validators.required, Validators.min(0), Validators.max(100)]],\n      codeProd: [this.produit.codeProd]\n    });\n    // Calculer automatiquement le prix TTC quand le prix HT ou la TVA change\n    form.get('prixUnitaireHT')?.valueChanges.subscribe(() => {\n      this.calculateTTC();\n    });\n    form.get('tva')?.valueChanges.subscribe(() => {\n      this.calculateTTC();\n    });\n    return form;\n  }\n  calculateTTC() {\n    if (this.produitForm) {\n      const prixHT = this.produitForm.get('prixUnitaireHT')?.value || 0;\n      const tva = this.produitForm.get('tva')?.value || 0;\n      const prixTTC = this.produitService.calculateTTC(prixHT, tva);\n      this.produit.prixUnitaireTTC = prixTTC;\n    }\n  }\n  submit() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      if (this.action === 'add') {\n        this.confirmAdd();\n      } else if (this.action === 'edit') {\n        this.confirmEdit();\n      }\n    }\n  }\n  onNoClick() {\n    this.dialogRef.close();\n  }\n  confirmAdd() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      const createDto = {\n        type: formValue.type,\n        description: formValue.description,\n        prixUnitaireHT: formValue.prixUnitaireHT,\n        tva: formValue.tva,\n        codeProd: formValue.codeProd\n      };\n      this.produitService.createProduit(createDto).subscribe({\n        next: result => {\n          this.dialogRef.close(result);\n        },\n        error: error => {\n          console.error('Erreur lors de la création du produit:', error);\n        }\n      });\n    }\n  }\n  confirmEdit() {\n    if (this.produitForm?.valid) {\n      const formValue = this.produitForm.getRawValue();\n      const updateDto = {\n        type: formValue.type,\n        description: formValue.description,\n        prixUnitaireHT: formValue.prixUnitaireHT,\n        tva: formValue.tva,\n        codeProd: formValue.codeProd\n      };\n      this.produitService.updateProduit(this.produit.id, updateDto).subscribe({\n        next: () => {\n          this.dialogRef.close(true);\n        },\n        error: error => {\n          console.error('Erreur lors de la modification du produit:', error);\n        }\n      });\n    }\n  }\n  static #_ = this.ɵfac = function FormProduitComponent_Factory(t) {\n    return new (t || FormProduitComponent)(i0.ɵɵdirectiveInject(i1.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA), i0.ɵɵdirectiveInject(i2.ProduitService), i0.ɵɵdirectiveInject(i3.UntypedFormBuilder));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: FormProduitComponent,\n    selectors: [[\"app-form-produit\"]],\n    standalone: true,\n    features: [i0.ɵɵStandaloneFeature],\n    decls: 3,\n    vars: 1,\n    consts: [[1, \"addContainer\"], [\"class\", \"produit-form\"], [1, \"produit-form\"], [1, \"modalHeader\"], [1, \"editRowModal\"], [1, \"modalHeader\", \"clearfix\"], [1, \"product-icon\"], [1, \"modal-about\"], [\"mat-icon-button\", \"\", \"aria-label\", \"Close dialog\", 1, \"modal-close-button\", 3, \"click\"], [\"mat-dialog-content\", \"\"], [1, \"register-form\", \"m-4\", 3, \"formGroup\", \"ngSubmit\"], [1, \"row\"], [1, \"col-xl-6\", \"col-lg-6\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"appearance\", \"outline\", 1, \"example-full-width\", \"mb-3\"], [\"formControlName\", \"type\", \"required\", \"\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [\"matInput\", \"\", \"formControlName\", \"codeProd\", \"placeholder\", \"Ex: PROD001\"], [\"matSuffix\", \"\"], [1, \"col-xl-12\", \"col-lg-12\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"required\", \"\", \"rows\", \"3\", \"placeholder\", \"Description d\\u00E9taill\\u00E9e du produit\"], [1, \"col-xl-4\", \"col-lg-4\", \"col-md-12\", \"col-sm-12\", \"mb-2\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"prixUnitaireHT\", \"required\", \"\", \"min\", \"0\", \"step\", \"0.01\", \"placeholder\", \"0.00\"], [\"matInput\", \"\", \"type\", \"number\", \"formControlName\", \"tva\", \"required\", \"\", \"min\", \"0\", \"max\", \"100\", \"step\", \"0.1\", \"placeholder\", \"20\"], [\"matInput\", \"\", \"type\", \"number\", \"readonly\", \"\", \"placeholder\", \"0.00\", 3, \"value\"], [1, \"example-button-row\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"btn-space\", 3, \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"type\", \"button\", 3, \"click\"], [3, \"value\"], [1, \"produit-details\"], [1, \"details-content\", \"m-4\"], [1, \"col-md-6\"], [1, \"detail-item\"], [1, \"col-md-12\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"]],\n    template: function FormProduitComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0);\n        i0.ɵɵtemplate(1, FormProduitComponent_Conditional_1_Template, 74, 16, \"div\", 1)(2, FormProduitComponent_Conditional_2_Template, 51, 17);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance();\n        i0.ɵɵconditional(1, !ctx.isDetails ? 1 : 2);\n      }\n    },\n    dependencies: [MatButtonModule, i4.MatButton, i4.MatIconButton, MatIconModule, i5.MatIcon, FormsModule, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NumberValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.RequiredValidator, i3.MinValidator, i3.MaxValidator, ReactiveFormsModule, i3.FormGroupDirective, i3.FormControlName, MatFormFieldModule, i6.MatFormField, i6.MatLabel, i6.MatHint, i6.MatError, i6.MatSuffix, MatInputModule, i7.MatInput, MatSelectModule, i8.MatSelect, i9.MatOption, MatCardModule, CommonModule, i10.NgForOf, i10.NgIf, i10.DecimalPipe, i10.CurrencyPipe],\n    styles: [\".addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding: 16px 24px;\\n  border-bottom: 1px solid #e0e0e0;\\n  background-color: #f5f5f5;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  padding: 0;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .product-icon[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .product-icon[_ngcontent-%COMP%] {\\n  font-size: 24px;\\n  margin-right: 12px;\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%] {\\n  font-size: 18px;\\n  font-weight: 500;\\n  color: #333;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%]:hover, .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-close-button[_ngcontent-%COMP%]:hover {\\n  color: #333;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-full-width[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 12px;\\n  justify-content: flex-end;\\n  margin-top: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   .btn-space[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline[_ngcontent-%COMP%]   .mat-form-field-outline[_ngcontent-%COMP%] {\\n  color: #ddd;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-form-field-appearance-outline.mat-focused[_ngcontent-%COMP%]   .mat-form-field-outline-thick[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   mat-form-field.mat-focused[_ngcontent-%COMP%]   .mat-form-field-label[_ngcontent-%COMP%] {\\n  color: #1976d2;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%]   input[readonly][_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #666;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n  padding: 8px 0;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  color: #333;\\n  font-weight: 600;\\n  margin-right: 8px;\\n}\\n.addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%]   .detail-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  margin-bottom: 0;\\n  color: #666;\\n  line-height: 1.5;\\n}\\n\\n@media (max-width: 768px) {\\n  .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .editRowModal[_ngcontent-%COMP%]   .modalHeader[_ngcontent-%COMP%]   .modal-about[_ngcontent-%COMP%] {\\n    font-size: 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .register-form[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .details-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n  }\\n  .addContainer[_ngcontent-%COMP%]   .produit-form[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   button[_ngcontent-%COMP%], .addContainer[_ngcontent-%COMP%]   .produit-details[_ngcontent-%COMP%]   .example-button-row[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n}\\n.mat-error[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_shake 0.3s ease-in-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_shake {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  25% {\\n    transform: translateX(-5px);\\n  }\\n  75% {\\n    transform: translateX(5px);\\n  }\\n}\\n.product-icon[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\nbutton[mat-raised-button][_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\nbutton[mat-raised-button][_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-right: 8px;\\n}\\n\\n.mat-hint[_ngcontent-%COMP%] {\\n  font-size: 12px;\\n  color: #999;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["MAT_DIALOG_DATA", "UntypedFormControl", "Validators", "FormsModule", "ReactiveFormsModule", "ProduitModel", "MatCardModule", "MatInputModule", "MatFormFieldModule", "MatIconModule", "MatButtonModule", "MatSelectModule", "CommonModule", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "type_r10", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "ɵɵlistener", "FormProduitComponent_Conditional_1_Template_button_click_8_listener", "ɵɵrestoreView", "_r12", "ctx_r11", "ɵɵnextContext", "ɵɵresetView", "dialogRef", "close", "FormProduitComponent_Conditional_1_Template_form_ngSubmit_12_listener", "ctx_r13", "submit", "ɵɵtemplate", "FormProduitComponent_Conditional_1_mat_option_19_Template", "FormProduitComponent_Conditional_1_mat_error_20_Template", "ɵɵelement", "FormProduitComponent_Conditional_1_mat_error_33_Template", "FormProduitComponent_Conditional_1_mat_error_41_Template", "FormProduitComponent_Conditional_1_mat_error_42_Template", "FormProduitComponent_Conditional_1_mat_error_50_Template", "FormProduitComponent_Conditional_1_mat_error_51_Template", "FormProduitComponent_Conditional_1_mat_error_52_Template", "FormProduitComponent_Conditional_1_Template_button_click_70_listener", "ctx_r14", "onNoClick", "ctx_r0", "dialogTitle", "produitForm", "typesProduits", "tmp_3_0", "get", "<PERSON><PERSON><PERSON><PERSON>", "tmp_4_0", "tmp_5_0", "tmp_6_0", "tmp_7_0", "tmp_8_0", "tmp_9_0", "ɵɵpipeBind2", "produit", "prixUnitaireTTC", "valid", "action", "FormProduitComponent_Conditional_2_Template_button_click_8_listener", "_r16", "ctx_r15", "FormProduitComponent_Conditional_2_Template_button_click_47_listener", "ctx_r17", "ctx_r1", "type", "codeProd", "ɵɵpipeBind4", "prixUnitaireHT", "tva", "ɵɵtextInterpolate", "description", "FormProduitComponent", "constructor", "data", "produitService", "fb", "isDetails", "formControl", "required", "createProduitForm", "blankObject", "getErrorMessage", "form", "group", "id", "min", "max", "valueChanges", "subscribe", "calculateTTC", "prixHT", "prixTTC", "formValue", "getRawValue", "confirmAdd", "confirmEdit", "createDto", "createProduit", "next", "result", "error", "console", "updateDto", "updateProduit", "_", "ɵɵdirectiveInject", "i1", "MatDialogRef", "i2", "ProduitService", "i3", "UntypedFormBuilder", "_2", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FormProduitComponent_Template", "rf", "ctx", "FormProduitComponent_Conditional_1_Template", "FormProduitComponent_Conditional_2_Template", "ɵɵconditional", "i4", "MatButton", "MatIconButton", "i5", "MatIcon", "ɵNgNoValidate", "DefaultValueAccessor", "NumberValueAccessor", "NgControlStatus", "NgControlStatusGroup", "RequiredValidator", "MinValidator", "MaxValidator", "FormGroupDirective", "FormControlName", "i6", "MatFormField", "<PERSON><PERSON><PERSON><PERSON>", "MatHint", "<PERSON><PERSON><PERSON><PERSON>", "MatSuffix", "i7", "MatInput", "i8", "MatSelect", "i9", "MatOption", "i10", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "C<PERSON><PERSON>cyPipe", "styles"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\form-produit\\form-produit.component.ts", "C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Produit\\form-produit\\form-produit.component.html"], "sourcesContent": ["import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';\r\nimport { Component, Inject } from '@angular/core';\r\nimport { ProduitService } from '../../services/produit.service';\r\nimport { UntypedFormControl, Validators, UntypedFormGroup, UntypedFormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';\r\nimport { Produit, ProduitModel, CreateProduitDTO, UpdateProduitDTO } from '../../Model/Produit';\r\nimport { MatCardModule } from '@angular/material/card';\r\nimport { MatInputModule } from '@angular/material/input';\r\nimport { MatFormFieldModule } from '@angular/material/form-field';\r\nimport { MatIconModule } from '@angular/material/icon';\r\nimport { MatButtonModule } from '@angular/material/button';\r\nimport { MatSelectModule } from '@angular/material/select';\r\nimport { CommonModule } from '@angular/common';\r\n\r\nexport interface DialogData {\r\n  id: string;\r\n  action: string;\r\n  produit: Produit;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-form-produit',\r\n  templateUrl: './form-produit.component.html',\r\n  styleUrls: ['./form-produit.component.scss'],\r\n  standalone: true,\r\n  imports: [\r\n    MatButtonModule,\r\n    MatIconModule,\r\n    FormsModule,\r\n    ReactiveFormsModule,\r\n    MatFormFieldModule,\r\n    MatInputModule,\r\n    MatSelectModule,\r\n    MatCardModule,\r\n    CommonModule,\r\n  ],\r\n})\r\nexport class FormProduitComponent {\r\n  action: string;\r\n  dialogTitle?: string;\r\n  isDetails = false;\r\n  produitForm?: UntypedFormGroup;\r\n  produit: ProduitModel;\r\n\r\n  // Types de produits disponibles\r\n  typesProduits = [\r\n    { value: 'Service', label: 'Service' },\r\n    { value: 'Produit', label: 'Produit' },\r\n    { value: 'Matériel', label: 'Matériel' },\r\n    { value: 'Logiciel', label: 'Logiciel' },\r\n    { value: 'Formation', label: 'Formation' },\r\n    { value: 'Consultation', label: 'Consultation' }\r\n  ];\r\n\r\n  constructor(\r\n    public dialogRef: MatDialogRef<FormProduitComponent>,\r\n    @Inject(MAT_DIALOG_DATA) public data: DialogData,\r\n    public produitService: ProduitService,\r\n    private fb: UntypedFormBuilder\r\n  ) {\r\n    // Set the defaults\r\n    this.action = data.action;\r\n    if (this.action === 'edit') {\r\n      this.isDetails = false;\r\n      this.dialogTitle = `Modifier: ${data.produit.type}`;\r\n      this.produit = new ProduitModel(data.produit);\r\n      this.produitForm = this.createProduitForm();\r\n    } else if (this.action === 'details') {\r\n      this.produit = new ProduitModel(data.produit);\r\n      this.isDetails = true;\r\n    } else {\r\n      this.isDetails = false;\r\n      this.dialogTitle = 'Nouveau Produit';\r\n      const blankObject = {} as Produit;\r\n      this.produit = new ProduitModel(blankObject);\r\n      this.produitForm = this.createProduitForm();\r\n    }\r\n  }\r\n\r\n  formControl = new UntypedFormControl('', [\r\n    Validators.required,\r\n  ]);\r\n\r\n  getErrorMessage() {\r\n    return this.formControl.hasError('required')\r\n      ? 'Champ requis'\r\n      : this.formControl.hasError('min')\r\n      ? 'La valeur doit être positive'\r\n      : '';\r\n  }\r\n\r\n  createProduitForm(): UntypedFormGroup {\r\n    const form = this.fb.group({\r\n      id: [this.produit.id],\r\n      type: [this.produit.type, [Validators.required]],\r\n      description: [this.produit.description, [Validators.required]],\r\n      prixUnitaireHT: [this.produit.prixUnitaireHT, [Validators.required, Validators.min(0)]],\r\n      tva: [this.produit.tva, [Validators.required, Validators.min(0), Validators.max(100)]],\r\n      codeProd: [this.produit.codeProd],\r\n    });\r\n\r\n    // Calculer automatiquement le prix TTC quand le prix HT ou la TVA change\r\n    form.get('prixUnitaireHT')?.valueChanges.subscribe(() => {\r\n      this.calculateTTC();\r\n    });\r\n\r\n    form.get('tva')?.valueChanges.subscribe(() => {\r\n      this.calculateTTC();\r\n    });\r\n\r\n    return form;\r\n  }\r\n\r\n  calculateTTC() {\r\n    if (this.produitForm) {\r\n      const prixHT = this.produitForm.get('prixUnitaireHT')?.value || 0;\r\n      const tva = this.produitForm.get('tva')?.value || 0;\r\n      const prixTTC = this.produitService.calculateTTC(prixHT, tva);\r\n      this.produit.prixUnitaireTTC = prixTTC;\r\n    }\r\n  }\r\n\r\n  submit() {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n\r\n      if (this.action === 'add') {\r\n        this.confirmAdd();\r\n      } else if (this.action === 'edit') {\r\n        this.confirmEdit();\r\n      }\r\n    }\r\n  }\r\n\r\n  onNoClick(): void {\r\n    this.dialogRef.close();\r\n  }\r\n\r\n  public confirmAdd(): void {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n      const createDto: CreateProduitDTO = {\r\n        type: formValue.type,\r\n        description: formValue.description,\r\n        prixUnitaireHT: formValue.prixUnitaireHT,\r\n        tva: formValue.tva,\r\n        codeProd: formValue.codeProd\r\n      };\r\n\r\n      this.produitService.createProduit(createDto).subscribe({\r\n        next: (result) => {\r\n          this.dialogRef.close(result);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la création du produit:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n\r\n  public confirmEdit(): void {\r\n    if (this.produitForm?.valid) {\r\n      const formValue = this.produitForm.getRawValue();\r\n      const updateDto: UpdateProduitDTO = {\r\n        type: formValue.type,\r\n        description: formValue.description,\r\n        prixUnitaireHT: formValue.prixUnitaireHT,\r\n        tva: formValue.tva,\r\n        codeProd: formValue.codeProd\r\n      };\r\n\r\n      this.produitService.updateProduit(this.produit.id, updateDto).subscribe({\r\n        next: () => {\r\n          this.dialogRef.close(true);\r\n        },\r\n        error: (error) => {\r\n          console.error('Erreur lors de la modification du produit:', error);\r\n        }\r\n      });\r\n    }\r\n  }\r\n}\r\n", "<div class=\"addContainer\">\r\n  @if (!isDetails) {\r\n  <div class=\"produit-form\">\r\n    <div class=\"modalHeader\">\r\n      <div class=\"editRowModal\">\r\n        <div class=\"modalHeader clearfix\">\r\n          <mat-icon class=\"product-icon\">inventory_2</mat-icon>\r\n          <div class=\"modal-about\">\r\n            {{dialogTitle}}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button mat-icon-button (click)=\"dialogRef.close()\" class=\"modal-close-button\" aria-label=\"Close dialog\">\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </div>\r\n    <div mat-dialog-content>\r\n      <form class=\"register-form m-4\" [formGroup]=\"produitForm!\" (ngSubmit)=\"submit()\">\r\n        <div class=\"row\">\r\n          <!-- Type de produit -->\r\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>Type de produit</mat-label>\r\n              <mat-select formControlName=\"type\" required>\r\n                <mat-option *ngFor=\"let type of typesProduits\" [value]=\"type.value\">\r\n                  {{type.label}}\r\n                </mat-option>\r\n              </mat-select>\r\n              <mat-error *ngIf=\"produitForm!.get('type')?.hasError('required')\">\r\n                Le type est requis\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- Code produit -->\r\n          <div class=\"col-xl-6 col-lg-6 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>Code produit</mat-label>\r\n              <input matInput formControlName=\"codeProd\" placeholder=\"Ex: PROD001\">\r\n              <mat-icon matSuffix>qr_code</mat-icon>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- Description -->\r\n          <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>Description</mat-label>\r\n              <textarea matInput formControlName=\"description\" required rows=\"3\"\r\n                        placeholder=\"Description détaillée du produit\"></textarea>\r\n              <mat-error *ngIf=\"produitForm!.get('description')?.hasError('required')\">\r\n                La description est requise\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- Prix unitaire HT -->\r\n          <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>Prix unitaire HT</mat-label>\r\n              <input matInput type=\"number\" formControlName=\"prixUnitaireHT\" required\r\n                     min=\"0\" step=\"0.01\" placeholder=\"0.00\">\r\n              <span matSuffix>€</span>\r\n              <mat-error *ngIf=\"produitForm!.get('prixUnitaireHT')?.hasError('required')\">\r\n                Le prix HT est requis\r\n              </mat-error>\r\n              <mat-error *ngIf=\"produitForm!.get('prixUnitaireHT')?.hasError('min')\">\r\n                Le prix doit être positif\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- TVA -->\r\n          <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>TVA (%)</mat-label>\r\n              <input matInput type=\"number\" formControlName=\"tva\" required\r\n                     min=\"0\" max=\"100\" step=\"0.1\" placeholder=\"20\">\r\n              <span matSuffix>%</span>\r\n              <mat-error *ngIf=\"produitForm!.get('tva')?.hasError('required')\">\r\n                La TVA est requise\r\n              </mat-error>\r\n              <mat-error *ngIf=\"produitForm!.get('tva')?.hasError('min')\">\r\n                La TVA doit être positive\r\n              </mat-error>\r\n              <mat-error *ngIf=\"produitForm!.get('tva')?.hasError('max')\">\r\n                La TVA ne peut pas dépasser 100%\r\n              </mat-error>\r\n            </mat-form-field>\r\n          </div>\r\n\r\n          <!-- Prix TTC (calculé automatiquement) -->\r\n          <div class=\"col-xl-4 col-lg-4 col-md-12 col-sm-12 mb-2\">\r\n            <mat-form-field class=\"example-full-width mb-3\" appearance=\"outline\">\r\n              <mat-label>Prix unitaire TTC</mat-label>\r\n              <input matInput type=\"number\" [value]=\"produit.prixUnitaireTTC | number:'1.2-2'\"\r\n                     readonly placeholder=\"0.00\">\r\n              <span matSuffix>€</span>\r\n              <mat-hint>Calculé automatiquement</mat-hint>\r\n            </mat-form-field>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Boutons d'action -->\r\n        <div class=\"row\">\r\n          <div class=\"col-xl-12 col-lg-12 col-md-12 col-sm-12 mb-2\">\r\n            <div class=\"example-button-row\">\r\n              <button mat-raised-button color=\"primary\" type=\"submit\"\r\n                      [disabled]=\"!produitForm!.valid\" class=\"btn-space\">\r\n                <mat-icon>save</mat-icon>\r\n                {{action === 'add' ? 'Ajouter' : 'Modifier'}}\r\n              </button>\r\n              <button mat-raised-button color=\"warn\" (click)=\"onNoClick()\" type=\"button\">\r\n                <mat-icon>cancel</mat-icon>\r\n                Annuler\r\n              </button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </form>\r\n    </div>\r\n  </div>\r\n  } @else {\r\n  <!-- Mode détails (lecture seule) -->\r\n  <div class=\"produit-details\">\r\n    <div class=\"modalHeader\">\r\n      <div class=\"editRowModal\">\r\n        <div class=\"modalHeader clearfix\">\r\n          <mat-icon class=\"product-icon\">inventory_2</mat-icon>\r\n          <div class=\"modal-about\">\r\n            Détails du produit: {{produit.type}}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <button mat-icon-button (click)=\"dialogRef.close()\" class=\"modal-close-button\" aria-label=\"Close dialog\">\r\n        <mat-icon>close</mat-icon>\r\n      </button>\r\n    </div>\r\n    <div mat-dialog-content>\r\n      <div class=\"details-content m-4\">\r\n        <div class=\"row\">\r\n          <div class=\"col-md-6\">\r\n            <div class=\"detail-item\">\r\n              <strong>Type:</strong> {{produit.type}}\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <strong>Code produit:</strong> {{produit.codeProd || 'Non défini'}}\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <strong>Prix HT:</strong> {{produit.prixUnitaireHT | currency:'EUR':'symbol':'1.2-2'}}\r\n            </div>\r\n          </div>\r\n          <div class=\"col-md-6\">\r\n            <div class=\"detail-item\">\r\n              <strong>TVA:</strong> {{produit.tva}}%\r\n            </div>\r\n            <div class=\"detail-item\">\r\n              <strong>Prix TTC:</strong> {{produit.prixUnitaireTTC | currency:'EUR':'symbol':'1.2-2'}}\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-12\">\r\n            <div class=\"detail-item\">\r\n              <strong>Description:</strong>\r\n              <p>{{produit.description}}</p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row\">\r\n          <div class=\"col-md-12\">\r\n            <button mat-raised-button color=\"primary\" (click)=\"onNoClick()\">\r\n              <mat-icon>close</mat-icon>\r\n              Fermer\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  }\r\n</div>\r\n"], "mappings": "AAAA,SAASA,eAAe,QAAsB,0BAA0B;AAGxE,SAASC,kBAAkB,EAAEC,UAAU,EAAwCC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvI,SAAkBC,YAAY,QAA4C,qBAAqB;AAC/F,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;;;;;;;;ICa9BC,EAAA,CAAAC,cAAA,qBAAoE;IAClED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAFkCH,EAAA,CAAAI,UAAA,UAAAC,QAAA,CAAAC,KAAA,CAAoB;IACjEN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,QAAA,CAAAI,KAAA,MACF;;;;;IAEFT,EAAA,CAAAC,cAAA,gBAAkE;IAChED,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAmBZH,EAAA,CAAAC,cAAA,gBAAyE;IACvED,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAA4E;IAC1ED,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAAuE;IACrED,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IAWZH,EAAA,CAAAC,cAAA,gBAAiE;IAC/DD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,uCACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;IACZH,EAAA,CAAAC,cAAA,gBAA4D;IAC1DD,EAAA,CAAAE,MAAA,8CACF;IAAAF,EAAA,CAAAG,YAAA,EAAY;;;;;;IApFxBH,EAAA,CAAAC,cAAA,aAA0B;IAIaD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAyG;IAAjFD,EAAA,CAAAU,UAAA,mBAAAC,oEAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAC,OAAA,GAAAd,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAF,OAAA,CAAAG,SAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IACjDlB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG9BH,EAAA,CAAAC,cAAA,cAAwB;IACqCD,EAAA,CAAAU,UAAA,sBAAAS,sEAAA;MAAAnB,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAO,OAAA,GAAApB,EAAA,CAAAe,aAAA;MAAA,OAAYf,EAAA,CAAAgB,WAAA,CAAAI,OAAA,CAAAC,MAAA,EAAQ;IAAA,EAAC;IAC9ErB,EAAA,CAAAC,cAAA,eAAiB;IAIAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACtCH,EAAA,CAAAC,cAAA,sBAA4C;IAC1CD,EAAA,CAAAsB,UAAA,KAAAC,yDAAA,yBAEa;IACfvB,EAAA,CAAAG,YAAA,EAAa;IACbH,EAAA,CAAAsB,UAAA,KAAAE,wDAAA,wBAEY;IACdxB,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACnCH,EAAA,CAAAyB,SAAA,iBAAqE;IACrEzB,EAAA,CAAAC,cAAA,oBAAoB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAK1CH,EAAA,CAAAC,cAAA,eAA0D;IAE3CD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAClCH,EAAA,CAAAyB,SAAA,oBACoE;IACpEzB,EAAA,CAAAsB,UAAA,KAAAI,wDAAA,wBAEY;IACd1B,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACvCH,EAAA,CAAAyB,SAAA,iBAC8C;IAC9CzB,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxBH,EAAA,CAAAsB,UAAA,KAAAK,wDAAA,wBAEY,KAAAC,wDAAA;IAId5B,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAyB,SAAA,iBACqD;IACrDzB,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxBH,EAAA,CAAAsB,UAAA,KAAAO,wDAAA,wBAEY,KAAAC,wDAAA,6BAAAC,wDAAA;IAOd/B,EAAA,CAAAG,YAAA,EAAiB;IAInBH,EAAA,CAAAC,cAAA,eAAwD;IAEzCD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAyB,SAAA,iBACmC;;IACnCzB,EAAA,CAAAC,cAAA,gBAAgB;IAAAD,EAAA,CAAAE,MAAA,cAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxBH,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,oCAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAMlDH,EAAA,CAAAC,cAAA,eAAiB;IAKCD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA2E;IAApCD,EAAA,CAAAU,UAAA,mBAAAsB,qEAAA;MAAAhC,EAAA,CAAAY,aAAA,CAAAC,IAAA;MAAA,MAAAoB,OAAA,GAAAjC,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAiB,OAAA,CAAAC,SAAA,EAAW;IAAA,EAAC;IAC1DlC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;;;;;;IA1GXH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA2B,MAAA,CAAAC,WAAA,MACF;IAQ4BpC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAI,UAAA,cAAA+B,MAAA,CAAAE,WAAA,CAA0B;IAOnBrC,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAI,UAAA,YAAA+B,MAAA,CAAAG,aAAA,CAAgB;IAInCtC,EAAA,CAAAO,SAAA,EAAoD;IAApDP,EAAA,CAAAI,UAAA,UAAAmC,OAAA,GAAAJ,MAAA,CAAAE,WAAA,CAAAG,GAAA,2BAAAD,OAAA,CAAAE,QAAA,aAAoD;IAqBpDzC,EAAA,CAAAO,SAAA,IAA2D;IAA3DP,EAAA,CAAAI,UAAA,UAAAsC,OAAA,GAAAP,MAAA,CAAAE,WAAA,CAAAG,GAAA,kCAAAE,OAAA,CAAAD,QAAA,aAA2D;IAa3DzC,EAAA,CAAAO,SAAA,GAA8D;IAA9DP,EAAA,CAAAI,UAAA,UAAAuC,OAAA,GAAAR,MAAA,CAAAE,WAAA,CAAAG,GAAA,qCAAAG,OAAA,CAAAF,QAAA,aAA8D;IAG9DzC,EAAA,CAAAO,SAAA,EAAyD;IAAzDP,EAAA,CAAAI,UAAA,UAAAwC,OAAA,GAAAT,MAAA,CAAAE,WAAA,CAAAG,GAAA,qCAAAI,OAAA,CAAAH,QAAA,QAAyD;IAazDzC,EAAA,CAAAO,SAAA,GAAmD;IAAnDP,EAAA,CAAAI,UAAA,UAAAyC,OAAA,GAAAV,MAAA,CAAAE,WAAA,CAAAG,GAAA,0BAAAK,OAAA,CAAAJ,QAAA,aAAmD;IAGnDzC,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAI,UAAA,UAAA0C,OAAA,GAAAX,MAAA,CAAAE,WAAA,CAAAG,GAAA,0BAAAM,OAAA,CAAAL,QAAA,QAA8C;IAG9CzC,EAAA,CAAAO,SAAA,EAA8C;IAA9CP,EAAA,CAAAI,UAAA,UAAA2C,OAAA,GAAAZ,MAAA,CAAAE,WAAA,CAAAG,GAAA,0BAAAO,OAAA,CAAAN,QAAA,QAA8C;IAU5BzC,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAI,UAAA,UAAAJ,EAAA,CAAAgD,WAAA,SAAAb,MAAA,CAAAc,OAAA,CAAAC,eAAA,WAAkD;IAaxElD,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAI,UAAA,cAAA+B,MAAA,CAAAE,WAAA,CAAAc,KAAA,CAAgC;IAEtCnD,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,MAAA2B,MAAA,CAAAiB,MAAA,yCACF;;;;;;IAaZpD,EAAA,CAAAC,cAAA,cAA6B;IAIUD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACrDH,EAAA,CAAAC,cAAA,aAAyB;IACvBD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,gBAAyG;IAAjFD,EAAA,CAAAU,UAAA,mBAAA2C,oEAAA;MAAArD,EAAA,CAAAY,aAAA,CAAA0C,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAuC,OAAA,CAAAtC,SAAA,CAAAC,KAAA,EAAiB;IAAA,EAAC;IACjDlB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAG9BH,EAAA,CAAAC,cAAA,cAAwB;IAKND,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACzB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACjC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC5B;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAERH,EAAA,CAAAC,cAAA,eAAsB;IAEVD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IACxB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAyB;IACfD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,IAC7B;;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAGVH,EAAA,CAAAC,cAAA,eAAiB;IAGHD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC7BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAIpCH,EAAA,CAAAC,cAAA,eAAiB;IAE6BD,EAAA,CAAAU,UAAA,mBAAA8C,qEAAA;MAAAxD,EAAA,CAAAY,aAAA,CAAA0C,IAAA;MAAA,MAAAG,OAAA,GAAAzD,EAAA,CAAAe,aAAA;MAAA,OAASf,EAAA,CAAAgB,WAAA,CAAAyC,OAAA,CAAAvB,SAAA,EAAW;IAAA,EAAC;IAC7DlC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IA5CTH,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAAQ,kBAAA,+BAAAkD,MAAA,CAAAT,OAAA,CAAAU,IAAA,MACF;IAY2B3D,EAAA,CAAAO,SAAA,IACzB;IADyBP,EAAA,CAAAQ,kBAAA,MAAAkD,MAAA,CAAAT,OAAA,CAAAU,IAAA,MACzB;IAEiC3D,EAAA,CAAAO,SAAA,GACjC;IADiCP,EAAA,CAAAQ,kBAAA,MAAAkD,MAAA,CAAAT,OAAA,CAAAW,QAAA,2BACjC;IAE4B5D,EAAA,CAAAO,SAAA,GAC5B;IAD4BP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA6D,WAAA,QAAAH,MAAA,CAAAT,OAAA,CAAAa,cAAA,iCAC5B;IAIwB9D,EAAA,CAAAO,SAAA,GACxB;IADwBP,EAAA,CAAAQ,kBAAA,MAAAkD,MAAA,CAAAT,OAAA,CAAAc,GAAA,OACxB;IAE6B/D,EAAA,CAAAO,SAAA,GAC7B;IAD6BP,EAAA,CAAAQ,kBAAA,MAAAR,EAAA,CAAA6D,WAAA,SAAAH,MAAA,CAAAT,OAAA,CAAAC,eAAA,iCAC7B;IAOKlD,EAAA,CAAAO,SAAA,GAAuB;IAAvBP,EAAA,CAAAgE,iBAAA,CAAAN,MAAA,CAAAT,OAAA,CAAAgB,WAAA,CAAuB;;;ADhIxC,OAAM,MAAOC,oBAAoB;EAiB/BC,YACSlD,SAA6C,EACpBmD,IAAgB,EACzCC,cAA8B,EAC7BC,EAAsB;IAHvB,KAAArD,SAAS,GAATA,SAAS;IACgB,KAAAmD,IAAI,GAAJA,IAAI;IAC7B,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,EAAE,GAAFA,EAAE;IAlBZ,KAAAC,SAAS,GAAG,KAAK;IAIjB;IACA,KAAAjC,aAAa,GAAG,CACd;MAAEhC,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,SAAS;MAAEG,KAAK,EAAE;IAAS,CAAE,EACtC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEH,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEH,KAAK,EAAE,WAAW;MAAEG,KAAK,EAAE;IAAW,CAAE,EAC1C;MAAEH,KAAK,EAAE,cAAc;MAAEG,KAAK,EAAE;IAAc,CAAE,CACjD;IA2BD,KAAA+D,WAAW,GAAG,IAAIpF,kBAAkB,CAAC,EAAE,EAAE,CACvCC,UAAU,CAACoF,QAAQ,CACpB,CAAC;IArBA;IACA,IAAI,CAACrB,MAAM,GAAGgB,IAAI,CAAChB,MAAM;IACzB,IAAI,IAAI,CAACA,MAAM,KAAK,MAAM,EAAE;MAC1B,IAAI,CAACmB,SAAS,GAAG,KAAK;MACtB,IAAI,CAACnC,WAAW,GAAG,aAAagC,IAAI,CAACnB,OAAO,CAACU,IAAI,EAAE;MACnD,IAAI,CAACV,OAAO,GAAG,IAAIzD,YAAY,CAAC4E,IAAI,CAACnB,OAAO,CAAC;MAC7C,IAAI,CAACZ,WAAW,GAAG,IAAI,CAACqC,iBAAiB,EAAE;KAC5C,MAAM,IAAI,IAAI,CAACtB,MAAM,KAAK,SAAS,EAAE;MACpC,IAAI,CAACH,OAAO,GAAG,IAAIzD,YAAY,CAAC4E,IAAI,CAACnB,OAAO,CAAC;MAC7C,IAAI,CAACsB,SAAS,GAAG,IAAI;KACtB,MAAM;MACL,IAAI,CAACA,SAAS,GAAG,KAAK;MACtB,IAAI,CAACnC,WAAW,GAAG,iBAAiB;MACpC,MAAMuC,WAAW,GAAG,EAAa;MACjC,IAAI,CAAC1B,OAAO,GAAG,IAAIzD,YAAY,CAACmF,WAAW,CAAC;MAC5C,IAAI,CAACtC,WAAW,GAAG,IAAI,CAACqC,iBAAiB,EAAE;;EAE/C;EAMAE,eAAeA,CAAA;IACb,OAAO,IAAI,CAACJ,WAAW,CAAC/B,QAAQ,CAAC,UAAU,CAAC,GACxC,cAAc,GACd,IAAI,CAAC+B,WAAW,CAAC/B,QAAQ,CAAC,KAAK,CAAC,GAChC,8BAA8B,GAC9B,EAAE;EACR;EAEAiC,iBAAiBA,CAAA;IACf,MAAMG,IAAI,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MACzBC,EAAE,EAAE,CAAC,IAAI,CAAC9B,OAAO,CAAC8B,EAAE,CAAC;MACrBpB,IAAI,EAAE,CAAC,IAAI,CAACV,OAAO,CAACU,IAAI,EAAE,CAACtE,UAAU,CAACoF,QAAQ,CAAC,CAAC;MAChDR,WAAW,EAAE,CAAC,IAAI,CAAChB,OAAO,CAACgB,WAAW,EAAE,CAAC5E,UAAU,CAACoF,QAAQ,CAAC,CAAC;MAC9DX,cAAc,EAAE,CAAC,IAAI,CAACb,OAAO,CAACa,cAAc,EAAE,CAACzE,UAAU,CAACoF,QAAQ,EAAEpF,UAAU,CAAC2F,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MACvFjB,GAAG,EAAE,CAAC,IAAI,CAACd,OAAO,CAACc,GAAG,EAAE,CAAC1E,UAAU,CAACoF,QAAQ,EAAEpF,UAAU,CAAC2F,GAAG,CAAC,CAAC,CAAC,EAAE3F,UAAU,CAAC4F,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;MACtFrB,QAAQ,EAAE,CAAC,IAAI,CAACX,OAAO,CAACW,QAAQ;KACjC,CAAC;IAEF;IACAiB,IAAI,CAACrC,GAAG,CAAC,gBAAgB,CAAC,EAAE0C,YAAY,CAACC,SAAS,CAAC,MAAK;MACtD,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IAEFP,IAAI,CAACrC,GAAG,CAAC,KAAK,CAAC,EAAE0C,YAAY,CAACC,SAAS,CAAC,MAAK;MAC3C,IAAI,CAACC,YAAY,EAAE;IACrB,CAAC,CAAC;IAEF,OAAOP,IAAI;EACb;EAEAO,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC/C,WAAW,EAAE;MACpB,MAAMgD,MAAM,GAAG,IAAI,CAAChD,WAAW,CAACG,GAAG,CAAC,gBAAgB,CAAC,EAAElC,KAAK,IAAI,CAAC;MACjE,MAAMyD,GAAG,GAAG,IAAI,CAAC1B,WAAW,CAACG,GAAG,CAAC,KAAK,CAAC,EAAElC,KAAK,IAAI,CAAC;MACnD,MAAMgF,OAAO,GAAG,IAAI,CAACjB,cAAc,CAACe,YAAY,CAACC,MAAM,EAAEtB,GAAG,CAAC;MAC7D,IAAI,CAACd,OAAO,CAACC,eAAe,GAAGoC,OAAO;;EAE1C;EAEAjE,MAAMA,CAAA;IACJ,IAAI,IAAI,CAACgB,WAAW,EAAEc,KAAK,EAAE;MAC3B,MAAMoC,SAAS,GAAG,IAAI,CAAClD,WAAW,CAACmD,WAAW,EAAE;MAEhD,IAAI,IAAI,CAACpC,MAAM,KAAK,KAAK,EAAE;QACzB,IAAI,CAACqC,UAAU,EAAE;OAClB,MAAM,IAAI,IAAI,CAACrC,MAAM,KAAK,MAAM,EAAE;QACjC,IAAI,CAACsC,WAAW,EAAE;;;EAGxB;EAEAxD,SAASA,CAAA;IACP,IAAI,CAACjB,SAAS,CAACC,KAAK,EAAE;EACxB;EAEOuE,UAAUA,CAAA;IACf,IAAI,IAAI,CAACpD,WAAW,EAAEc,KAAK,EAAE;MAC3B,MAAMoC,SAAS,GAAG,IAAI,CAAClD,WAAW,CAACmD,WAAW,EAAE;MAChD,MAAMG,SAAS,GAAqB;QAClChC,IAAI,EAAE4B,SAAS,CAAC5B,IAAI;QACpBM,WAAW,EAAEsB,SAAS,CAACtB,WAAW;QAClCH,cAAc,EAAEyB,SAAS,CAACzB,cAAc;QACxCC,GAAG,EAAEwB,SAAS,CAACxB,GAAG;QAClBH,QAAQ,EAAE2B,SAAS,CAAC3B;OACrB;MAED,IAAI,CAACS,cAAc,CAACuB,aAAa,CAACD,SAAS,CAAC,CAACR,SAAS,CAAC;QACrDU,IAAI,EAAGC,MAAM,IAAI;UACf,IAAI,CAAC7E,SAAS,CAACC,KAAK,CAAC4E,MAAM,CAAC;QAC9B,CAAC;QACDC,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAChE;OACD,CAAC;;EAEN;EAEOL,WAAWA,CAAA;IAChB,IAAI,IAAI,CAACrD,WAAW,EAAEc,KAAK,EAAE;MAC3B,MAAMoC,SAAS,GAAG,IAAI,CAAClD,WAAW,CAACmD,WAAW,EAAE;MAChD,MAAMS,SAAS,GAAqB;QAClCtC,IAAI,EAAE4B,SAAS,CAAC5B,IAAI;QACpBM,WAAW,EAAEsB,SAAS,CAACtB,WAAW;QAClCH,cAAc,EAAEyB,SAAS,CAACzB,cAAc;QACxCC,GAAG,EAAEwB,SAAS,CAACxB,GAAG;QAClBH,QAAQ,EAAE2B,SAAS,CAAC3B;OACrB;MAED,IAAI,CAACS,cAAc,CAAC6B,aAAa,CAAC,IAAI,CAACjD,OAAO,CAAC8B,EAAE,EAAEkB,SAAS,CAAC,CAACd,SAAS,CAAC;QACtEU,IAAI,EAAEA,CAAA,KAAK;UACT,IAAI,CAAC5E,SAAS,CAACC,KAAK,CAAC,IAAI,CAAC;QAC5B,CAAC;QACD6E,KAAK,EAAGA,KAAK,IAAI;UACfC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QACpE;OACD,CAAC;;EAEN;EAAC,QAAAI,CAAA,G;qBA/IUjC,oBAAoB,EAAAlE,EAAA,CAAAoG,iBAAA,CAAAC,EAAA,CAAAC,YAAA,GAAAtG,EAAA,CAAAoG,iBAAA,CAmBrBjH,eAAe,GAAAa,EAAA,CAAAoG,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAAxG,EAAA,CAAAoG,iBAAA,CAAAK,EAAA,CAAAC,kBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAnBdzC,oBAAoB;IAAA0C,SAAA;IAAAC,UAAA;IAAAC,QAAA,GAAA9G,EAAA,CAAA+G,mBAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCpCjCrH,EAAA,CAAAC,cAAA,aAA0B;QACxBD,EAAA,CAAAsB,UAAA,IAAAiG,2CAAA,mBAwHC,IAAAC,2CAAA;QA2DHxH,EAAA,CAAAG,YAAA,EAAM;;;QAnLJH,EAAA,CAAAO,SAAA,EAwHC;QAxHDP,EAAA,CAAAyH,aAAA,KAAAH,GAAA,CAAA/C,SAAA,SAwHC;;;mBDhGC1E,eAAe,EAAA6H,EAAA,CAAAC,SAAA,EAAAD,EAAA,CAAAE,aAAA,EACfhI,aAAa,EAAAiI,EAAA,CAAAC,OAAA,EACbxI,WAAW,EAAAmH,EAAA,CAAAsB,aAAA,EAAAtB,EAAA,CAAAuB,oBAAA,EAAAvB,EAAA,CAAAwB,mBAAA,EAAAxB,EAAA,CAAAyB,eAAA,EAAAzB,EAAA,CAAA0B,oBAAA,EAAA1B,EAAA,CAAA2B,iBAAA,EAAA3B,EAAA,CAAA4B,YAAA,EAAA5B,EAAA,CAAA6B,YAAA,EACX/I,mBAAmB,EAAAkH,EAAA,CAAA8B,kBAAA,EAAA9B,EAAA,CAAA+B,eAAA,EACnB7I,kBAAkB,EAAA8I,EAAA,CAAAC,YAAA,EAAAD,EAAA,CAAAE,QAAA,EAAAF,EAAA,CAAAG,OAAA,EAAAH,EAAA,CAAAI,QAAA,EAAAJ,EAAA,CAAAK,SAAA,EAClBpJ,cAAc,EAAAqJ,EAAA,CAAAC,QAAA,EACdlJ,eAAe,EAAAmJ,EAAA,CAAAC,SAAA,EAAAC,EAAA,CAAAC,SAAA,EACf3J,aAAa,EACbM,YAAY,EAAAsJ,GAAA,CAAAC,OAAA,EAAAD,GAAA,CAAAE,IAAA,EAAAF,GAAA,CAAAG,WAAA,EAAAH,GAAA,CAAAI,YAAA;IAAAC,MAAA;EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}