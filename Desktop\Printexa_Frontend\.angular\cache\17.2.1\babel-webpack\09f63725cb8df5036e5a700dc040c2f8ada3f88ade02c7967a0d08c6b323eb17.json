{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n      console.log('Clients récupérés du serveur:', clients?.length || 0);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      console.error('Erreur lors de la récupération des clients:', error);\n      // En cas d'erreur de connexion, utiliser des données de test\n      if (error.status === 0) {\n        console.log('Serveur non disponible, utilisation de données de test');\n        const testClients = [{\n          id: '1',\n          code: 'CLI001',\n          syntax: 'Client Test 1',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '12345678'\n        }, {\n          id: '2',\n          code: 'CLI002',\n          syntax: 'Client Test 2',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '87654321'\n        }];\n        this.dataChange.next(testClients);\n        return new Observable(observer => {\n          observer.next(testClients);\n          observer.complete();\n        });\n      }\n      // Pour les autres erreurs, mettre un tableau vide\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    // Validation des données d'entrée\n    if (!id || id.trim() === '') {\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\n    }\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === id);\n      if (clientByCode && clientByCode.id !== id) {\n        console.log('ID trouvé par code:', clientByCode.id);\n        actualId = clientByCode.id;\n      }\n    }\n    // Nettoyer et valider les données\n    const cleanedClient = {};\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\n    const clientData = {\n      ...client\n    };\n    if ('id' in clientData) {\n      delete clientData.id;\n    }\n    // Ajouter seulement les champs non vides et valides\n    if (clientData.code && clientData.code.trim()) {\n      cleanedClient.code = clientData.code.trim();\n      // Validation du code\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\n      }\n    }\n    if (clientData.syntax && clientData.syntax.trim()) {\n      cleanedClient.syntax = clientData.syntax.trim();\n      if (cleanedClient.syntax.length > 100) {\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\n      cleanedClient.matFiscal = clientData.matFiscal.trim().toUpperCase();\n      // Validation du matricule fiscal\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n      if (!matFiscalPattern.test(cleanedClient.matFiscal)) {\n        return throwError(() => new Error('Format de matricule fiscal invalide'));\n      }\n    }\n    if (clientData.email && clientData.email.trim()) {\n      cleanedClient.email = clientData.email.trim().toLowerCase();\n      // Validation de l'email\n      if (!this.validateEmail(cleanedClient.email)) {\n        return throwError(() => new Error('Format d\\'email invalide'));\n      }\n      if (cleanedClient.email.length > 100) {\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (clientData.telephone && clientData.telephone.trim()) {\n      cleanedClient.telephone = clientData.telephone.trim();\n      // Validation du téléphone\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(cleanedClient.telephone)) {\n        return throwError(() => new Error('Format de téléphone invalide'));\n      }\n    }\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\n    }\n    // Validation stricte côté client pour éviter les erreurs serveur\n    const validationResult = this.validateUpdateData(cleanedClient, actualId);\n    if (!validationResult.isValid) {\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\n    }\n    console.log('=== MISE À JOUR CLIENT ===');\n    console.log('ID original:', id);\n    console.log('ID à utiliser:', actualId);\n    console.log('Type de l\\'ID:', typeof actualId);\n    console.log('Longueur de l\\'ID:', actualId.length);\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\n    console.log('Données originales:', client);\n    console.log('Données après suppression ID:', clientData);\n    console.log('Données nettoyées à envoyer:', cleanedClient);\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\n    console.log('Headers:', this.getHeaders());\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\n    console.log('========================');\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Si le serveur retourne NoContent (204), créer le client mis à jour localement\n      if (response.status === 204 || !response.body) {\n        const currentData = this.dataChange.value;\n        // Chercher par l'ID original ou l'ID actuel\n        const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\n        if (!existingClient) {\n          throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\n        }\n        // Créer le client mis à jour en combinant les données existantes et les nouvelles\n        const updatedClient = {\n          ...existingClient,\n          ...cleanedClient\n        };\n        return updatedClient;\n      }\n      // Si le serveur retourne le client mis à jour\n      const updatedClient = response.body;\n      return updatedClient;\n    }), tap(updatedClient => {\n      console.log('Client mis à jour avec succès:', updatedClient);\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n      // Mettre à jour le client courant si c'est le même\n      if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\n        this.currentClientSubject.next(updatedClient);\n        localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n      }\n    }), catchError(error => {\n      console.error('Erreur lors de la mise à jour:', error);\n      // Si le serveur n'est pas disponible, simuler la mise à jour localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la mise à jour...');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      // TEMPORAIRE: Désactiver la simulation pour voir l'erreur réelle\n      // const errorMessage = error.error?.message || error.error?.title || '';\n      // if (error.status === 400 && errorMessage.includes('validation errors')) {\n      //   console.warn('Erreur de validation serveur persistante, simulation locale...');\n      //   return this.simulateLocalUpdate(actualId, cleanedClient);\n      // }\n      console.error('ERREUR 400 - PAS DE SIMULATION, ERREUR RÉELLE:');\n      console.error('Détails complets de l\\'erreur:', error);\n      return this.handleUpdateError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    console.log('Tentative de suppression des clients:', ids);\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log('Suppression en masse réussie');\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const updatedData = currentData.filter(client => !ids.includes(client.id));\n      this.dataChange.next(updatedData);\n    }), catchError(error => {\n      console.error('Erreur lors de la suppression en masse:', error);\n      // Si le serveur n'est pas disponible, simuler la suppression localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la suppression...');\n        return this.simulateLocalDeletion(ids);\n      }\n      // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\n      if (error.status === 500 || error.status === 404) {\n        console.log('Tentative de suppression individuelle...');\n        return this.deleteClientsIndividually(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Méthode de fallback pour supprimer les clients individuellement\n  deleteClientsIndividually(ids) {\n    const deleteRequests = ids.map(id => this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error(`Erreur lors de la suppression du client ${id}:`, error);\n      return throwError(() => error);\n    })));\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\n    return new Observable(observer => {\n      let completedCount = 0;\n      let hasError = false;\n      const errors = [];\n      deleteRequests.forEach((request, index) => {\n        request.subscribe({\n          next: () => {\n            completedCount++;\n            if (completedCount === ids.length && !hasError) {\n              // Mettre à jour les données locales\n              const currentData = this.dataChange.value;\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\n              this.dataChange.next(updatedData);\n              observer.next({\n                deletedCount: completedCount\n              });\n              observer.complete();\n            }\n          },\n          error: error => {\n            hasError = true;\n            errors.push({\n              id: ids[index],\n              error\n            });\n            if (completedCount + errors.length === ids.length) {\n              observer.error({\n                message: 'Certains clients n\\'ont pas pu être supprimés',\n                errors,\n                deletedCount: completedCount\n              });\n            }\n          }\n        });\n      });\n    });\n  }\n  // Simuler la suppression locale quand le serveur n'est pas disponible\n  simulateLocalDeletion(ids) {\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\n    // Mettre à jour les données locales\n    const currentData = this.dataChange.value;\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\n    this.dataChange.next(updatedData);\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length,\n          message: 'Suppression simulée localement (serveur non disponible)'\n        });\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\n  simulateLocalUpdate(id, updateData) {\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\n    // Trouver et mettre à jour le client dans les données locales\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\n    }\n    // Créer le client mis à jour\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    // Mettre à jour les données locales\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    // Mettre à jour le client courant si c'est le même\n    if (this.currentClientSubject.value?.id === id) {\n      this.currentClientSubject.next(updatedClient);\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n    }\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Méthode de test pour diagnostiquer les problèmes d'ID\n  testClientId(id) {\n    console.log('=== TEST ID CLIENT ===');\n    console.log('ID fourni:', id);\n    console.log('Type:', typeof id);\n    console.log('Longueur:', id.length);\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\n    const currentData = this.dataChange.value;\n    const clientById = currentData.find(c => c.id === id);\n    const clientByCode = currentData.find(c => c.code === id);\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\n    if (clientByCode) {\n      console.log('Client par code:', clientByCode);\n    }\n    console.log('===================');\n  }\n  // Méthode de test pour valider une mise à jour sans l'envoyer\n  testUpdateValidation(id, updateData) {\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\n    console.log('ID:', id);\n    console.log('Données:', updateData);\n    const validation = this.validateUpdateData(updateData, id);\n    console.log('Validation réussie:', validation.isValid);\n    if (!validation.isValid) {\n      console.log('Erreurs:', validation.errors);\n    }\n    console.log('Données nettoyées qui seraient envoyées:');\n    const cleaned = {};\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\n    console.log('Cleaned data:', cleaned);\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\n    console.log('================================');\n  }\n  // Validation stricte pour la mise à jour\n  validateUpdateData(client, id) {\n    const errors = [];\n    // Validation de l'ID\n    if (!id || id.trim() === '') {\n      errors.push('ID requis');\n    } else {\n      const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n      if (!isGuid) {\n        errors.push('ID doit être un GUID valide');\n      }\n    }\n    // Validation des champs\n    if (client.code !== undefined) {\n      if (!client.code || client.code.trim() === '') {\n        errors.push('Code ne peut pas être vide');\n      } else if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Code doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\n      if (client.matFiscal.trim() !== '') {\n        const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n        if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {\n          errors.push('Format de matricule fiscal invalide');\n        }\n      }\n    }\n    if (client.email !== undefined && client.email !== null) {\n      if (client.email.trim() !== '') {\n        if (!this.validateEmail(client.email)) {\n          errors.push('Format d\\'email invalide');\n        }\n        if (client.email.length > 100) {\n          errors.push('Email ne peut pas dépasser 100 caractères');\n        }\n      }\n    }\n    if (client.telephone !== undefined && client.telephone !== null) {\n      if (client.telephone.trim() !== '') {\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n        if (!phonePattern.test(client.telephone)) {\n          errors.push('Format de téléphone invalide');\n        }\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Méthode publique pour tester une mise à jour complète\n  debugUpdate(clientId, updateData) {\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\n    // Test 1: Validation de l'ID\n    this.testClientId(clientId);\n    // Test 2: Validation des données\n    this.testUpdateValidation(clientId, updateData);\n    // Test 3: Simulation de la requête\n    console.log('--- Simulation de la requête ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\n    let actualId = clientId;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === clientId);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Méthode: PUT');\n    console.log('Headers:', this.getHeaders());\n    const cleanedData = {};\n    if (updateData.code) cleanedData.code = updateData.code.trim();\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email) cleanedData.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\n    console.log('================================');\n  }\n  // Méthode pour tester la requête HTTP brute\n  testRawHttpRequest(clientId, updateData) {\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\n    const url = `${this.baseUrl}${clientId}`;\n    const headers = this.getHeaders();\n    console.log('URL:', url);\n    console.log('Headers:', headers);\n    console.log('Body:', JSON.stringify(updateData, null, 2));\n    return this.http.put(url, updateData, {\n      headers: headers,\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('SUCCÈS - Response:', response);\n    }), catchError(error => {\n      console.error('ÉCHEC - Error détaillé:', error);\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      // Retourner l'erreur pour que l'appelant puisse la voir\n      return throwError(() => error);\n    }));\n  }\n  // Test avec des données minimales pour identifier le problème\n  testMinimalUpdate(clientId) {\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\n    // Test 1: Seulement le code\n    const minimalData1 = {\n      code: 'TEST001'\n    };\n    console.log('Test 1 - Seulement code:', minimalData1);\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(catchError(error1 => {\n      console.log('Test 1 échoué, essai test 2...');\n      // Test 2: Seulement la syntax\n      const minimalData2 = {\n        syntax: 'Test Client'\n      };\n      console.log('Test 2 - Seulement syntax:', minimalData2);\n      return this.testRawHttpRequest(clientId, minimalData2).pipe(catchError(error2 => {\n        console.log('Test 2 échoué, essai test 3...');\n        // Test 3: Objet vide\n        const minimalData3 = {};\n        console.log('Test 3 - Objet vide:', minimalData3);\n        return this.testRawHttpRequest(clientId, minimalData3);\n      }));\n    }));\n  }\n  // Méthode utilitaire pour tester la validation des données client\n  validateClientData(client) {\n    const errors = [];\n    if (client.code) {\n      if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax && client.syntax.length > 100) {\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal) {\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n      if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {\n        errors.push('Format de matricule fiscal invalide');\n      }\n    }\n    if (client.email) {\n      if (!this.validateEmail(client.email)) {\n        errors.push('Format d\\'email invalide');\n      }\n      if (client.email.length > 100) {\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\n      }\n    }\n    if (client.telephone) {\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(client.telephone)) {\n        errors.push('Format de téléphone invalide');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('=== ERREUR DE MISE À JOUR ===');\n    console.error('Status:', error.status);\n    console.error('StatusText:', error.statusText);\n    console.error('URL:', error.url);\n    console.error('Error object:', error.error);\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\n    console.error('Full error:', error);\n    console.error('============================');\n    // Analyser la structure de l'erreur en détail\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    const traceId = error.error?.traceId;\n    const type = error.error?.type;\n    console.log('API Error:', apiError);\n    console.log('Validation Errors:', validationErrors);\n    console.log('Validation Errors type:', typeof validationErrors);\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\n    console.log('Trace ID:', traceId);\n    console.log('Error Type:', type);\n    // Essayer de capturer d'autres propriétés d'erreur\n    if (error.error) {\n      console.log('Toutes les propriétés de error.error:');\n      for (const key in error.error) {\n        console.log(`  ${key}:`, error.error[key]);\n      }\n    }\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "console", "log", "length", "error", "status", "testClients", "id", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "observer", "complete", "handleError", "getClientById", "client", "setItem", "stringify", "createClient", "Error", "trim", "validateEmail", "generateGuid", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "isGuid", "test", "isCode", "actualId", "clientByCode", "find", "c", "cleanedClient", "toUpperCase", "matFiscalPattern", "toLowerCase", "phonePattern", "Object", "keys", "validationResult", "validateUpdateData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "join", "put", "existingClient", "updatedClient", "index", "findIndex", "simulateLocalUpdate", "handleUpdateError", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "updatedData", "filter", "includes", "simulateLocalDeletion", "deleteClientsIndividually", "deleteRequests", "completedCount", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "request", "subscribe", "deletedCount", "push", "message", "setTimeout", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "re", "testClientId", "clientById", "testUpdateValidation", "validation", "cleaned", "undefined", "debugUpdate", "clientId", "cleanedData", "testRawHttpRequest", "url", "testMinimalUpdate", "minimalData1", "error1", "minimalData2", "error2", "minimalData3", "validateClientData", "errorMessage", "statusText", "apiError", "title", "validationErrors", "traceId", "type", "key", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n        console.log('Clients récupérés du serveur:', clients?.length || 0);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        console.error('Erreur lors de la récupération des clients:', error);\r\n\r\n        // En cas d'erreur de connexion, utiliser des données de test\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, utilisation de données de test');\r\n          const testClients: Client[] = [\r\n            {\r\n              id: '1',\r\n              code: 'CLI001',\r\n              syntax: 'Client Test 1',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '12345678'\r\n            },\r\n            {\r\n              id: '2',\r\n              code: 'CLI002',\r\n              syntax: 'Client Test 2',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '87654321'\r\n            }\r\n          ];\r\n          this.dataChange.next(testClients);\r\n          return new Observable<Client[]>(observer => {\r\n            observer.next(testClients);\r\n            observer.complete();\r\n          });\r\n        }\r\n\r\n        // Pour les autres erreurs, mettre un tableau vide\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!id || id.trim() === '') {\r\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\r\n    }\r\n\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    // Vérifier le format de l'ID et essayer de trouver le bon ID si nécessaire\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    console.log('Format ID - GUID:', isGuid, 'Code:', isCode);\r\n\r\n    // Si l'ID est un code client, essayer de trouver le GUID correspondant\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === id);\r\n      if (clientByCode && clientByCode.id !== id) {\r\n        console.log('ID trouvé par code:', clientByCode.id);\r\n        actualId = clientByCode.id;\r\n      }\r\n    }\r\n\r\n    // Nettoyer et valider les données\r\n    const cleanedClient: UpdateClientDto = {};\r\n\r\n    // S'assurer que l'id n'est jamais inclus dans les données à envoyer\r\n    const clientData = { ...client };\r\n    if ('id' in clientData) {\r\n      delete (clientData as any).id;\r\n    }\r\n\r\n    // Ajouter seulement les champs non vides et valides\r\n    if (clientData.code && clientData.code.trim()) {\r\n      cleanedClient.code = clientData.code.trim();\r\n      // Validation du code\r\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\r\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.syntax && clientData.syntax.trim()) {\r\n      cleanedClient.syntax = clientData.syntax.trim();\r\n      if (cleanedClient.syntax.length > 100) {\r\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.matFiscal && clientData.matFiscal.trim()) {\r\n      cleanedClient.matFiscal = clientData.matFiscal.trim().toUpperCase();\r\n      // Validation du matricule fiscal\r\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\r\n      if (!matFiscalPattern.test(cleanedClient.matFiscal)) {\r\n        return throwError(() => new Error('Format de matricule fiscal invalide'));\r\n      }\r\n    }\r\n\r\n    if (clientData.email && clientData.email.trim()) {\r\n      cleanedClient.email = clientData.email.trim().toLowerCase();\r\n      // Validation de l'email\r\n      if (!this.validateEmail(cleanedClient.email)) {\r\n        return throwError(() => new Error('Format d\\'email invalide'));\r\n      }\r\n      if (cleanedClient.email.length > 100) {\r\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (clientData.telephone && clientData.telephone.trim()) {\r\n      cleanedClient.telephone = clientData.telephone.trim();\r\n      // Validation du téléphone\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(cleanedClient.telephone)) {\r\n        return throwError(() => new Error('Format de téléphone invalide'));\r\n      }\r\n    }\r\n\r\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\r\n    }\r\n\r\n    // Validation stricte côté client pour éviter les erreurs serveur\r\n    const validationResult = this.validateUpdateData(cleanedClient, actualId);\r\n    if (!validationResult.isValid) {\r\n      return throwError(() => new Error(`Validation échouée: ${validationResult.errors.join(', ')}`));\r\n    }\r\n\r\n    console.log('=== MISE À JOUR CLIENT ===');\r\n    console.log('ID original:', id);\r\n    console.log('ID à utiliser:', actualId);\r\n    console.log('Type de l\\'ID:', typeof actualId);\r\n    console.log('Longueur de l\\'ID:', actualId.length);\r\n    console.log('ID est un GUID?', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId));\r\n    console.log('ID est un code?', /^[A-Z]{3}-\\d{3}$/.test(actualId));\r\n    console.log('Données originales:', client);\r\n    console.log('Données après suppression ID:', clientData);\r\n    console.log('Données nettoyées à envoyer:', cleanedClient);\r\n    console.log('Contient un ID dans le body?', 'id' in cleanedClient ? 'OUI - ERREUR!' : 'NON - OK');\r\n    console.log('URL complète:', `${this.baseUrl}${actualId}`);\r\n    console.log('Headers:', this.getHeaders());\r\n    console.log('Body JSON:', JSON.stringify(cleanedClient, null, 2));\r\n    console.log('========================');\r\n\r\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Si le serveur retourne NoContent (204), créer le client mis à jour localement\r\n        if (response.status === 204 || !response.body) {\r\n          const currentData = this.dataChange.value;\r\n          // Chercher par l'ID original ou l'ID actuel\r\n          const existingClient = currentData.find(c => c.id === actualId || c.id === id || c.code === id);\r\n          if (!existingClient) {\r\n            throw new Error(`Client non trouvé dans les données locales (ID: ${actualId}, original: ${id})`);\r\n          }\r\n\r\n          // Créer le client mis à jour en combinant les données existantes et les nouvelles\r\n          const updatedClient: Client = {\r\n            ...existingClient,\r\n            ...cleanedClient\r\n          };\r\n          return updatedClient;\r\n        }\r\n\r\n        // Si le serveur retourne le client mis à jour\r\n        const updatedClient: Client = response.body;\r\n        return updatedClient;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        console.log('Client mis à jour avec succès:', updatedClient);\r\n\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === actualId || c.id === id || c.code === id);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n\r\n        // Mettre à jour le client courant si c'est le même\r\n        if (this.currentClientSubject.value?.id === actualId || this.currentClientSubject.value?.id === id) {\r\n          this.currentClientSubject.next(updatedClient);\r\n          localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la mise à jour:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la mise à jour localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la mise à jour...');\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n\r\n        // TEMPORAIRE: Désactiver la simulation pour voir l'erreur réelle\r\n        // const errorMessage = error.error?.message || error.error?.title || '';\r\n        // if (error.status === 400 && errorMessage.includes('validation errors')) {\r\n        //   console.warn('Erreur de validation serveur persistante, simulation locale...');\r\n        //   return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        // }\r\n\r\n        console.error('ERREUR 400 - PAS DE SIMULATION, ERREUR RÉELLE:');\r\n        console.error('Détails complets de l\\'erreur:', error);\r\n\r\n        return this.handleUpdateError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    console.log('Tentative de suppression des clients:', ids);\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log('Suppression en masse réussie');\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n        this.dataChange.next(updatedData);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la suppression en masse:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la suppression localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la suppression...');\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n\r\n        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\r\n        if (error.status === 500 || error.status === 404) {\r\n          console.log('Tentative de suppression individuelle...');\r\n          return this.deleteClientsIndividually(ids);\r\n        }\r\n\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode de fallback pour supprimer les clients individuellement\r\n  private deleteClientsIndividually(ids: string[]): Observable<any> {\r\n    const deleteRequests = ids.map(id =>\r\n      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n        catchError(error => {\r\n          console.error(`Erreur lors de la suppression du client ${id}:`, error);\r\n          return throwError(() => error);\r\n        })\r\n      )\r\n    );\r\n\r\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\r\n    return new Observable(observer => {\r\n      let completedCount = 0;\r\n      let hasError = false;\r\n      const errors: any[] = [];\r\n\r\n      deleteRequests.forEach((request, index) => {\r\n        request.subscribe({\r\n          next: () => {\r\n            completedCount++;\r\n            if (completedCount === ids.length && !hasError) {\r\n              // Mettre à jour les données locales\r\n              const currentData = this.dataChange.value;\r\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n              this.dataChange.next(updatedData);\r\n              observer.next({ deletedCount: completedCount });\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            hasError = true;\r\n            errors.push({ id: ids[index], error });\r\n            if (completedCount + errors.length === ids.length) {\r\n              observer.error({\r\n                message: 'Certains clients n\\'ont pas pu être supprimés',\r\n                errors,\r\n                deletedCount: completedCount\r\n              });\r\n            }\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  // Simuler la suppression locale quand le serveur n'est pas disponible\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\r\n\r\n    // Mettre à jour les données locales\r\n    const currentData = this.dataChange.value;\r\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n    this.dataChange.next(updatedData);\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({\r\n          deletedCount: ids.length,\r\n          message: 'Suppression simulée localement (serveur non disponible)'\r\n        });\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\r\n\r\n    // Trouver et mettre à jour le client dans les données locales\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\r\n    }\r\n\r\n    // Créer le client mis à jour\r\n    const updatedClient: Client = {\r\n      ...currentData[index],\r\n      ...updateData\r\n    };\r\n\r\n    // Mettre à jour les données locales\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    // Mettre à jour le client courant si c'est le même\r\n    if (this.currentClientSubject.value?.id === id) {\r\n      this.currentClientSubject.next(updatedClient);\r\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n    }\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Méthode de test pour diagnostiquer les problèmes d'ID\r\n  public testClientId(id: string): void {\r\n    console.log('=== TEST ID CLIENT ===');\r\n    console.log('ID fourni:', id);\r\n    console.log('Type:', typeof id);\r\n    console.log('Longueur:', id.length);\r\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\r\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\r\n\r\n    const currentData = this.dataChange.value;\r\n    const clientById = currentData.find(c => c.id === id);\r\n    const clientByCode = currentData.find(c => c.code === id);\r\n\r\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\r\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\r\n\r\n    if (clientByCode) {\r\n      console.log('Client par code:', clientByCode);\r\n    }\r\n    console.log('===================');\r\n  }\r\n\r\n  // Méthode de test pour valider une mise à jour sans l'envoyer\r\n  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {\r\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\r\n    console.log('ID:', id);\r\n    console.log('Données:', updateData);\r\n\r\n    const validation = this.validateUpdateData(updateData, id);\r\n    console.log('Validation réussie:', validation.isValid);\r\n    if (!validation.isValid) {\r\n      console.log('Erreurs:', validation.errors);\r\n    }\r\n\r\n    console.log('Données nettoyées qui seraient envoyées:');\r\n    const cleaned: UpdateClientDto = {};\r\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\r\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Cleaned data:', cleaned);\r\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Validation stricte pour la mise à jour\r\n  private validateUpdateData(client: UpdateClientDto, id: string): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // Validation de l'ID\r\n    if (!id || id.trim() === '') {\r\n      errors.push('ID requis');\r\n    } else {\r\n      const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n      if (!isGuid) {\r\n        errors.push('ID doit être un GUID valide');\r\n      }\r\n    }\r\n\r\n    // Validation des champs\r\n    if (client.code !== undefined) {\r\n      if (!client.code || client.code.trim() === '') {\r\n        errors.push('Code ne peut pas être vide');\r\n      } else if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Code doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\r\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\r\n      if (client.matFiscal.trim() !== '') {\r\n        const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\r\n        if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {\r\n          errors.push('Format de matricule fiscal invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.email !== undefined && client.email !== null) {\r\n      if (client.email.trim() !== '') {\r\n        if (!this.validateEmail(client.email)) {\r\n          errors.push('Format d\\'email invalide');\r\n        }\r\n        if (client.email.length > 100) {\r\n          errors.push('Email ne peut pas dépasser 100 caractères');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.telephone !== undefined && client.telephone !== null) {\r\n      if (client.telephone.trim() !== '') {\r\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n        if (!phonePattern.test(client.telephone)) {\r\n          errors.push('Format de téléphone invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Méthode publique pour tester une mise à jour complète\r\n  public debugUpdate(clientId: string, updateData: any): void {\r\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\r\n\r\n    // Test 1: Validation de l'ID\r\n    this.testClientId(clientId);\r\n\r\n    // Test 2: Validation des données\r\n    this.testUpdateValidation(clientId, updateData);\r\n\r\n    // Test 3: Simulation de la requête\r\n    console.log('--- Simulation de la requête ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\r\n\r\n    let actualId = clientId;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === clientId);\r\n      if (clientByCode) {\r\n        actualId = clientByCode.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\r\n    console.log('Méthode: PUT');\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const cleanedData: any = {};\r\n    if (updateData.code) cleanedData.code = updateData.code.trim();\r\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email) cleanedData.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\r\n    console.log('================================');\r\n  }\r\n\r\n  // Méthode pour tester la requête HTTP brute\r\n  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {\r\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\r\n\r\n    const url = `${this.baseUrl}${clientId}`;\r\n    const headers = this.getHeaders();\r\n\r\n    console.log('URL:', url);\r\n    console.log('Headers:', headers);\r\n    console.log('Body:', JSON.stringify(updateData, null, 2));\r\n\r\n    return this.http.put(url, updateData, {\r\n      headers: headers,\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('SUCCÈS - Response:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('ÉCHEC - Error détaillé:', error);\r\n        console.error('Status:', error.status);\r\n        console.error('Error body:', error.error);\r\n\r\n        // Retourner l'erreur pour que l'appelant puisse la voir\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Test avec des données minimales pour identifier le problème\r\n  public testMinimalUpdate(clientId: string): Observable<any> {\r\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\r\n\r\n    // Test 1: Seulement le code\r\n    const minimalData1 = { code: 'TEST001' };\r\n    console.log('Test 1 - Seulement code:', minimalData1);\r\n\r\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(\r\n      catchError(error1 => {\r\n        console.log('Test 1 échoué, essai test 2...');\r\n\r\n        // Test 2: Seulement la syntax\r\n        const minimalData2 = { syntax: 'Test Client' };\r\n        console.log('Test 2 - Seulement syntax:', minimalData2);\r\n\r\n        return this.testRawHttpRequest(clientId, minimalData2).pipe(\r\n          catchError(error2 => {\r\n            console.log('Test 2 échoué, essai test 3...');\r\n\r\n            // Test 3: Objet vide\r\n            const minimalData3 = {};\r\n            console.log('Test 3 - Objet vide:', minimalData3);\r\n\r\n            return this.testRawHttpRequest(clientId, minimalData3);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode utilitaire pour tester la validation des données client\r\n  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (client.code) {\r\n      if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.length > 100) {\r\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal) {\r\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\r\n      if (!matFiscalPattern.test(client.matFiscal.toUpperCase())) {\r\n        errors.push('Format de matricule fiscal invalide');\r\n      }\r\n    }\r\n\r\n    if (client.email) {\r\n      if (!this.validateEmail(client.email)) {\r\n        errors.push('Format d\\'email invalide');\r\n      }\r\n      if (client.email.length > 100) {\r\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.telephone) {\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(client.telephone)) {\r\n        errors.push('Format de téléphone invalide');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n\r\n    console.error('=== ERREUR DE MISE À JOUR ===');\r\n    console.error('Status:', error.status);\r\n    console.error('StatusText:', error.statusText);\r\n    console.error('URL:', error.url);\r\n    console.error('Error object:', error.error);\r\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\r\n    console.error('Full error:', error);\r\n    console.error('============================');\r\n\r\n    // Analyser la structure de l'erreur en détail\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n    const traceId = error.error?.traceId;\r\n    const type = error.error?.type;\r\n\r\n    console.log('API Error:', apiError);\r\n    console.log('Validation Errors:', validationErrors);\r\n    console.log('Validation Errors type:', typeof validationErrors);\r\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\r\n    console.log('Trace ID:', traceId);\r\n    console.log('Error Type:', type);\r\n\r\n    // Essayer de capturer d'autres propriétés d'erreur\r\n    if (error.error) {\r\n      console.log('Toutes les propriétés de error.error:');\r\n      for (const key in error.error) {\r\n        console.log(`  ${key}:`, error.error[key]);\r\n      }\r\n    }\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;MACnCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,OAAO,EAAEI,MAAM,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC,EACF5B,UAAU,CAAC6B,KAAK,IAAG;MACjB,IAAI,CAACrB,YAAY,GAAG,KAAK;MACzBkB,OAAO,CAACG,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MAEnE;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMI,WAAW,GAAa,CAC5B;UACEC,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,EACD;UACEL,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,CACF;QACD,IAAI,CAAC9B,UAAU,CAACkB,IAAI,CAACM,WAAW,CAAC;QACjC,OAAO,IAAIlC,UAAU,CAAWyC,QAAQ,IAAG;UACzCA,QAAQ,CAACb,IAAI,CAACM,WAAW,CAAC;UAC1BO,QAAQ,CAACC,QAAQ,EAAE;QACrB,CAAC,CAAC;;MAGJ;MACA,IAAI,CAAChC,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACe,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAY,aAAaA,CAACT,EAAU;IACtB,OAAO,IAAI,CAAC5B,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC2C,MAAM,IAAG;MACX5B,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACjC,oBAAoB,CAACgB,IAAI,CAACiB,MAAM,CAAC;IACxC,CAAC,CAAC,EACF1C,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACN,KAAK,IAAI,CAAC,IAAI,CAACY,aAAa,CAACN,MAAM,CAACN,KAAK,CAAC,EAAE;MACrD,OAAOtC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMjC,UAAU,GAA0B;MACxCmB,EAAE,EAAEU,MAAM,CAACV,EAAE,IAAI,IAAI,CAACiB,YAAY,EAAE;MACpChB,IAAI,EAAES,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE;MACxBb,MAAM,EAAEQ,MAAM,CAACR,MAAM,EAAEa,IAAI,EAAE;MAC7BZ,SAAS,EAAEO,MAAM,CAACP,SAAS,EAAEY,IAAI,EAAE;MACnCX,KAAK,EAAEM,MAAM,CAACN,KAAK,EAAEW,IAAI,EAAE;MAC3BV,SAAS,EAAEK,MAAM,CAACL,SAAS,EAAEU,IAAI;KAClC;IAED,OAAO,IAAI,CAAC3C,IAAI,CAAC8C,IAAI,CAAS,IAAI,CAAC5C,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIP,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOO,SAAS;IAClB,CAAC,CAAC,EACFtD,GAAG,CAAEsD,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,IAAI,CAACjD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFrD,UAAU,CAAC,IAAI,CAACyD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAAC1B,EAAU,EAAEU,MAAuB;IAC9C;IACA,IAAI,CAACV,EAAE,IAAIA,EAAE,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3B,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,yCAAyC,CAAC,CAAC;;IAG/E,IAAI,CAACJ,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E;IACA,MAAMa,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAAC5B,EAAE,CAAC;IACzF,MAAM6B,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAAC5B,EAAE,CAAC;IAE1CN,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEgC,MAAM,EAAE,OAAO,EAAEE,MAAM,CAAC;IAEzD;IACA,IAAIC,QAAQ,GAAG9B,EAAE;IACjB,IAAI6B,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMO,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;MACzD,IAAI+B,YAAY,IAAIA,YAAY,CAAC/B,EAAE,KAAKA,EAAE,EAAE;QAC1CN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEoC,YAAY,CAAC/B,EAAE,CAAC;QACnD8B,QAAQ,GAAGC,YAAY,CAAC/B,EAAE;;;IAI9B;IACA,MAAMkC,aAAa,GAAoB,EAAE;IAEzC;IACA,MAAMrD,UAAU,GAAG;MAAE,GAAG6B;IAAM,CAAE;IAChC,IAAI,IAAI,IAAI7B,UAAU,EAAE;MACtB,OAAQA,UAAkB,CAACmB,EAAE;;IAG/B;IACA,IAAInB,UAAU,CAACoB,IAAI,IAAIpB,UAAU,CAACoB,IAAI,CAACc,IAAI,EAAE,EAAE;MAC7CmB,aAAa,CAACjC,IAAI,GAAGpB,UAAU,CAACoB,IAAI,CAACc,IAAI,EAAE;MAC3C;MACA,IAAImB,aAAa,CAACjC,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIsC,aAAa,CAACjC,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QACnE,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIjC,UAAU,CAACqB,MAAM,IAAIrB,UAAU,CAACqB,MAAM,CAACa,IAAI,EAAE,EAAE;MACjDmB,aAAa,CAAChC,MAAM,GAAGrB,UAAU,CAACqB,MAAM,CAACa,IAAI,EAAE;MAC/C,IAAImB,aAAa,CAAChC,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;QACrC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIjC,UAAU,CAACsB,SAAS,IAAItB,UAAU,CAACsB,SAAS,CAACY,IAAI,EAAE,EAAE;MACvDmB,aAAa,CAAC/B,SAAS,GAAGtB,UAAU,CAACsB,SAAS,CAACY,IAAI,EAAE,CAACoB,WAAW,EAAE;MACnE;MACA,MAAMC,gBAAgB,GAAG,6CAA6C;MACtE,IAAI,CAACA,gBAAgB,CAACR,IAAI,CAACM,aAAa,CAAC/B,SAAS,CAAC,EAAE;QACnD,OAAOrC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;;IAI7E,IAAIjC,UAAU,CAACuB,KAAK,IAAIvB,UAAU,CAACuB,KAAK,CAACW,IAAI,EAAE,EAAE;MAC/CmB,aAAa,CAAC9B,KAAK,GAAGvB,UAAU,CAACuB,KAAK,CAACW,IAAI,EAAE,CAACsB,WAAW,EAAE;MAC3D;MACA,IAAI,CAAC,IAAI,CAACrB,aAAa,CAACkB,aAAa,CAAC9B,KAAK,CAAC,EAAE;QAC5C,OAAOtC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,0BAA0B,CAAC,CAAC;;MAEhE,IAAIoB,aAAa,CAAC9B,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;QACpC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8CAA8C,CAAC,CAAC;;;IAItF,IAAIjC,UAAU,CAACwB,SAAS,IAAIxB,UAAU,CAACwB,SAAS,CAACU,IAAI,EAAE,EAAE;MACvDmB,aAAa,CAAC7B,SAAS,GAAGxB,UAAU,CAACwB,SAAS,CAACU,IAAI,EAAE;MACrD;MACA,MAAMuB,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAACM,aAAa,CAAC7B,SAAS,CAAC,EAAE;QAC/C,OAAOvC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8BAA8B,CAAC,CAAC;;;IAItE;IACA,IAAIyB,MAAM,CAACC,IAAI,CAACN,aAAa,CAAC,CAACtC,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2CAA2C,CAAC,CAAC;;IAGjF;IACA,MAAM2B,gBAAgB,GAAG,IAAI,CAACC,kBAAkB,CAACR,aAAa,EAAEJ,QAAQ,CAAC;IACzE,IAAI,CAACW,gBAAgB,CAACE,OAAO,EAAE;MAC7B,OAAO7E,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uBAAuB2B,gBAAgB,CAACG,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;;IAGjGnD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzCD,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEK,EAAE,CAAC;IAC/BN,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmC,QAAQ,CAAC;IACvCpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOmC,QAAQ,CAAC;IAC9CpC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEmC,QAAQ,CAAClC,MAAM,CAAC;IAClDF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,iEAAiE,CAACiC,IAAI,CAACE,QAAQ,CAAC,CAAC;IAChHpC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,kBAAkB,CAACiC,IAAI,CAACE,QAAQ,CAAC,CAAC;IACjEpC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,MAAM,CAAC;IAC1ChB,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEd,UAAU,CAAC;IACxDa,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuC,aAAa,CAAC;IAC1DxC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE,IAAI,IAAIuC,aAAa,GAAG,eAAe,GAAG,UAAU,CAAC;IACjGxC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAGwD,QAAQ,EAAE,CAAC;IAC1DpC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACT,UAAU,EAAE,CAAC;IAC1CQ,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEX,IAAI,CAAC4B,SAAS,CAACsB,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACjExC,OAAO,CAACC,GAAG,CAAC,0BAA0B,CAAC;IAEvC,OAAO,IAAI,CAACvB,IAAI,CAAC0E,GAAG,CAAC,GAAG,IAAI,CAACxE,OAAO,GAAGwD,QAAQ,EAAE,EAAEI,aAAa,EAAE;MAChE5C,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAACtB,MAAM,KAAK,GAAG,IAAI,CAACsB,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;QACzC;QACA,MAAMuB,cAAc,GAAGxB,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAK8B,QAAQ,IAAIG,CAAC,CAACjC,EAAE,KAAKA,EAAE,IAAIiC,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;QAC/F,IAAI,CAAC+C,cAAc,EAAE;UACnB,MAAM,IAAIjC,KAAK,CAAC,mDAAmDgB,QAAQ,eAAe9B,EAAE,GAAG,CAAC;;QAGlG;QACA,MAAMgD,aAAa,GAAW;UAC5B,GAAGD,cAAc;UACjB,GAAGb;SACJ;QACD,OAAOc,aAAa;;MAGtB;MACA,MAAMA,aAAa,GAAW5B,QAAQ,CAACE,IAAI;MAC3C,OAAO0B,aAAa;IACtB,CAAC,CAAC,EACFjF,GAAG,CAAEiF,aAAqB,IAAI;MAC5BtD,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEqD,aAAa,CAAC;MAE5D;MACA,MAAMzB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMyB,KAAK,GAAG1B,WAAW,CAAC2B,SAAS,CAACjB,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAK8B,QAAQ,IAAIG,CAAC,CAACjC,EAAE,KAAKA,EAAE,IAAIiC,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;MAC3F,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB1B,WAAW,CAAC0B,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAACzE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;;MAGxC;MACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAK8B,QAAQ,IAAI,IAAI,CAACrD,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;QAClG,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAACuD,aAAa,CAAC;QAC7ClE,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACoC,aAAa,CAAC,CAAC;;IAExE,CAAC,CAAC,EACFhF,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAEtD;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACwD,mBAAmB,CAACrB,QAAQ,EAAEI,aAAa,CAAC;;MAG1D;MACA;MACA;MACA;MACA;MACA;MAEAxC,OAAO,CAACG,KAAK,CAAC,gDAAgD,CAAC;MAC/DH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAEtD,OAAO,IAAI,CAACuD,iBAAiB,CAACvD,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACAwD,YAAYA,CAACrD,EAAU;IACrB,OAAO,IAAI,CAAC5B,IAAI,CAACkF,MAAM,CAAO,GAAG,IAAI,CAAChF,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAACwF,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACFvF,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAgD,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAAC7D,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrFpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE8D,GAAG,CAAC;IAEzD,OAAO,IAAI,CAACrF,IAAI,CAACkF,MAAM,CAAC,GAAG,IAAI,CAAChF,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BoC,IAAI,EAAEmC;KACP,CAAC,CAAClE,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP2B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;MACA,MAAM4B,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMkC,WAAW,GAAGnC,WAAW,CAACoC,MAAM,CAACjD,MAAM,IAAI,CAAC+C,GAAG,CAACG,QAAQ,CAAClD,MAAM,CAACV,EAAE,CAAC,CAAC;MAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACiE,WAAW,CAAC;IACnC,CAAC,CAAC,EACF1F,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAE/D;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACkE,qBAAqB,CAACJ,GAAG,CAAC;;MAGxC;MACA,IAAI5D,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QAChDJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI,CAACmE,yBAAyB,CAACL,GAAG,CAAC;;MAG5C,OAAO,IAAI,CAACjD,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQiE,yBAAyBA,CAACL,GAAa;IAC7C,MAAMM,cAAc,GAAGN,GAAG,CAACxF,GAAG,CAAC+B,EAAE,IAC/B,IAAI,CAAC5B,IAAI,CAACkF,MAAM,CAAC,GAAG,IAAI,CAAChF,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3EvB,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,2CAA2CG,EAAE,GAAG,EAAEH,KAAK,CAAC;MACtE,OAAO/B,UAAU,CAAC,MAAM+B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACF;IAED;IACA,OAAO,IAAIhC,UAAU,CAACyC,QAAQ,IAAG;MAC/B,IAAI0D,cAAc,GAAG,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,MAAMrB,MAAM,GAAU,EAAE;MAExBmB,cAAc,CAACG,OAAO,CAAC,CAACC,OAAO,EAAElB,KAAK,KAAI;QACxCkB,OAAO,CAACC,SAAS,CAAC;UAChB3E,IAAI,EAAEA,CAAA,KAAK;YACTuE,cAAc,EAAE;YAChB,IAAIA,cAAc,KAAKP,GAAG,CAAC7D,MAAM,IAAI,CAACqE,QAAQ,EAAE;cAC9C;cACA,MAAM1C,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;cACzC,MAAMkC,WAAW,GAAGnC,WAAW,CAACoC,MAAM,CAACjD,MAAM,IAAI,CAAC+C,GAAG,CAACG,QAAQ,CAAClD,MAAM,CAACV,EAAE,CAAC,CAAC;cAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACiE,WAAW,CAAC;cACjCpD,QAAQ,CAACb,IAAI,CAAC;gBAAE4E,YAAY,EAAEL;cAAc,CAAE,CAAC;cAC/C1D,QAAQ,CAACC,QAAQ,EAAE;;UAEvB,CAAC;UACDV,KAAK,EAAGA,KAAK,IAAI;YACfoE,QAAQ,GAAG,IAAI;YACfrB,MAAM,CAAC0B,IAAI,CAAC;cAAEtE,EAAE,EAAEyD,GAAG,CAACR,KAAK,CAAC;cAAEpD;YAAK,CAAE,CAAC;YACtC,IAAImE,cAAc,GAAGpB,MAAM,CAAChD,MAAM,KAAK6D,GAAG,CAAC7D,MAAM,EAAE;cACjDU,QAAQ,CAACT,KAAK,CAAC;gBACb0E,OAAO,EAAE,+CAA+C;gBACxD3B,MAAM;gBACNyB,YAAY,EAAEL;eACf,CAAC;;UAEN;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQH,qBAAqBA,CAACJ,GAAa;IACzC/D,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAE8D,GAAG,CAAC;IAErE;IACA,MAAMlC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMkC,WAAW,GAAGnC,WAAW,CAACoC,MAAM,CAACjD,MAAM,IAAI,CAAC+C,GAAG,CAACG,QAAQ,CAAClD,MAAM,CAACV,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACiE,WAAW,CAAC;IAEjC;IACA,OAAO,IAAI7F,UAAU,CAACyC,QAAQ,IAAG;MAC/BkE,UAAU,CAAC,MAAK;QACdlE,QAAQ,CAACb,IAAI,CAAC;UACZ4E,YAAY,EAAEZ,GAAG,CAAC7D,MAAM;UACxB2E,OAAO,EAAE;SACV,CAAC;QACFjE,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACQ4C,mBAAmBA,CAACnD,EAAU,EAAEyE,UAA2B;IACjE/E,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,EAAE,EAAEyE,UAAU,CAAC;IAE9E;IACA,MAAMlD,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMyB,KAAK,GAAG1B,WAAW,CAAC2B,SAAS,CAACxC,MAAM,IAAIA,MAAM,CAACV,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIiD,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOnF,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E;IACA,MAAMkC,aAAa,GAAW;MAC5B,GAAGzB,WAAW,CAAC0B,KAAK,CAAC;MACrB,GAAGwB;KACJ;IAED;IACAlD,WAAW,CAAC0B,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAACzE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;IAEtC;IACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;MAC9C,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAACuD,aAAa,CAAC;MAC7ClE,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACoC,aAAa,CAAC,CAAC;;IAGtE;IACA,OAAO,IAAInF,UAAU,CAASyC,QAAQ,IAAG;MACvCkE,UAAU,CAAC,MAAK;QACdlE,QAAQ,CAACb,IAAI,CAACuD,aAAa,CAAC;QAC5B1C,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACAgD,kBAAkBA,CAAA;IAChBzE,YAAY,CAAC4F,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACjG,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAIkF,IAAIA,CAAA;IACN,OAAO,IAAI,CAACpG,UAAU,CAACiD,KAAK;EAC9B;EAEAoD,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQ5D,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAAC6D,OAAO,CAAC,OAAO,EAAE,UAAS7C,CAAC;MACvE,MAAM8C,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGjD,CAAC,KAAK,GAAG,GAAG8C,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQnE,aAAaA,CAACZ,KAAa;IACjC,MAAMgF,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAACxD,IAAI,CAACxB,KAAK,CAAC;EACvB;EAEA;EACOiF,YAAYA,CAACrF,EAAU;IAC5BN,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEK,EAAE,CAAC;IAC7BN,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAOK,EAAE,CAAC;IAC/BN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEK,EAAE,CAACJ,MAAM,CAAC;IACnCF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,iEAAiE,CAACiC,IAAI,CAAC5B,EAAE,CAAC,CAAC;IACpGN,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAACiC,IAAI,CAAC5B,EAAE,CAAC,CAAC;IAErD,MAAMuB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAM8D,UAAU,GAAG/D,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACjC,EAAE,KAAKA,EAAE,CAAC;IACrD,MAAM+B,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAKD,EAAE,CAAC;IAEzDN,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE2F,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;IAChE5F,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEoC,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IAEpE,IAAIA,YAAY,EAAE;MAChBrC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEoC,YAAY,CAAC;;IAE/CrC,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA;EACO4F,oBAAoBA,CAACvF,EAAU,EAAEyE,UAA2B;IACjE/E,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEK,EAAE,CAAC;IACtBN,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE8E,UAAU,CAAC;IAEnC,MAAMe,UAAU,GAAG,IAAI,CAAC9C,kBAAkB,CAAC+B,UAAU,EAAEzE,EAAE,CAAC;IAC1DN,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE6F,UAAU,CAAC7C,OAAO,CAAC;IACtD,IAAI,CAAC6C,UAAU,CAAC7C,OAAO,EAAE;MACvBjD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6F,UAAU,CAAC5C,MAAM,CAAC;;IAG5ClD,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,MAAM8F,OAAO,GAAoB,EAAE;IACnC,IAAIhB,UAAU,CAACxE,IAAI,IAAIwE,UAAU,CAACxE,IAAI,CAACc,IAAI,EAAE,EAAE0E,OAAO,CAACxF,IAAI,GAAGwE,UAAU,CAACxE,IAAI,CAACc,IAAI,EAAE;IACpF,IAAI0D,UAAU,CAACvE,MAAM,IAAIuE,UAAU,CAACvE,MAAM,CAACa,IAAI,EAAE,EAAE0E,OAAO,CAACvF,MAAM,GAAGuE,UAAU,CAACvE,MAAM,CAACa,IAAI,EAAE;IAC5F,IAAI0D,UAAU,CAACtE,SAAS,IAAIsE,UAAU,CAACtE,SAAS,CAACY,IAAI,EAAE,EAAE0E,OAAO,CAACtF,SAAS,GAAGsE,UAAU,CAACtE,SAAS,CAACY,IAAI,EAAE,CAACoB,WAAW,EAAE;IACtH,IAAIsC,UAAU,CAACrE,KAAK,IAAIqE,UAAU,CAACrE,KAAK,CAACW,IAAI,EAAE,EAAE0E,OAAO,CAACrF,KAAK,GAAGqE,UAAU,CAACrE,KAAK,CAACW,IAAI,EAAE,CAACsB,WAAW,EAAE;IACtG,IAAIoC,UAAU,CAACpE,SAAS,IAAIoE,UAAU,CAACpE,SAAS,CAACU,IAAI,EAAE,EAAE0E,OAAO,CAACpF,SAAS,GAAGoE,UAAU,CAACpE,SAAS,CAACU,IAAI,EAAE;IAExGrB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE8F,OAAO,CAAC;IACrC/F,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAG0B,EAAE,EAAE,CAAC;IAC/DN,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQ+C,kBAAkBA,CAAChC,MAAuB,EAAEV,EAAU;IAC5D,MAAM4C,MAAM,GAAa,EAAE;IAE3B;IACA,IAAI,CAAC5C,EAAE,IAAIA,EAAE,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3B6B,MAAM,CAAC0B,IAAI,CAAC,WAAW,CAAC;KACzB,MAAM;MACL,MAAM3C,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAAC5B,EAAE,CAAC;MACzF,IAAI,CAAC2B,MAAM,EAAE;QACXiB,MAAM,CAAC0B,IAAI,CAAC,6BAA6B,CAAC;;;IAI9C;IACA,IAAI5D,MAAM,CAACT,IAAI,KAAKyF,SAAS,EAAE;MAC7B,IAAI,CAAChF,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7C6B,MAAM,CAAC0B,IAAI,CAAC,4BAA4B,CAAC;OAC1C,MAAM,IAAI5D,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIc,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QAC5DgD,MAAM,CAAC0B,IAAI,CAAC,6CAA6C,CAAC;;;IAI9D,IAAI5D,MAAM,CAACR,MAAM,KAAKwF,SAAS,IAAIhF,MAAM,CAACR,MAAM,KAAK,IAAI,IAAIQ,MAAM,CAACR,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;MACvFgD,MAAM,CAAC0B,IAAI,CAAC,oDAAoD,CAAC;;IAGnE,IAAI5D,MAAM,CAACP,SAAS,KAAKuF,SAAS,IAAIhF,MAAM,CAACP,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIO,MAAM,CAACP,SAAS,CAACY,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAMqB,gBAAgB,GAAG,6CAA6C;QACtE,IAAI,CAACA,gBAAgB,CAACR,IAAI,CAAClB,MAAM,CAACP,SAAS,CAACgC,WAAW,EAAE,CAAC,EAAE;UAC1DS,MAAM,CAAC0B,IAAI,CAAC,qCAAqC,CAAC;;;;IAKxD,IAAI5D,MAAM,CAACN,KAAK,KAAKsF,SAAS,IAAIhF,MAAM,CAACN,KAAK,KAAK,IAAI,EAAE;MACvD,IAAIM,MAAM,CAACN,KAAK,CAACW,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B,IAAI,CAAC,IAAI,CAACC,aAAa,CAACN,MAAM,CAACN,KAAK,CAAC,EAAE;UACrCwC,MAAM,CAAC0B,IAAI,CAAC,0BAA0B,CAAC;;QAEzC,IAAI5D,MAAM,CAACN,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;UAC7BgD,MAAM,CAAC0B,IAAI,CAAC,2CAA2C,CAAC;;;;IAK9D,IAAI5D,MAAM,CAACL,SAAS,KAAKqF,SAAS,IAAIhF,MAAM,CAACL,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIK,MAAM,CAACL,SAAS,CAACU,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAMuB,YAAY,GAAG,6BAA6B;QAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAAClB,MAAM,CAACL,SAAS,CAAC,EAAE;UACxCuC,MAAM,CAAC0B,IAAI,CAAC,8BAA8B,CAAC;;;;IAKjD,OAAO;MACL3B,OAAO,EAAEC,MAAM,CAAChD,MAAM,KAAK,CAAC;MAC5BgD;KACD;EACH;EAEA;EACO+C,WAAWA,CAACC,QAAgB,EAAEnB,UAAe;IAClD/E,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAAC0F,YAAY,CAACO,QAAQ,CAAC;IAE3B;IACA,IAAI,CAACL,oBAAoB,CAACK,QAAQ,EAAEnB,UAAU,CAAC;IAE/C;IACA/E,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,MAAMgC,MAAM,GAAG,iEAAiE,CAACC,IAAI,CAACgE,QAAQ,CAAC;IAC/F,MAAM/D,MAAM,GAAG,kBAAkB,CAACD,IAAI,CAACgE,QAAQ,CAAC;IAEhD,IAAI9D,QAAQ,GAAG8D,QAAQ;IACvB,IAAI/D,MAAM,IAAI,CAACF,MAAM,EAAE;MACrB,MAAMJ,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMO,YAAY,GAAGR,WAAW,CAACS,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAChC,IAAI,KAAK2F,QAAQ,CAAC;MAC/D,IAAI7D,YAAY,EAAE;QAChBD,QAAQ,GAAGC,YAAY,CAAC/B,EAAE;QAC1BN,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEmC,QAAQ,CAAC;;;IAIvCpC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAACrB,OAAO,GAAGwD,QAAQ,EAAE,CAAC;IACxDpC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACT,UAAU,EAAE,CAAC;IAE1C,MAAM2G,WAAW,GAAQ,EAAE;IAC3B,IAAIpB,UAAU,CAACxE,IAAI,EAAE4F,WAAW,CAAC5F,IAAI,GAAGwE,UAAU,CAACxE,IAAI,CAACc,IAAI,EAAE;IAC9D,IAAI0D,UAAU,CAACvE,MAAM,EAAE2F,WAAW,CAAC3F,MAAM,GAAGuE,UAAU,CAACvE,MAAM,CAACa,IAAI,EAAE;IACpE,IAAI0D,UAAU,CAACtE,SAAS,EAAE0F,WAAW,CAAC1F,SAAS,GAAGsE,UAAU,CAACtE,SAAS,CAACY,IAAI,EAAE,CAACoB,WAAW,EAAE;IAC3F,IAAIsC,UAAU,CAACrE,KAAK,EAAEyF,WAAW,CAACzF,KAAK,GAAGqE,UAAU,CAACrE,KAAK,CAACW,IAAI,EAAE,CAACsB,WAAW,EAAE;IAC/E,IAAIoC,UAAU,CAACpE,SAAS,EAAEwF,WAAW,CAACxF,SAAS,GAAGoE,UAAU,CAACpE,SAAS,CAACU,IAAI,EAAE;IAE7ErB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEX,IAAI,CAAC4B,SAAS,CAACiF,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChEnG,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACOmG,kBAAkBA,CAACF,QAAgB,EAAEnB,UAAe;IACzD/E,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,MAAMoG,GAAG,GAAG,GAAG,IAAI,CAACzH,OAAO,GAAGsH,QAAQ,EAAE;IACxC,MAAMtG,OAAO,GAAG,IAAI,CAACJ,UAAU,EAAE;IAEjCQ,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEoG,GAAG,CAAC;IACxBrG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEL,OAAO,CAAC;IAChCI,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEX,IAAI,CAAC4B,SAAS,CAAC6D,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO,IAAI,CAACrG,IAAI,CAAC0E,GAAG,CAACiD,GAAG,EAAEtB,UAAU,EAAE;MACpCnF,OAAO,EAAEA,OAAO;MAChB6B,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLxB,GAAG,CAACqD,QAAQ,IAAG;MACb1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEyB,QAAQ,CAAC;IAC7C,CAAC,CAAC,EACFpD,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CH,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACC,MAAM,CAAC;MACtCJ,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MAEzC;MACA,OAAO/B,UAAU,CAAC,MAAM+B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACOmG,iBAAiBA,CAACJ,QAAgB;IACvClG,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAMsG,YAAY,GAAG;MAAEhG,IAAI,EAAE;IAAS,CAAE;IACxCP,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsG,YAAY,CAAC;IAErD,OAAO,IAAI,CAACH,kBAAkB,CAACF,QAAQ,EAAEK,YAAY,CAAC,CAAC1G,IAAI,CACzDvB,UAAU,CAACkI,MAAM,IAAG;MAClBxG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA,MAAMwG,YAAY,GAAG;QAAEjG,MAAM,EAAE;MAAa,CAAE;MAC9CR,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEwG,YAAY,CAAC;MAEvD,OAAO,IAAI,CAACL,kBAAkB,CAACF,QAAQ,EAAEO,YAAY,CAAC,CAAC5G,IAAI,CACzDvB,UAAU,CAACoI,MAAM,IAAG;QAClB1G,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C;QACA,MAAM0G,YAAY,GAAG,EAAE;QACvB3G,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE0G,YAAY,CAAC;QAEjD,OAAO,IAAI,CAACP,kBAAkB,CAACF,QAAQ,EAAES,YAAY,CAAC;MACxD,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACOC,kBAAkBA,CAAC5F,MAAuB;IAC/C,MAAMkC,MAAM,GAAa,EAAE;IAE3B,IAAIlC,MAAM,CAACT,IAAI,EAAE;MACf,IAAIS,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,CAAC,IAAIc,MAAM,CAACT,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QACrDgD,MAAM,CAAC0B,IAAI,CAAC,uDAAuD,CAAC;;;IAIxE,IAAI5D,MAAM,CAACR,MAAM,IAAIQ,MAAM,CAACR,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;MAC/CgD,MAAM,CAAC0B,IAAI,CAAC,uDAAuD,CAAC;;IAGtE,IAAI5D,MAAM,CAACP,SAAS,EAAE;MACpB,MAAMiC,gBAAgB,GAAG,6CAA6C;MACtE,IAAI,CAACA,gBAAgB,CAACR,IAAI,CAAClB,MAAM,CAACP,SAAS,CAACgC,WAAW,EAAE,CAAC,EAAE;QAC1DS,MAAM,CAAC0B,IAAI,CAAC,qCAAqC,CAAC;;;IAItD,IAAI5D,MAAM,CAACN,KAAK,EAAE;MAChB,IAAI,CAAC,IAAI,CAACY,aAAa,CAACN,MAAM,CAACN,KAAK,CAAC,EAAE;QACrCwC,MAAM,CAAC0B,IAAI,CAAC,0BAA0B,CAAC;;MAEzC,IAAI5D,MAAM,CAACN,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;QAC7BgD,MAAM,CAAC0B,IAAI,CAAC,8CAA8C,CAAC;;;IAI/D,IAAI5D,MAAM,CAACL,SAAS,EAAE;MACpB,MAAMiC,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACV,IAAI,CAAClB,MAAM,CAACL,SAAS,CAAC,EAAE;QACxCuC,MAAM,CAAC0B,IAAI,CAAC,8BAA8B,CAAC;;;IAI/C,OAAO;MACL3B,OAAO,EAAEC,MAAM,CAAChD,MAAM,KAAK,CAAC;MAC5BgD;KACD;EACH;EAEA;EACQQ,iBAAiBA,CAACvD,KAAwB;IAChD,IAAI0G,YAAY,GAAG,yCAAyC;IAE5D7G,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;IAC9CH,OAAO,CAACG,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACC,MAAM,CAAC;IACtCJ,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC2G,UAAU,CAAC;IAC9C9G,OAAO,CAACG,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACkG,GAAG,CAAC;IAChCrG,OAAO,CAACG,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CH,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,GAAG0C,MAAM,CAACC,IAAI,CAAC3C,KAAK,CAACA,KAAK,CAAC,GAAG,iBAAiB,CAAC;IACxFH,OAAO,CAACG,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnCH,OAAO,CAACG,KAAK,CAAC,8BAA8B,CAAC;IAE7C;IACA,MAAM4G,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE0E,OAAO,IAAI1E,KAAK,CAACA,KAAK,EAAE6G,KAAK,IAAI7G,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAM8G,gBAAgB,GAAG9G,KAAK,CAACA,KAAK,EAAE+C,MAAM;IAC5C,MAAMgE,OAAO,GAAG/G,KAAK,CAACA,KAAK,EAAE+G,OAAO;IACpC,MAAMC,IAAI,GAAGhH,KAAK,CAACA,KAAK,EAAEgH,IAAI;IAE9BnH,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE8G,QAAQ,CAAC;IACnC/G,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEgH,gBAAgB,CAAC;IACnDjH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOgH,gBAAgB,CAAC;IAC/DjH,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEgH,gBAAgB,GAAGpE,MAAM,CAACC,IAAI,CAACmE,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;IACjHjH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEiH,OAAO,CAAC;IACjClH,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEkH,IAAI,CAAC;IAEhC;IACA,IAAIhH,KAAK,CAACA,KAAK,EAAE;MACfH,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,KAAK,MAAMmH,GAAG,IAAIjH,KAAK,CAACA,KAAK,EAAE;QAC7BH,OAAO,CAACC,GAAG,CAAC,KAAKmH,GAAG,GAAG,EAAEjH,KAAK,CAACA,KAAK,CAACiH,GAAG,CAAC,CAAC;;;IAI9C,IAAIjH,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtByG,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAII,gBAAgB,EAAE;QACpB,MAAMI,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOJ,gBAAgB,KAAK,QAAQ,EAAE;UACxCpE,MAAM,CAACC,IAAI,CAACmE,gBAAgB,CAAC,CAACzC,OAAO,CAAC8C,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGN,gBAAgB,CAACK,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAAC/C,OAAO,CAACkD,GAAG,IAAIL,YAAY,CAACzC,IAAI,CAAC,GAAG0C,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAACzC,IAAI,CAAC,GAAG0C,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAACnH,MAAM,GAAG,CAAC,EAAE;UAC3B2G,YAAY,IAAI,iBAAiBQ,YAAY,CAAClE,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAI4D,QAAQ,EAAE;QACnBF,YAAY,IAAI,eAAeE,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC7C,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/D2C,YAAY,GAAG,0BAA0B;QACzC,IAAI1G,KAAK,CAACA,KAAK,EAAE+C,MAAM,EAAE;UACvB2D,YAAY,IAAI,0CAA0C;UAC1DhE,MAAM,CAACC,IAAI,CAAC3C,KAAK,CAACA,KAAK,CAAC+C,MAAM,CAAC,CAACsB,OAAO,CAAC8C,KAAK,IAAG;YAC9CT,YAAY,IAAI,KAAKS,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACLT,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAClI,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIE,QAAQ,EAAE;MACnBF,YAAY,GAAG,0BAA0BE,QAAQ,EAAE;;IAGrD,OAAO3I,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACyF,YAAY,CAAC,CAAC;EAClD;EAEQ9E,iBAAiBA,CAAC5B,KAAwB;IAChD,IAAI0G,YAAY,GAAG,sCAAsC;IACzD,MAAME,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE0E,OAAO,IAAI1E,KAAK,CAACA,KAAK,EAAE6G,KAAK,IAAI7G,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAI4G,QAAQ,EAAE;MACZF,YAAY,GAAG,uBAAuBE,QAAQ,EAAE;KACjD,MAAM,IAAI5G,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7ByG,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,mBAAmB;MAClC,IAAI1G,KAAK,CAACA,KAAK,EAAE+C,MAAM,EAAE;QACvB,MAAM+D,gBAAgB,GAAGpE,MAAM,CAAC+E,MAAM,CAACzH,KAAK,CAACA,KAAK,CAAC+C,MAAM,CAAC,CAAC2E,IAAI,EAAE;QACjEhB,YAAY,IAAI,aAAaI,gBAAgB,CAAC9D,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAIhD,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAClI,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,mCAAmC;;IAGpD,OAAOzI,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACyF,YAAY,CAAC,CAAC;EAClD;EAEQ/F,WAAWA,CAACX,KAAwB;IAC1C,IAAI0G,YAAY,GAAG,mBAAmB;IACtC,MAAME,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE0E,OAAO,IAAI1E,KAAK,CAACA,KAAK,EAAE6G,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZF,YAAY,GAAGE,QAAQ;KACxB,MAAM,IAAI5G,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7ByG,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,cAAc;MAC7B,IAAI,CAAClI,MAAM,CAACgJ,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAI1G,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/ByG,YAAY,GAAG,kCAAkC;;IAGnD,OAAOzI,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAACyF,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAiB,CAAA,G;qBAt2BUtJ,aAAa,EAAAuJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb7J,aAAa;IAAA8J,OAAA,EAAb9J,aAAa,CAAA+J,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}