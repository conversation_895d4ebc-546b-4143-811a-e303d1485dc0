// Exemple de gestion d'erreurs côté frontend pour les contraintes de clé étrangère

// Dans le service client.service.ts
private handleDeleteError(error: HttpErrorResponse) {
  console.error('=== ERREUR SUPPRESSION ===');
  console.error('Status:', error.status);
  console.error('Error object:', error.error);
  console.error('=========================');

  let errorMessage = 'Erreur lors de la suppression';
  
  if (error.status === 400 && error.error?.hasRelatedData) {
    // Erreur de contrainte de clé étrangère
    errorMessage = error.error.message;
  } else if (error.status === 0) {
    errorMessage = 'Impossible de se connecter au serveur';
  } else if (error.status === 404) {
    errorMessage = 'Client non trouvé';
  } else if (error.status === 500) {
    errorMessage = 'Erreur interne du serveur';
  }

  const detailedError = new Error(errorMessage);
  (detailedError as any).originalError = error;
  (detailedError as any).hasRelatedData = error.error?.hasRelatedData;
  (detailedError as any).relatedInfo = error.error;
  
  return throwError(() => detailedError);
}

// Dans le composant de suppression individuelle
confirmDelete(): void {
  this.clientService.deleteClient(this.data.id).subscribe({
    next: () => {
      this.dialogRef.close(true);
    },
    error: (error) => {
      console.error('Erreur lors de la suppression:', error);
      
      if (error.hasRelatedData) {
        // Afficher une alerte spécifique pour les contraintes
        const relatedInfo = error.relatedInfo;
        let alertMessage = `⚠️ Suppression impossible\n\n${error.message}`;
        
        if (relatedInfo.relatedCount) {
          alertMessage += `\n\nNombre de ${relatedInfo.relatedType}: ${relatedInfo.relatedCount}`;
        }
        
        alert(alertMessage);
      } else {
        // Erreur générique
        alert(`Erreur: ${error.message}`);
      }
      
      this.dialogRef.close(false);
    }
  });
}

// Dans le composant de suppression en lot
removeSelectedRows(): void {
  const selectedIds = this.selection.selected.map(item => item.id);
  
  this.clientService.deleteSelectedClients(selectedIds).subscribe({
    next: (result) => {
      // Succès
      this.selection.clear();
      this.refreshTable();
      this.showSuccessNotification(`${result.deletedCount} clients supprimés`);
    },
    error: (error) => {
      console.error('Erreur suppression en lot:', error);
      
      if (error.hasRelatedData) {
        const relatedInfo = error.relatedInfo;
        let alertMessage = `⚠️ Suppression impossible\n\n${error.message}`;
        
        if (relatedInfo.clientsWithRelations) {
          alertMessage += '\n\nClients concernés:\n';
          relatedInfo.clientsWithRelations.forEach((client: any) => {
            alertMessage += `• ${client.code} (${client.devisCount} devis)\n`;
          });
        }
        
        alert(alertMessage);
      } else {
        this.showErrorNotification('Erreur lors de la suppression');
      }
      
      this.selection.clear();
      this.refreshTable();
    }
  });
}

// Types TypeScript pour une meilleure gestion des erreurs
interface RelatedDataError {
  message: string;
  hasRelatedData: boolean;
  relatedCount?: number;
  relatedType?: string;
  clientsWithRelations?: Array<{
    code: string;
    devisCount: number;
  }>;
  totalClientsRequested?: number;
  clientsWithRelationsCount?: number;
}

interface DeleteResponse {
  message: string;
  deletedCount?: number;
}
