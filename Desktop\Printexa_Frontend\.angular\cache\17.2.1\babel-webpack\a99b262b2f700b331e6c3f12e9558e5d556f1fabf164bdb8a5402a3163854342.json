{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) actualId = clientByCode.id;\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\n    }\n    // Nettoyer les données (supprimer l'id et les champs vides)\n    const cleanedClient = {};\n    Object.keys(client).forEach(key => {\n      if (key !== 'id' && client[key]?.toString().trim()) {\n        cleanedClient[key] = client[key];\n      }\n    });\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Gérer NoContent (204) ou réponse avec body\n      if (response.status === 204 || !response.body) {\n        const existing = this.dataChange.value.find(c => c.id === actualId);\n        return existing ? {\n          ...existing,\n          ...cleanedClient\n        } : cleanedClient;\n      }\n      return response.body;\n    }), tap(updatedClient => {\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(error => {\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || error.status === 400 && error.error?.message?.includes('validation')) {\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(error => {\n      // Simulation locale en cas d'erreur\n      if (error.status === 0 || error.status === 500) {\n        return this.simulateLocalDeletion(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Simulation locale de suppression\n  simulateLocalDeletion(ids) {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length\n        });\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Simulation locale de mise à jour\n  simulateLocalUpdate(id, updateData) {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Méthode de test pour diagnostiquer les problèmes d'ID\n  testClientId(id) {\n    console.log('=== TEST ID CLIENT ===');\n    console.log('ID fourni:', id);\n    console.log('Type:', typeof id);\n    console.log('Longueur:', id.length);\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\n    const currentData = this.dataChange.value;\n    console.log('Nombre de clients dans les données:', currentData.length);\n    const clientById = currentData.find(c => c.id === id);\n    const clientByCode = currentData.find(c => c.code === id);\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\n    if (clientById) {\n      console.log('Client par ID:', clientById);\n    }\n    if (clientByCode) {\n      console.log('Client par code:', clientByCode);\n    }\n    // Test de résolution d'ID\n    console.log('--- Test de résolution ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const clientByCodeResolution = currentData.find(c => c.code === id);\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\n        actualId = clientByCodeResolution.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    console.log('ID final:', actualId);\n    console.log('ID final est GUID:', finalIsGuid);\n    console.log('===================');\n  }\n  // Méthode de test pour valider une mise à jour sans l'envoyer\n  testUpdateValidation(id, updateData) {\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\n    console.log('ID:', id);\n    console.log('Données:', updateData);\n    const validation = this.validateUpdateData(updateData);\n    console.log('Validation réussie:', validation.isValid);\n    if (!validation.isValid) {\n      console.log('Erreurs:', validation.errors);\n    }\n    console.log('Données nettoyées qui seraient envoyées:');\n    const cleaned = {};\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\n    console.log('Cleaned data:', cleaned);\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\n    console.log('================================');\n  }\n  // Validation stricte pour la mise à jour\n  validateUpdateData(client) {\n    const errors = [];\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\n    // Validation des champs\n    if (client.code !== undefined) {\n      if (!client.code || client.code.trim() === '') {\n        errors.push('Code ne peut pas être vide');\n      } else if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Code doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\n      // Validation de format supprimée - accepter tout format\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email !== undefined && client.email !== null) {\n      if (client.email.trim() !== '') {\n        // Validation de format supprimée - garder seulement la limite de longueur\n        if (client.email.length > 100) {\n          errors.push('Email ne peut pas dépasser 100 caractères');\n        }\n      }\n    }\n    if (client.telephone !== undefined && client.telephone !== null) {\n      if (client.telephone.trim() !== '') {\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n        if (!phonePattern.test(client.telephone)) {\n          errors.push('Format de téléphone invalide');\n        }\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Méthode publique pour tester une mise à jour complète\n  debugUpdate(clientId, updateData) {\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\n    // Test 1: Validation de l'ID\n    this.testClientId(clientId);\n    // Test 2: Validation des données\n    this.testUpdateValidation(clientId, updateData);\n    // Test 3: Simulation de la requête\n    console.log('--- Simulation de la requête ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\n    let actualId = clientId;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === clientId);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Méthode: PUT');\n    console.log('Headers:', this.getHeaders());\n    const cleanedData = {};\n    if (updateData.code) cleanedData.code = updateData.code.trim();\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\n    console.log('================================');\n  }\n  // Méthode pour tester la requête HTTP brute\n  testRawHttpRequest(clientId, updateData) {\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\n    const url = `${this.baseUrl}${clientId}`;\n    const headers = this.getHeaders();\n    console.log('URL:', url);\n    console.log('Headers:', headers);\n    console.log('Body:', JSON.stringify(updateData, null, 2));\n    return this.http.put(url, updateData, {\n      headers: headers,\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('SUCCÈS - Response:', response);\n    }), catchError(error => {\n      console.error('ÉCHEC - Error détaillé:', error);\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      // Retourner l'erreur pour que l'appelant puisse la voir\n      return throwError(() => error);\n    }));\n  }\n  // Test avec des données minimales pour identifier le problème\n  testMinimalUpdate(clientId) {\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\n    // Test 1: Seulement le code\n    const minimalData1 = {\n      code: 'TEST001'\n    };\n    console.log('Test 1 - Seulement code:', minimalData1);\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(catchError(() => {\n      console.log('Test 1 échoué, essai test 2...');\n      // Test 2: Seulement la syntax\n      const minimalData2 = {\n        syntax: 'Test Client'\n      };\n      console.log('Test 2 - Seulement syntax:', minimalData2);\n      return this.testRawHttpRequest(clientId, minimalData2).pipe(catchError(() => {\n        console.log('Test 2 échoué, essai test 3...');\n        // Test 3: Objet vide\n        const minimalData3 = {};\n        console.log('Test 3 - Objet vide:', minimalData3);\n        return this.testRawHttpRequest(clientId, minimalData3);\n      }));\n    }));\n  }\n  // Méthode utilitaire pour tester la validation des données client\n  validateClientData(client) {\n    const errors = [];\n    if (client.code) {\n      if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax && client.syntax.length > 100) {\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.email.length > 100) {\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\n      }\n    }\n    if (client.telephone) {\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(client.telephone)) {\n        errors.push('Format de téléphone invalide');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('=== ERREUR DE MISE À JOUR ===');\n    console.error('Status:', error.status);\n    console.error('StatusText:', error.statusText);\n    console.error('URL:', error.url);\n    console.error('Error object:', error.error);\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\n    console.error('Full error:', error);\n    console.error('============================');\n    // Analyser la structure de l'erreur en détail\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    const traceId = error.error?.traceId;\n    const type = error.error?.type;\n    console.log('API Error:', apiError);\n    console.log('Validation Errors:', validationErrors);\n    console.log('Validation Errors type:', typeof validationErrors);\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\n    console.log('Trace ID:', traceId);\n    console.log('Error Type:', type);\n    // Essayer de capturer d'autres propriétés d'erreur\n    if (error.error) {\n      console.log('Toutes les propriétés de error.error:');\n      for (const key in error.error) {\n        console.log(`  ${key}:`, error.error[key]);\n      }\n    }\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "email", "validateEmail", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "telephone", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "cleanedClient", "Object", "keys", "for<PERSON>ach", "key", "toString", "length", "put", "status", "existing", "updatedClient", "index", "findIndex", "message", "includes", "simulateLocalUpdate", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "filter", "simulateLocalDeletion", "observer", "setTimeout", "deletedCount", "complete", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "re", "testClientId", "console", "log", "clientById", "clientByCodeResolution", "finalIsGuid", "testUpdateValidation", "validation", "validateUpdateData", "<PERSON><PERSON><PERSON><PERSON>", "errors", "cleaned", "toUpperCase", "toLowerCase", "undefined", "push", "phonePattern", "debugUpdate", "clientId", "cleanedData", "testRawHttpRequest", "url", "testMinimalUpdate", "minimalData1", "minimalData2", "minimalData3", "validateClientData", "handleUpdateError", "errorMessage", "statusText", "apiError", "title", "validationErrors", "traceId", "type", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "join", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    if (!id?.trim() || !client) {\r\n      return throwError(() => new Error('ID et données client requis'));\r\n    }\r\n\r\n    // Résoudre l'ID si c'est un code client\r\n    let actualId = id;\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n    if (isCode) {\r\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\r\n      if (clientByCode) actualId = clientByCode.id;\r\n    }\r\n\r\n    // Valider que l'ID final est un GUID\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    if (!isGuid) {\r\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\r\n    }\r\n\r\n    // Nettoyer les données (supprimer l'id et les champs vides)\r\n    const cleanedClient: UpdateClientDto = {};\r\n    Object.keys(client).forEach(key => {\r\n      if (key !== 'id' && client[key as keyof UpdateClientDto]?.toString().trim()) {\r\n        cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];\r\n      }\r\n    });\r\n\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\r\n    }\r\n\r\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Gérer NoContent (204) ou réponse avec body\r\n        if (response.status === 204 || !response.body) {\r\n          const existing = this.dataChange.value.find(c => c.id === actualId);\r\n          return existing ? { ...existing, ...cleanedClient } : cleanedClient as Client;\r\n        }\r\n        return response.body;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === actualId);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        // Mode simulation en cas d'erreur\r\n        if (error.status === 0 || (error.status === 400 && error.error?.message?.includes('validation'))) {\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids?.length) {\r\n      return throwError(() => new Error('Aucun ID fourni'));\r\n    }\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\r\n      }),\r\n      catchError(error => {\r\n        // Simulation locale en cas d'erreur\r\n        if (error.status === 0 || error.status === 500) {\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Simulation locale de suppression\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    const currentData = this.dataChange.value;\r\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\r\n\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({ deletedCount: ids.length });\r\n        observer.complete();\r\n      }, 300);\r\n    });\r\n  }\r\n\r\n  // Simulation locale de mise à jour\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé'));\r\n    }\r\n\r\n    const updatedClient: Client = { ...currentData[index], ...updateData };\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 300);\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Méthode de test pour diagnostiquer les problèmes d'ID\r\n  public testClientId(id: string): void {\r\n    console.log('=== TEST ID CLIENT ===');\r\n    console.log('ID fourni:', id);\r\n    console.log('Type:', typeof id);\r\n    console.log('Longueur:', id.length);\r\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\r\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\r\n\r\n    const currentData = this.dataChange.value;\r\n    console.log('Nombre de clients dans les données:', currentData.length);\r\n\r\n    const clientById = currentData.find(c => c.id === id);\r\n    const clientByCode = currentData.find(c => c.code === id);\r\n\r\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\r\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\r\n\r\n    if (clientById) {\r\n      console.log('Client par ID:', clientById);\r\n    }\r\n    if (clientByCode) {\r\n      console.log('Client par code:', clientByCode);\r\n    }\r\n\r\n    // Test de résolution d'ID\r\n    console.log('--- Test de résolution ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const clientByCodeResolution = currentData.find(c => c.code === id);\r\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\r\n        actualId = clientByCodeResolution.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    console.log('ID final:', actualId);\r\n    console.log('ID final est GUID:', finalIsGuid);\r\n    console.log('===================');\r\n  }\r\n\r\n  // Méthode de test pour valider une mise à jour sans l'envoyer\r\n  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {\r\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\r\n    console.log('ID:', id);\r\n    console.log('Données:', updateData);\r\n\r\n    const validation = this.validateUpdateData(updateData);\r\n    console.log('Validation réussie:', validation.isValid);\r\n    if (!validation.isValid) {\r\n      console.log('Erreurs:', validation.errors);\r\n    }\r\n\r\n    console.log('Données nettoyées qui seraient envoyées:');\r\n    const cleaned: UpdateClientDto = {};\r\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\r\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Cleaned data:', cleaned);\r\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Validation stricte pour la mise à jour\r\n  private validateUpdateData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\r\n\r\n    // Validation des champs\r\n    if (client.code !== undefined) {\r\n      if (!client.code || client.code.trim() === '') {\r\n        errors.push('Code ne peut pas être vide');\r\n      } else if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Code doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\r\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\r\n      // Validation de format supprimée - accepter tout format\r\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email !== undefined && client.email !== null) {\r\n      if (client.email.trim() !== '') {\r\n        // Validation de format supprimée - garder seulement la limite de longueur\r\n        if (client.email.length > 100) {\r\n          errors.push('Email ne peut pas dépasser 100 caractères');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.telephone !== undefined && client.telephone !== null) {\r\n      if (client.telephone.trim() !== '') {\r\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n        if (!phonePattern.test(client.telephone)) {\r\n          errors.push('Format de téléphone invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Méthode publique pour tester une mise à jour complète\r\n  public debugUpdate(clientId: string, updateData: any): void {\r\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\r\n\r\n    // Test 1: Validation de l'ID\r\n    this.testClientId(clientId);\r\n\r\n    // Test 2: Validation des données\r\n    this.testUpdateValidation(clientId, updateData);\r\n\r\n    // Test 3: Simulation de la requête\r\n    console.log('--- Simulation de la requête ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\r\n\r\n    let actualId = clientId;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === clientId);\r\n      if (clientByCode) {\r\n        actualId = clientByCode.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\r\n    console.log('Méthode: PUT');\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const cleanedData: any = {};\r\n    if (updateData.code) cleanedData.code = updateData.code.trim();\r\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\r\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\r\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\r\n    console.log('================================');\r\n  }\r\n\r\n  // Méthode pour tester la requête HTTP brute\r\n  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {\r\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\r\n\r\n    const url = `${this.baseUrl}${clientId}`;\r\n    const headers = this.getHeaders();\r\n\r\n    console.log('URL:', url);\r\n    console.log('Headers:', headers);\r\n    console.log('Body:', JSON.stringify(updateData, null, 2));\r\n\r\n    return this.http.put(url, updateData, {\r\n      headers: headers,\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('SUCCÈS - Response:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('ÉCHEC - Error détaillé:', error);\r\n        console.error('Status:', error.status);\r\n        console.error('Error body:', error.error);\r\n\r\n        // Retourner l'erreur pour que l'appelant puisse la voir\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Test avec des données minimales pour identifier le problème\r\n  public testMinimalUpdate(clientId: string): Observable<any> {\r\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\r\n\r\n    // Test 1: Seulement le code\r\n    const minimalData1 = { code: 'TEST001' };\r\n    console.log('Test 1 - Seulement code:', minimalData1);\r\n\r\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(\r\n      catchError(() => {\r\n        console.log('Test 1 échoué, essai test 2...');\r\n\r\n        // Test 2: Seulement la syntax\r\n        const minimalData2 = { syntax: 'Test Client' };\r\n        console.log('Test 2 - Seulement syntax:', minimalData2);\r\n\r\n        return this.testRawHttpRequest(clientId, minimalData2).pipe(\r\n          catchError(() => {\r\n            console.log('Test 2 échoué, essai test 3...');\r\n\r\n            // Test 3: Objet vide\r\n            const minimalData3 = {};\r\n            console.log('Test 3 - Objet vide:', minimalData3);\r\n\r\n            return this.testRawHttpRequest(clientId, minimalData3);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode utilitaire pour tester la validation des données client\r\n  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (client.code) {\r\n      if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.length > 100) {\r\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.email.length > 100) {\r\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.telephone) {\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(client.telephone)) {\r\n        errors.push('Format de téléphone invalide');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n\r\n    console.error('=== ERREUR DE MISE À JOUR ===');\r\n    console.error('Status:', error.status);\r\n    console.error('StatusText:', error.statusText);\r\n    console.error('URL:', error.url);\r\n    console.error('Error object:', error.error);\r\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\r\n    console.error('Full error:', error);\r\n    console.error('============================');\r\n\r\n    // Analyser la structure de l'erreur en détail\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n    const traceId = error.error?.traceId;\r\n    const type = error.error?.type;\r\n\r\n    console.log('API Error:', apiError);\r\n    console.log('Validation Errors:', validationErrors);\r\n    console.log('Validation Errors type:', typeof validationErrors);\r\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\r\n    console.log('Trace ID:', traceId);\r\n    console.log('Error Type:', type);\r\n\r\n    // Essayer de capturer d'autres propriétés d'erreur\r\n    if (error.error) {\r\n      console.log('Toutes les propriétés de error.error:');\r\n      for (const key in error.error) {\r\n        console.log(`  ${key}:`, error.error[key]);\r\n      }\r\n    }\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACE,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACO,KAAK,IAAI,CAAC,IAAI,CAACC,aAAa,CAACR,MAAM,CAACO,KAAK,CAAC,EAAE;MACrD,OAAOvC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMrB,UAAU,GAA0B;MACxCgB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACU,YAAY,EAAE;MACpCJ,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBI,MAAM,EAAEV,MAAM,CAACU,MAAM,EAAEJ,IAAI,EAAE;MAC7BK,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEL,IAAI,EAAE;MACnCC,KAAK,EAAEP,MAAM,CAACO,KAAK,EAAED,IAAI,EAAE;MAC3BM,SAAS,EAAEZ,MAAM,CAACY,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAAChC,IAAI,CAACuC,IAAI,CAAS,IAAI,CAACrC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIZ,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOY,SAAS;IAClB,CAAC,CAAC,EACF/C,GAAG,CAAE+C,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF9C,UAAU,CAAC,IAAI,CAACkD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAACtB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEO,IAAI,EAAE,IAAI,CAACN,MAAM,EAAE;MAC1B,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIkB,QAAQ,GAAGvB,EAAE;IACjB,MAAMwB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACzB,EAAE,CAAC;IAC1C,IAAIwB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAAChD,UAAU,CAAC0C,KAAK,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAI0B,YAAY,EAAEH,QAAQ,GAAGG,YAAY,CAAC1B,EAAE;;IAG9C;IACA,MAAM6B,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,gBAAgBkB,QAAQ,EAAE,CAAC,CAAC;;IAGhE;IACA,MAAMO,aAAa,GAAoB,EAAE;IACzCC,MAAM,CAACC,IAAI,CAAC/B,MAAM,CAAC,CAACgC,OAAO,CAACC,GAAG,IAAG;MAChC,IAAIA,GAAG,KAAK,IAAI,IAAIjC,MAAM,CAACiC,GAA4B,CAAC,EAAEC,QAAQ,EAAE,CAAC5B,IAAI,EAAE,EAAE;QAC3EuB,aAAa,CAACI,GAA4B,CAAC,GAAGjC,MAAM,CAACiC,GAA4B,CAAC;;IAEtF,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAOnE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE,OAAO,IAAI,CAAC9B,IAAI,CAAC8D,GAAG,CAAC,GAAG,IAAI,CAAC5D,OAAO,GAAG8C,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChErC,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAACsB,MAAM,KAAK,GAAG,IAAI,CAACtB,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMqB,QAAQ,GAAG,IAAI,CAAC7D,UAAU,CAAC0C,KAAK,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKuB,QAAQ,CAAC;QACnE,OAAOgB,QAAQ,GAAG;UAAE,GAAGA,QAAQ;UAAE,GAAGT;QAAa,CAAE,GAAGA,aAAuB;;MAE/E,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,EACFhD,GAAG,CAAEsE,aAAqB,IAAI;MAC5B;MACA,MAAMrB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMqB,KAAK,GAAGtB,WAAW,CAACuB,SAAS,CAACd,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKuB,QAAQ,CAAC;MAC3D,IAAIkB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBtB,WAAW,CAACsB,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAAC9D,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACFhD,UAAU,CAAC0B,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACyC,MAAM,KAAK,CAAC,IAAKzC,KAAK,CAACyC,MAAM,KAAK,GAAG,IAAIzC,KAAK,CAACA,KAAK,EAAE8C,OAAO,EAAEC,QAAQ,CAAC,YAAY,CAAE,EAAE;QAChG,OAAO,IAAI,CAACC,mBAAmB,CAACtB,QAAQ,EAAEO,aAAa,CAAC;;MAE1D,OAAO,IAAI,CAAChC,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAiD,YAAYA,CAAC9C,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAACwE,MAAM,CAAO,GAAG,IAAI,CAACtE,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAAC8E,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF7E,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmD,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,EAAEd,MAAM,EAAE;MAChB,OAAOnE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,iBAAiB,CAAC,CAAC;;IAGvD,OAAO,IAAI,CAAC9B,IAAI,CAACwE,MAAM,CAAC,GAAG,IAAI,CAACtE,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B6B,IAAI,EAAEgC;KACP,CAAC,CAACxD,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAMiD,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACkB,IAAI,CAACuB,WAAW,CAACgC,MAAM,CAAClD,MAAM,IAAI,CAACiD,GAAG,CAACN,QAAQ,CAAC3C,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACF7B,UAAU,CAAC0B,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACyC,MAAM,KAAK,CAAC,IAAIzC,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;QAC9C,OAAO,IAAI,CAACc,qBAAqB,CAACF,GAAG,CAAC;;MAExC,OAAO,IAAI,CAACpD,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQuD,qBAAqBA,CAACF,GAAa;IACzC,MAAM/B,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,IAAI,CAAC1C,UAAU,CAACkB,IAAI,CAACuB,WAAW,CAACgC,MAAM,CAAClD,MAAM,IAAI,CAACiD,GAAG,CAACN,QAAQ,CAAC3C,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,IAAIhC,UAAU,CAACqF,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAACzD,IAAI,CAAC;UAAE2D,YAAY,EAAEL,GAAG,CAACd;QAAM,CAAE,CAAC;QAC3CiB,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACQX,mBAAmBA,CAAC7C,EAAU,EAAEyD,UAA2B;IACjE,MAAMtC,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAMqB,KAAK,GAAGtB,WAAW,CAACuB,SAAS,CAACzC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIyC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOxE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,MAAMmC,aAAa,GAAW;MAAE,GAAGrB,WAAW,CAACsB,KAAK,CAAC;MAAE,GAAGgB;IAAU,CAAE;IACtEtC,WAAW,CAACsB,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAAC9D,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;IAEtC,OAAO,IAAInD,UAAU,CAASqF,QAAQ,IAAG;MACvCC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAACzD,IAAI,CAAC4C,aAAa,CAAC;QAC5Ba,QAAQ,CAACG,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACAR,kBAAkBA,CAAA;IAChB/D,YAAY,CAACyE,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAAC9E,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI+D,IAAIA,CAAA;IACN,OAAO,IAAI,CAACjF,UAAU,CAAC0C,KAAK;EAC9B;EAEAwC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQnD,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACoD,OAAO,CAAC,OAAO,EAAE,UAASlC,CAAC;MACvE,MAAMmC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGtC,CAAC,KAAK,GAAG,GAAGmC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAAC/B,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQ1B,aAAaA,CAACD,KAAa;IACjC,MAAM2D,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAAC1C,IAAI,CAACjB,KAAK,CAAC;EACvB;EAEA;EACO4D,YAAYA,CAACpE,EAAU;IAC5BqE,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEtE,EAAE,CAAC;IAC7BqE,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAOtE,EAAE,CAAC;IAC/BqE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEtE,EAAE,CAACoC,MAAM,CAAC;IACnCiC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,iEAAiE,CAAC7C,IAAI,CAACzB,EAAE,CAAC,CAAC;IACpGqE,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC7C,IAAI,CAACzB,EAAE,CAAC,CAAC;IAErD,MAAMmB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzCiD,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEnD,WAAW,CAACiB,MAAM,CAAC;IAEtE,MAAMmC,UAAU,GAAGpD,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKA,EAAE,CAAC;IACrD,MAAM0B,YAAY,GAAGP,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;IAEzDqE,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEC,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;IAChEF,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE5C,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IAEpE,IAAI6C,UAAU,EAAE;MACdF,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEC,UAAU,CAAC;;IAE3C,IAAI7C,YAAY,EAAE;MAChB2C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE5C,YAAY,CAAC;;IAG/C;IACA2C,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,MAAMzC,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACzB,EAAE,CAAC;IACzF,MAAMwB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACzB,EAAE,CAAC;IAE1C,IAAIuB,QAAQ,GAAGvB,EAAE;IACjB,IAAIwB,MAAM,IAAI,CAACK,MAAM,EAAE;MACrB,MAAM2C,sBAAsB,GAAGrD,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAIwE,sBAAsB,IAAIA,sBAAsB,CAACxE,EAAE,KAAKA,EAAE,EAAE;QAC9DuB,QAAQ,GAAGiD,sBAAsB,CAACxE,EAAE;QACpCqE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE/C,QAAQ,CAAC;;;IAIvC,MAAMkD,WAAW,GAAG,iEAAiE,CAAChD,IAAI,CAACF,QAAQ,CAAC;IACpG8C,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE/C,QAAQ,CAAC;IAClC8C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEG,WAAW,CAAC;IAC9CJ,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA;EACOI,oBAAoBA,CAAC1E,EAAU,EAAEyD,UAA2B;IACjEY,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEtE,EAAE,CAAC;IACtBqE,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEb,UAAU,CAAC;IAEnC,MAAMkB,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAACnB,UAAU,CAAC;IACtDY,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEK,UAAU,CAACE,OAAO,CAAC;IACtD,IAAI,CAACF,UAAU,CAACE,OAAO,EAAE;MACvBR,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEK,UAAU,CAACG,MAAM,CAAC;;IAG5CT,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,MAAMS,OAAO,GAAoB,EAAE;IACnC,IAAItB,UAAU,CAACnD,IAAI,IAAImD,UAAU,CAACnD,IAAI,CAACC,IAAI,EAAE,EAAEwE,OAAO,CAACzE,IAAI,GAAGmD,UAAU,CAACnD,IAAI,CAACC,IAAI,EAAE;IACpF,IAAIkD,UAAU,CAAC9C,MAAM,IAAI8C,UAAU,CAAC9C,MAAM,CAACJ,IAAI,EAAE,EAAEwE,OAAO,CAACpE,MAAM,GAAG8C,UAAU,CAAC9C,MAAM,CAACJ,IAAI,EAAE;IAC5F,IAAIkD,UAAU,CAAC7C,SAAS,IAAI6C,UAAU,CAAC7C,SAAS,CAACL,IAAI,EAAE,EAAEwE,OAAO,CAACnE,SAAS,GAAG6C,UAAU,CAAC7C,SAAS,CAACL,IAAI,EAAE,CAACyE,WAAW,EAAE;IACtH,IAAIvB,UAAU,CAACjD,KAAK,IAAIiD,UAAU,CAACjD,KAAK,CAACD,IAAI,EAAE,EAAEwE,OAAO,CAACvE,KAAK,GAAGiD,UAAU,CAACjD,KAAK,CAACD,IAAI,EAAE,CAAC0E,WAAW,EAAE;IACtG,IAAIxB,UAAU,CAAC5C,SAAS,IAAI4C,UAAU,CAAC5C,SAAS,CAACN,IAAI,EAAE,EAAEwE,OAAO,CAAClE,SAAS,GAAG4C,UAAU,CAAC5C,SAAS,CAACN,IAAI,EAAE;IAExG8D,OAAO,CAACC,GAAG,CAAC,eAAe,EAAES,OAAO,CAAC;IACrCV,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAAC7F,OAAO,GAAGuB,EAAE,EAAE,CAAC;IAC/DqE,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQM,kBAAkBA,CAAC3E,MAAuB;IAChD,MAAM6E,MAAM,GAAa,EAAE;IAE3B;IAEA;IACA,IAAI7E,MAAM,CAACK,IAAI,KAAK4E,SAAS,EAAE;MAC7B,IAAI,CAACjF,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7CuE,MAAM,CAACK,IAAI,CAAC,4BAA4B,CAAC;OAC1C,MAAM,IAAIlF,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,CAAC,IAAInC,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,EAAE,EAAE;QAC5D0C,MAAM,CAACK,IAAI,CAAC,6CAA6C,CAAC;;;IAI9D,IAAIlF,MAAM,CAACU,MAAM,KAAKuE,SAAS,IAAIjF,MAAM,CAACU,MAAM,KAAK,IAAI,IAAIV,MAAM,CAACU,MAAM,CAACyB,MAAM,GAAG,GAAG,EAAE;MACvF0C,MAAM,CAACK,IAAI,CAAC,oDAAoD,CAAC;;IAGnE,IAAIlF,MAAM,CAACW,SAAS,KAAKsE,SAAS,IAAIjF,MAAM,CAACW,SAAS,KAAK,IAAI,EAAE;MAC/D;MACA,IAAIX,MAAM,CAACW,SAAS,CAACL,IAAI,EAAE,KAAK,EAAE,IAAIN,MAAM,CAACW,SAAS,CAACwB,MAAM,GAAG,EAAE,EAAE;QAClE0C,MAAM,CAACK,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAIlF,MAAM,CAACO,KAAK,KAAK0E,SAAS,IAAIjF,MAAM,CAACO,KAAK,KAAK,IAAI,EAAE;MACvD,IAAIP,MAAM,CAACO,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B;QACA,IAAIN,MAAM,CAACO,KAAK,CAAC4B,MAAM,GAAG,GAAG,EAAE;UAC7B0C,MAAM,CAACK,IAAI,CAAC,2CAA2C,CAAC;;;;IAK9D,IAAIlF,MAAM,CAACY,SAAS,KAAKqE,SAAS,IAAIjF,MAAM,CAACY,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIZ,MAAM,CAACY,SAAS,CAACN,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAM6E,YAAY,GAAG,6BAA6B;QAClD,IAAI,CAACA,YAAY,CAAC3D,IAAI,CAACxB,MAAM,CAACY,SAAS,CAAC,EAAE;UACxCiE,MAAM,CAACK,IAAI,CAAC,8BAA8B,CAAC;;;;IAKjD,OAAO;MACLN,OAAO,EAAEC,MAAM,CAAC1C,MAAM,KAAK,CAAC;MAC5B0C;KACD;EACH;EAEA;EACOO,WAAWA,CAACC,QAAgB,EAAE7B,UAAe;IAClDY,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAACF,YAAY,CAACkB,QAAQ,CAAC;IAE3B;IACA,IAAI,CAACZ,oBAAoB,CAACY,QAAQ,EAAE7B,UAAU,CAAC;IAE/C;IACAY,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,MAAMzC,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAAC6D,QAAQ,CAAC;IAC/F,MAAM9D,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAAC6D,QAAQ,CAAC;IAEhD,IAAI/D,QAAQ,GAAG+D,QAAQ;IACvB,IAAI9D,MAAM,IAAI,CAACK,MAAM,EAAE;MACrB,MAAMV,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMM,YAAY,GAAGP,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKgF,QAAQ,CAAC;MAC/D,IAAI5D,YAAY,EAAE;QAChBH,QAAQ,GAAGG,YAAY,CAAC1B,EAAE;QAC1BqE,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE/C,QAAQ,CAAC;;;IAIvC8C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC7F,OAAO,GAAG8C,QAAQ,EAAE,CAAC;IACxD8C,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACjF,UAAU,EAAE,CAAC;IAE1C,MAAMkG,WAAW,GAAQ,EAAE;IAC3B,IAAI9B,UAAU,CAACnD,IAAI,EAAEiF,WAAW,CAACjF,IAAI,GAAGmD,UAAU,CAACnD,IAAI,CAACC,IAAI,EAAE;IAC9D,IAAIkD,UAAU,CAAC9C,MAAM,EAAE4E,WAAW,CAAC5E,MAAM,GAAG8C,UAAU,CAAC9C,MAAM,CAACJ,IAAI,EAAE;IACpE,IAAIkD,UAAU,CAAC7C,SAAS,EAAE2E,WAAW,CAAC3E,SAAS,GAAG6C,UAAU,CAAC7C,SAAS,CAACL,IAAI,EAAE,CAAC,CAAC;IAC/E,IAAIkD,UAAU,CAACjD,KAAK,EAAE+E,WAAW,CAAC/E,KAAK,GAAGiD,UAAU,CAACjD,KAAK,CAACD,IAAI,EAAE,CAAC,CAAC;IACnE,IAAIkD,UAAU,CAAC5C,SAAS,EAAE0E,WAAW,CAAC1E,SAAS,GAAG4C,UAAU,CAAC5C,SAAS,CAACN,IAAI,EAAE;IAE7E8D,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEnF,IAAI,CAACgB,SAAS,CAACoF,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChElB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACOkB,kBAAkBA,CAACF,QAAgB,EAAE7B,UAAe;IACzDY,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,MAAMmB,GAAG,GAAG,GAAG,IAAI,CAAChH,OAAO,GAAG6G,QAAQ,EAAE;IACxC,MAAM7F,OAAO,GAAG,IAAI,CAACJ,UAAU,EAAE;IAEjCgF,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEmB,GAAG,CAAC;IACxBpB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE7E,OAAO,CAAC;IAChC4E,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEnF,IAAI,CAACgB,SAAS,CAACsD,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO,IAAI,CAAClF,IAAI,CAAC8D,GAAG,CAACoD,GAAG,EAAEhC,UAAU,EAAE;MACpChE,OAAO,EAAEA,OAAO;MAChBsB,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLxB,GAAG,CAAC8C,QAAQ,IAAG;MACbqD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEtD,QAAQ,CAAC;IAC7C,CAAC,CAAC,EACF7C,UAAU,CAAC0B,KAAK,IAAG;MACjBwE,OAAO,CAACxE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CwE,OAAO,CAACxE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACyC,MAAM,CAAC;MACtC+B,OAAO,CAACxE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MAEzC;MACA,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACO6F,iBAAiBA,CAACJ,QAAgB;IACvCjB,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAMqB,YAAY,GAAG;MAAErF,IAAI,EAAE;IAAS,CAAE;IACxC+D,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEqB,YAAY,CAAC;IAErD,OAAO,IAAI,CAACH,kBAAkB,CAACF,QAAQ,EAAEK,YAAY,CAAC,CAACjG,IAAI,CACzDvB,UAAU,CAAC,MAAK;MACdkG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA,MAAMsB,YAAY,GAAG;QAAEjF,MAAM,EAAE;MAAa,CAAE;MAC9C0D,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEsB,YAAY,CAAC;MAEvD,OAAO,IAAI,CAACJ,kBAAkB,CAACF,QAAQ,EAAEM,YAAY,CAAC,CAAClG,IAAI,CACzDvB,UAAU,CAAC,MAAK;QACdkG,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C;QACA,MAAMuB,YAAY,GAAG,EAAE;QACvBxB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEuB,YAAY,CAAC;QAEjD,OAAO,IAAI,CAACL,kBAAkB,CAACF,QAAQ,EAAEO,YAAY,CAAC;MACxD,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACOC,kBAAkBA,CAAC7F,MAAuB;IAC/C,MAAM6E,MAAM,GAAa,EAAE;IAE3B,IAAI7E,MAAM,CAACK,IAAI,EAAE;MACf,IAAIL,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,CAAC,IAAInC,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,EAAE,EAAE;QACrD0C,MAAM,CAACK,IAAI,CAAC,uDAAuD,CAAC;;;IAIxE,IAAIlF,MAAM,CAACU,MAAM,IAAIV,MAAM,CAACU,MAAM,CAACyB,MAAM,GAAG,GAAG,EAAE;MAC/C0C,MAAM,CAACK,IAAI,CAAC,uDAAuD,CAAC;;IAGtE,IAAIlF,MAAM,CAACW,SAAS,EAAE;MACpB;MACA,IAAIX,MAAM,CAACW,SAAS,CAACwB,MAAM,GAAG,EAAE,EAAE;QAChC0C,MAAM,CAACK,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAIlF,MAAM,CAACO,KAAK,EAAE;MAChB;MACA,IAAIP,MAAM,CAACO,KAAK,CAAC4B,MAAM,GAAG,GAAG,EAAE;QAC7B0C,MAAM,CAACK,IAAI,CAAC,8CAA8C,CAAC;;;IAI/D,IAAIlF,MAAM,CAACY,SAAS,EAAE;MACpB,MAAMuE,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAAC3D,IAAI,CAACxB,MAAM,CAACY,SAAS,CAAC,EAAE;QACxCiE,MAAM,CAACK,IAAI,CAAC,8BAA8B,CAAC;;;IAI/C,OAAO;MACLN,OAAO,EAAEC,MAAM,CAAC1C,MAAM,KAAK,CAAC;MAC5B0C;KACD;EACH;EAEA;EACQiB,iBAAiBA,CAAClG,KAAwB;IAChD,IAAImG,YAAY,GAAG,yCAAyC;IAE5D3B,OAAO,CAACxE,KAAK,CAAC,+BAA+B,CAAC;IAC9CwE,OAAO,CAACxE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACyC,MAAM,CAAC;IACtC+B,OAAO,CAACxE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACoG,UAAU,CAAC;IAC9C5B,OAAO,CAACxE,KAAK,CAAC,MAAM,EAAEA,KAAK,CAAC4F,GAAG,CAAC;IAChCpB,OAAO,CAACxE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CwE,OAAO,CAACxE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,GAAGkC,MAAM,CAACC,IAAI,CAACnC,KAAK,CAACA,KAAK,CAAC,GAAG,iBAAiB,CAAC;IACxFwE,OAAO,CAACxE,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnCwE,OAAO,CAACxE,KAAK,CAAC,8BAA8B,CAAC;IAE7C;IACA,MAAMqG,QAAQ,GAAGrG,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAEsG,KAAK,IAAItG,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAMuG,gBAAgB,GAAGvG,KAAK,CAACA,KAAK,EAAEiF,MAAM;IAC5C,MAAMuB,OAAO,GAAGxG,KAAK,CAACA,KAAK,EAAEwG,OAAO;IACpC,MAAMC,IAAI,GAAGzG,KAAK,CAACA,KAAK,EAAEyG,IAAI;IAE9BjC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE4B,QAAQ,CAAC;IACnC7B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,gBAAgB,CAAC;IACnD/B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAO8B,gBAAgB,CAAC;IAC/D/B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE8B,gBAAgB,GAAGrE,MAAM,CAACC,IAAI,CAACoE,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;IACjH/B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE+B,OAAO,CAAC;IACjChC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEgC,IAAI,CAAC;IAEhC;IACA,IAAIzG,KAAK,CAACA,KAAK,EAAE;MACfwE,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,KAAK,MAAMpC,GAAG,IAAIrC,KAAK,CAACA,KAAK,EAAE;QAC7BwE,OAAO,CAACC,GAAG,CAAC,KAAKpC,GAAG,GAAG,EAAErC,KAAK,CAACA,KAAK,CAACqC,GAAG,CAAC,CAAC;;;IAI9C,IAAIrC,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MACtB0D,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAII,gBAAgB,EAAE;QACpB,MAAMG,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOH,gBAAgB,KAAK,QAAQ,EAAE;UACxCrE,MAAM,CAACC,IAAI,CAACoE,gBAAgB,CAAC,CAACnE,OAAO,CAACuE,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGL,gBAAgB,CAACI,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAACxE,OAAO,CAAC2E,GAAG,IAAIL,YAAY,CAACpB,IAAI,CAAC,GAAGqB,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAACpB,IAAI,CAAC,GAAGqB,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAACnE,MAAM,GAAG,CAAC,EAAE;UAC3B4D,YAAY,IAAI,iBAAiBO,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAIX,QAAQ,EAAE;QACnBF,YAAY,IAAI,eAAeE,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAACtD,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/DoD,YAAY,GAAG,0BAA0B;QACzC,IAAInG,KAAK,CAACA,KAAK,EAAEiF,MAAM,EAAE;UACvBkB,YAAY,IAAI,0CAA0C;UAC1DjE,MAAM,CAACC,IAAI,CAACnC,KAAK,CAACA,KAAK,CAACiF,MAAM,CAAC,CAAC7C,OAAO,CAACuE,KAAK,IAAG;YAC9CR,YAAY,IAAI,KAAKQ,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACLR,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACxH,MAAM,CAACsI,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIjH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIE,QAAQ,EAAE;MACnBF,YAAY,GAAG,0BAA0BE,QAAQ,EAAE;;IAGrD,OAAOjI,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC2F,YAAY,CAAC,CAAC;EAClD;EAEQ3E,iBAAiBA,CAACxB,KAAwB;IAChD,IAAImG,YAAY,GAAG,sCAAsC;IACzD,MAAME,QAAQ,GAAGrG,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAEsG,KAAK,IAAItG,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAIqG,QAAQ,EAAE;MACZF,YAAY,GAAG,uBAAuBE,QAAQ,EAAE;KACjD,MAAM,IAAIrG,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC7B0D,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,mBAAmB;MAClC,IAAInG,KAAK,CAACA,KAAK,EAAEiF,MAAM,EAAE;QACvB,MAAMsB,gBAAgB,GAAGrE,MAAM,CAACgF,MAAM,CAAClH,KAAK,CAACA,KAAK,CAACiF,MAAM,CAAC,CAACkC,IAAI,EAAE;QACjEhB,YAAY,IAAI,aAAaI,gBAAgB,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAIhH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACxH,MAAM,CAACsI,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIjH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,mCAAmC;;IAGpD,OAAO/H,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC2F,YAAY,CAAC,CAAC;EAClD;EAEQlG,WAAWA,CAACD,KAAwB;IAC1C,IAAImG,YAAY,GAAG,mBAAmB;IACtC,MAAME,QAAQ,GAAGrG,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAEsG,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZF,YAAY,GAAGE,QAAQ;KACxB,MAAM,IAAIrG,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC7B0D,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACxH,MAAM,CAACsI,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIjH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAInG,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/B0D,YAAY,GAAG,kCAAkC;;IAGnD,OAAO/H,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC2F,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAiB,CAAA,G;qBAzoBU5I,aAAa,EAAA6I,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbnJ,aAAa;IAAAoJ,OAAA,EAAbpJ,aAAa,CAAAqJ,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}