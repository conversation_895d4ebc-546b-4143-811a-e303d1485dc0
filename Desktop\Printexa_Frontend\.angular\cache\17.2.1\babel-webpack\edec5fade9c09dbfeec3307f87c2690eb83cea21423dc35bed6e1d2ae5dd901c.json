{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n      console.log('Clients récupérés du serveur:', clients?.length || 0);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      console.error('Erreur lors de la récupération des clients:', error);\n      // En cas d'erreur de connexion, utiliser des données de test\n      if (error.status === 0) {\n        console.log('Serveur non disponible, utilisation de données de test');\n        const testClients = [{\n          id: '1',\n          code: 'CLI001',\n          syntax: 'Client Test 1',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '12345678'\n        }, {\n          id: '2',\n          code: 'CLI002',\n          syntax: 'Client Test 2',\n          matFiscal: '*********',\n          email: '<EMAIL>',\n          telephone: '87654321'\n        }];\n        this.dataChange.next(testClients);\n        return new Observable(observer => {\n          observer.next(testClients);\n          observer.complete();\n        });\n      }\n      // Pour les autres erreurs, mettre un tableau vide\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    // Validation des données d'entrée\n    if (!id || id.trim() === '') {\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\n    }\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    // Nettoyer et valider les données\n    const cleanedClient = {};\n    // Ajouter seulement les champs non vides et valides\n    if (client.code && client.code.trim()) {\n      cleanedClient.code = client.code.trim();\n      // Validation du code\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\n      }\n    }\n    if (client.syntax && client.syntax.trim()) {\n      cleanedClient.syntax = client.syntax.trim();\n      if (cleanedClient.syntax.length > 100) {\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (client.matFiscal && client.matFiscal.trim()) {\n      cleanedClient.matFiscal = client.matFiscal.trim().toUpperCase();\n      // Validation du matricule fiscal\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\n      if (!matFiscalPattern.test(cleanedClient.matFiscal)) {\n        return throwError(() => new Error('Format de matricule fiscal invalide'));\n      }\n    }\n    if (client.email && client.email.trim()) {\n      cleanedClient.email = client.email.trim().toLowerCase();\n      // Validation de l'email\n      if (!this.validateEmail(cleanedClient.email)) {\n        return throwError(() => new Error('Format d\\'email invalide'));\n      }\n      if (cleanedClient.email.length > 100) {\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\n      }\n    }\n    if (client.telephone && client.telephone.trim()) {\n      cleanedClient.telephone = client.telephone.trim();\n      // Validation du téléphone\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(cleanedClient.telephone)) {\n        return throwError(() => new Error('Format de téléphone invalide'));\n      }\n    }\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\n    }\n    console.log('Mise à jour du client:', id, cleanedClient);\n    return this.http.put(`${this.baseUrl}${id}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Si le serveur retourne NoContent (204), créer le client mis à jour localement\n      if (response.status === 204 || !response.body) {\n        const currentData = this.dataChange.value;\n        const existingClient = currentData.find(c => c.id === id);\n        if (!existingClient) {\n          throw new Error('Client non trouvé dans les données locales');\n        }\n        // Créer le client mis à jour en combinant les données existantes et les nouvelles\n        const updatedClient = {\n          ...existingClient,\n          ...cleanedClient\n        };\n        return updatedClient;\n      }\n      // Si le serveur retourne le client mis à jour\n      const updatedClient = response.body;\n      return updatedClient;\n    }), tap(updatedClient => {\n      console.log('Client mis à jour avec succès:', updatedClient);\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === id);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n      // Mettre à jour le client courant si c'est le même\n      if (this.currentClientSubject.value?.id === id) {\n        this.currentClientSubject.next(updatedClient);\n        localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n      }\n    }), catchError(error => {\n      console.error('Erreur lors de la mise à jour:', error);\n      // Si le serveur n'est pas disponible, simuler la mise à jour localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la mise à jour...');\n        return this.simulateLocalUpdate(id, cleanedClient);\n      }\n      return this.handleUpdateError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    console.log('Tentative de suppression des clients:', ids);\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log('Suppression en masse réussie');\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const updatedData = currentData.filter(client => !ids.includes(client.id));\n      this.dataChange.next(updatedData);\n    }), catchError(error => {\n      console.error('Erreur lors de la suppression en masse:', error);\n      // Si le serveur n'est pas disponible, simuler la suppression localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la suppression...');\n        return this.simulateLocalDeletion(ids);\n      }\n      // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\n      if (error.status === 500 || error.status === 404) {\n        console.log('Tentative de suppression individuelle...');\n        return this.deleteClientsIndividually(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Méthode de fallback pour supprimer les clients individuellement\n  deleteClientsIndividually(ids) {\n    const deleteRequests = ids.map(id => this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error(`Erreur lors de la suppression du client ${id}:`, error);\n      return throwError(() => error);\n    })));\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\n    return new Observable(observer => {\n      let completedCount = 0;\n      let hasError = false;\n      const errors = [];\n      deleteRequests.forEach((request, index) => {\n        request.subscribe({\n          next: () => {\n            completedCount++;\n            if (completedCount === ids.length && !hasError) {\n              // Mettre à jour les données locales\n              const currentData = this.dataChange.value;\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\n              this.dataChange.next(updatedData);\n              observer.next({\n                deletedCount: completedCount\n              });\n              observer.complete();\n            }\n          },\n          error: error => {\n            hasError = true;\n            errors.push({\n              id: ids[index],\n              error\n            });\n            if (completedCount + errors.length === ids.length) {\n              observer.error({\n                message: 'Certains clients n\\'ont pas pu être supprimés',\n                errors,\n                deletedCount: completedCount\n              });\n            }\n          }\n        });\n      });\n    });\n  }\n  // Simuler la suppression locale quand le serveur n'est pas disponible\n  simulateLocalDeletion(ids) {\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\n    // Mettre à jour les données locales\n    const currentData = this.dataChange.value;\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\n    this.dataChange.next(updatedData);\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length,\n          message: 'Suppression simulée localement (serveur non disponible)'\n        });\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\n  simulateLocalUpdate(id, updateData) {\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\n    // Trouver et mettre à jour le client dans les données locales\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\n    }\n    // Créer le client mis à jour\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    // Mettre à jour les données locales\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    // Mettre à jour le client courant si c'est le même\n    if (this.currentClientSubject.value?.id === id) {\n      this.currentClientSubject.next(updatedClient);\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n    }\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('Erreur de mise à jour détaillée:', error);\n    // Analyser la structure de l'erreur\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "console", "log", "length", "error", "status", "testClients", "id", "code", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "observer", "complete", "handleError", "getClientById", "client", "setItem", "stringify", "createClient", "Error", "trim", "validateEmail", "generateGuid", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "cleanedClient", "toUpperCase", "matFiscalPattern", "test", "toLowerCase", "phonePattern", "Object", "keys", "put", "existingClient", "find", "c", "updatedClient", "index", "findIndex", "simulateLocalUpdate", "handleUpdateError", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "updatedData", "filter", "includes", "simulateLocalDeletion", "deleteClientsIndividually", "deleteRequests", "completedCount", "<PERSON><PERSON><PERSON><PERSON>", "errors", "for<PERSON>ach", "request", "subscribe", "deletedCount", "push", "message", "setTimeout", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "re", "errorMessage", "apiError", "title", "validationErrors", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "join", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n        console.log('Clients récupérés du serveur:', clients?.length || 0);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        console.error('Erreur lors de la récupération des clients:', error);\r\n\r\n        // En cas d'erreur de connexion, utiliser des données de test\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, utilisation de données de test');\r\n          const testClients: Client[] = [\r\n            {\r\n              id: '1',\r\n              code: 'CLI001',\r\n              syntax: 'Client Test 1',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '12345678'\r\n            },\r\n            {\r\n              id: '2',\r\n              code: 'CLI002',\r\n              syntax: 'Client Test 2',\r\n              matFiscal: '*********',\r\n              email: '<EMAIL>',\r\n              telephone: '87654321'\r\n            }\r\n          ];\r\n          this.dataChange.next(testClients);\r\n          return new Observable<Client[]>(observer => {\r\n            observer.next(testClients);\r\n            observer.complete();\r\n          });\r\n        }\r\n\r\n        // Pour les autres erreurs, mettre un tableau vide\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!id || id.trim() === '') {\r\n      return throwError(() => new Error('ID du client requis pour la mise à jour'));\r\n    }\r\n\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    // Nettoyer et valider les données\r\n    const cleanedClient: UpdateClientDto = {};\r\n\r\n    // Ajouter seulement les champs non vides et valides\r\n    if (client.code && client.code.trim()) {\r\n      cleanedClient.code = client.code.trim();\r\n      // Validation du code\r\n      if (cleanedClient.code.length < 2 || cleanedClient.code.length > 20) {\r\n        return throwError(() => new Error('Le code client doit contenir entre 2 et 20 caractères'));\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.trim()) {\r\n      cleanedClient.syntax = client.syntax.trim();\r\n      if (cleanedClient.syntax.length > 100) {\r\n        return throwError(() => new Error('La raison sociale ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (client.matFiscal && client.matFiscal.trim()) {\r\n      cleanedClient.matFiscal = client.matFiscal.trim().toUpperCase();\r\n      // Validation du matricule fiscal\r\n      const matFiscalPattern = /^[0-9]{7}[A-Z]{3}[0-9]{3}$|^[A-Z0-9]{8,15}$/;\r\n      if (!matFiscalPattern.test(cleanedClient.matFiscal)) {\r\n        return throwError(() => new Error('Format de matricule fiscal invalide'));\r\n      }\r\n    }\r\n\r\n    if (client.email && client.email.trim()) {\r\n      cleanedClient.email = client.email.trim().toLowerCase();\r\n      // Validation de l'email\r\n      if (!this.validateEmail(cleanedClient.email)) {\r\n        return throwError(() => new Error('Format d\\'email invalide'));\r\n      }\r\n      if (cleanedClient.email.length > 100) {\r\n        return throwError(() => new Error('L\\'email ne peut pas dépasser 100 caractères'));\r\n      }\r\n    }\r\n\r\n    if (client.telephone && client.telephone.trim()) {\r\n      cleanedClient.telephone = client.telephone.trim();\r\n      // Validation du téléphone\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(cleanedClient.telephone)) {\r\n        return throwError(() => new Error('Format de téléphone invalide'));\r\n      }\r\n    }\r\n\r\n    // Vérifier qu'au moins un champ est fourni pour la mise à jour\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée fournie pour la mise à jour'));\r\n    }\r\n\r\n    console.log('Mise à jour du client:', id, cleanedClient);\r\n\r\n    return this.http.put(`${this.baseUrl}${id}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Si le serveur retourne NoContent (204), créer le client mis à jour localement\r\n        if (response.status === 204 || !response.body) {\r\n          const currentData = this.dataChange.value;\r\n          const existingClient = currentData.find(c => c.id === id);\r\n          if (!existingClient) {\r\n            throw new Error('Client non trouvé dans les données locales');\r\n          }\r\n\r\n          // Créer le client mis à jour en combinant les données existantes et les nouvelles\r\n          const updatedClient: Client = {\r\n            ...existingClient,\r\n            ...cleanedClient\r\n          };\r\n          return updatedClient;\r\n        }\r\n\r\n        // Si le serveur retourne le client mis à jour\r\n        const updatedClient: Client = response.body;\r\n        return updatedClient;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        console.log('Client mis à jour avec succès:', updatedClient);\r\n\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === id);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n\r\n        // Mettre à jour le client courant si c'est le même\r\n        if (this.currentClientSubject.value?.id === id) {\r\n          this.currentClientSubject.next(updatedClient);\r\n          localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la mise à jour:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la mise à jour localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la mise à jour...');\r\n          return this.simulateLocalUpdate(id, cleanedClient);\r\n        }\r\n\r\n        return this.handleUpdateError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    console.log('Tentative de suppression des clients:', ids);\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log('Suppression en masse réussie');\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n        this.dataChange.next(updatedData);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la suppression en masse:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la suppression localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la suppression...');\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n\r\n        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\r\n        if (error.status === 500 || error.status === 404) {\r\n          console.log('Tentative de suppression individuelle...');\r\n          return this.deleteClientsIndividually(ids);\r\n        }\r\n\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode de fallback pour supprimer les clients individuellement\r\n  private deleteClientsIndividually(ids: string[]): Observable<any> {\r\n    const deleteRequests = ids.map(id =>\r\n      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n        catchError(error => {\r\n          console.error(`Erreur lors de la suppression du client ${id}:`, error);\r\n          return throwError(() => error);\r\n        })\r\n      )\r\n    );\r\n\r\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\r\n    return new Observable(observer => {\r\n      let completedCount = 0;\r\n      let hasError = false;\r\n      const errors: any[] = [];\r\n\r\n      deleteRequests.forEach((request, index) => {\r\n        request.subscribe({\r\n          next: () => {\r\n            completedCount++;\r\n            if (completedCount === ids.length && !hasError) {\r\n              // Mettre à jour les données locales\r\n              const currentData = this.dataChange.value;\r\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n              this.dataChange.next(updatedData);\r\n              observer.next({ deletedCount: completedCount });\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            hasError = true;\r\n            errors.push({ id: ids[index], error });\r\n            if (completedCount + errors.length === ids.length) {\r\n              observer.error({\r\n                message: 'Certains clients n\\'ont pas pu être supprimés',\r\n                errors,\r\n                deletedCount: completedCount\r\n              });\r\n            }\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  // Simuler la suppression locale quand le serveur n'est pas disponible\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\r\n\r\n    // Mettre à jour les données locales\r\n    const currentData = this.dataChange.value;\r\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n    this.dataChange.next(updatedData);\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({\r\n          deletedCount: ids.length,\r\n          message: 'Suppression simulée localement (serveur non disponible)'\r\n        });\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\r\n\r\n    // Trouver et mettre à jour le client dans les données locales\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\r\n    }\r\n\r\n    // Créer le client mis à jour\r\n    const updatedClient: Client = {\r\n      ...currentData[index],\r\n      ...updateData\r\n    };\r\n\r\n    // Mettre à jour les données locales\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    // Mettre à jour le client courant si c'est le même\r\n    if (this.currentClientSubject.value?.id === id) {\r\n      this.currentClientSubject.next(updatedClient);\r\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n    }\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n    console.error('Erreur de mise à jour détaillée:', error);\r\n\r\n    // Analyser la structure de l'erreur\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;MACnCE,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEH,OAAO,EAAEI,MAAM,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC,EACF5B,UAAU,CAAC6B,KAAK,IAAG;MACjB,IAAI,CAACrB,YAAY,GAAG,KAAK;MACzBkB,OAAO,CAACG,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;MAEnE;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;QACrE,MAAMI,WAAW,GAAa,CAC5B;UACEC,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,EACD;UACEL,EAAE,EAAE,GAAG;UACPC,IAAI,EAAE,QAAQ;UACdC,MAAM,EAAE,eAAe;UACvBC,SAAS,EAAE,WAAW;UACtBC,KAAK,EAAE,kBAAkB;UACzBC,SAAS,EAAE;SACZ,CACF;QACD,IAAI,CAAC9B,UAAU,CAACkB,IAAI,CAACM,WAAW,CAAC;QACjC,OAAO,IAAIlC,UAAU,CAAWyC,QAAQ,IAAG;UACzCA,QAAQ,CAACb,IAAI,CAACM,WAAW,CAAC;UAC1BO,QAAQ,CAACC,QAAQ,EAAE;QACrB,CAAC,CAAC;;MAGJ;MACA,IAAI,CAAChC,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACe,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAY,aAAaA,CAACT,EAAU;IACtB,OAAO,IAAI,CAAC5B,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC2C,MAAM,IAAG;MACX5B,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACjC,oBAAoB,CAACgB,IAAI,CAACiB,MAAM,CAAC;IACxC,CAAC,CAAC,EACF1C,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAK,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACN,KAAK,IAAI,CAAC,IAAI,CAACY,aAAa,CAACN,MAAM,CAACN,KAAK,CAAC,EAAE;MACrD,OAAOtC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMjC,UAAU,GAA0B;MACxCmB,EAAE,EAAEU,MAAM,CAACV,EAAE,IAAI,IAAI,CAACiB,YAAY,EAAE;MACpChB,IAAI,EAAES,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE;MACxBb,MAAM,EAAEQ,MAAM,CAACR,MAAM,EAAEa,IAAI,EAAE;MAC7BZ,SAAS,EAAEO,MAAM,CAACP,SAAS,EAAEY,IAAI,EAAE;MACnCX,KAAK,EAAEM,MAAM,CAACN,KAAK,EAAEW,IAAI,EAAE;MAC3BV,SAAS,EAAEK,MAAM,CAACL,SAAS,EAAEU,IAAI;KAClC;IAED,OAAO,IAAI,CAAC3C,IAAI,CAAC8C,IAAI,CAAS,IAAI,CAAC5C,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIP,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOO,SAAS;IAClB,CAAC,CAAC,EACFtD,GAAG,CAAEsD,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,IAAI,CAACjD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACFrD,UAAU,CAAC,IAAI,CAACyD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAAC1B,EAAU,EAAEU,MAAuB;IAC9C;IACA,IAAI,CAACV,EAAE,IAAIA,EAAE,CAACe,IAAI,EAAE,KAAK,EAAE,EAAE;MAC3B,OAAOjD,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,yCAAyC,CAAC,CAAC;;IAG/E,IAAI,CAACJ,MAAM,EAAE;MACX,OAAO5C,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E;IACA,MAAMa,aAAa,GAAoB,EAAE;IAEzC;IACA,IAAIjB,MAAM,CAACT,IAAI,IAAIS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE,EAAE;MACrCY,aAAa,CAAC1B,IAAI,GAAGS,MAAM,CAACT,IAAI,CAACc,IAAI,EAAE;MACvC;MACA,IAAIY,aAAa,CAAC1B,IAAI,CAACL,MAAM,GAAG,CAAC,IAAI+B,aAAa,CAAC1B,IAAI,CAACL,MAAM,GAAG,EAAE,EAAE;QACnE,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIJ,MAAM,CAACR,MAAM,IAAIQ,MAAM,CAACR,MAAM,CAACa,IAAI,EAAE,EAAE;MACzCY,aAAa,CAACzB,MAAM,GAAGQ,MAAM,CAACR,MAAM,CAACa,IAAI,EAAE;MAC3C,IAAIY,aAAa,CAACzB,MAAM,CAACN,MAAM,GAAG,GAAG,EAAE;QACrC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uDAAuD,CAAC,CAAC;;;IAI/F,IAAIJ,MAAM,CAACP,SAAS,IAAIO,MAAM,CAACP,SAAS,CAACY,IAAI,EAAE,EAAE;MAC/CY,aAAa,CAACxB,SAAS,GAAGO,MAAM,CAACP,SAAS,CAACY,IAAI,EAAE,CAACa,WAAW,EAAE;MAC/D;MACA,MAAMC,gBAAgB,GAAG,6CAA6C;MACtE,IAAI,CAACA,gBAAgB,CAACC,IAAI,CAACH,aAAa,CAACxB,SAAS,CAAC,EAAE;QACnD,OAAOrC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,qCAAqC,CAAC,CAAC;;;IAI7E,IAAIJ,MAAM,CAACN,KAAK,IAAIM,MAAM,CAACN,KAAK,CAACW,IAAI,EAAE,EAAE;MACvCY,aAAa,CAACvB,KAAK,GAAGM,MAAM,CAACN,KAAK,CAACW,IAAI,EAAE,CAACgB,WAAW,EAAE;MACvD;MACA,IAAI,CAAC,IAAI,CAACf,aAAa,CAACW,aAAa,CAACvB,KAAK,CAAC,EAAE;QAC5C,OAAOtC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,0BAA0B,CAAC,CAAC;;MAEhE,IAAIa,aAAa,CAACvB,KAAK,CAACR,MAAM,GAAG,GAAG,EAAE;QACpC,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8CAA8C,CAAC,CAAC;;;IAItF,IAAIJ,MAAM,CAACL,SAAS,IAAIK,MAAM,CAACL,SAAS,CAACU,IAAI,EAAE,EAAE;MAC/CY,aAAa,CAACtB,SAAS,GAAGK,MAAM,CAACL,SAAS,CAACU,IAAI,EAAE;MACjD;MACA,MAAMiB,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAACF,IAAI,CAACH,aAAa,CAACtB,SAAS,CAAC,EAAE;QAC/C,OAAOvC,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,8BAA8B,CAAC,CAAC;;;IAItE;IACA,IAAImB,MAAM,CAACC,IAAI,CAACP,aAAa,CAAC,CAAC/B,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,2CAA2C,CAAC,CAAC;;IAGjFpB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEK,EAAE,EAAE2B,aAAa,CAAC;IAExD,OAAO,IAAI,CAACvD,IAAI,CAAC+D,GAAG,CAAC,GAAG,IAAI,CAAC7D,OAAO,GAAG0B,EAAE,EAAE,EAAE2B,aAAa,EAAE;MAC1DrC,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BiC,OAAO,EAAE;KACV,CAAC,CAAC5B,IAAI,CACLtB,GAAG,CAAEmD,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAACtB,MAAM,KAAK,GAAG,IAAI,CAACsB,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;QACzC,MAAMY,cAAc,GAAGb,WAAW,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKA,EAAE,CAAC;QACzD,IAAI,CAACoC,cAAc,EAAE;UACnB,MAAM,IAAItB,KAAK,CAAC,4CAA4C,CAAC;;QAG/D;QACA,MAAMyB,aAAa,GAAW;UAC5B,GAAGH,cAAc;UACjB,GAAGT;SACJ;QACD,OAAOY,aAAa;;MAGtB;MACA,MAAMA,aAAa,GAAWnB,QAAQ,CAACE,IAAI;MAC3C,OAAOiB,aAAa;IACtB,CAAC,CAAC,EACFxE,GAAG,CAAEwE,aAAqB,IAAI;MAC5B7C,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE4C,aAAa,CAAC;MAE5D;MACA,MAAMhB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMgB,KAAK,GAAGjB,WAAW,CAACkB,SAAS,CAACH,CAAC,IAAIA,CAAC,CAACtC,EAAE,KAAKA,EAAE,CAAC;MACrD,IAAIwC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBjB,WAAW,CAACiB,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAAChE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;;MAGxC;MACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;QAC9C,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAAC8C,aAAa,CAAC;QAC7CzD,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAAC2B,aAAa,CAAC,CAAC;;IAExE,CAAC,CAAC,EACFvE,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MAEtD;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAAC+C,mBAAmB,CAAC1C,EAAE,EAAE2B,aAAa,CAAC;;MAGpD,OAAO,IAAI,CAACgB,iBAAiB,CAAC9C,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA+C,YAAYA,CAAC5C,EAAU;IACrB,OAAO,IAAI,CAAC5B,IAAI,CAACyE,MAAM,CAAO,GAAG,IAAI,CAACvE,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAAC+E,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF9E,UAAU,CAAC,IAAI,CAACwC,WAAW,CAAC,CAC7B;EACH;EAEA;EACAuC,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACpD,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAO9B,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrFpB,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEqD,GAAG,CAAC;IAEzD,OAAO,IAAI,CAAC5E,IAAI,CAACyE,MAAM,CAAC,GAAG,IAAI,CAACvE,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BoC,IAAI,EAAE0B;KACP,CAAC,CAACzD,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP2B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;MACA,MAAM4B,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;MACzC,MAAMyB,WAAW,GAAG1B,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAI,CAACsC,GAAG,CAACG,QAAQ,CAACzC,MAAM,CAACV,EAAE,CAAC,CAAC;MAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACwD,WAAW,CAAC;IACnC,CAAC,CAAC,EACFjF,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAE/D;MACA,IAAIA,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;QACtBJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACyD,qBAAqB,CAACJ,GAAG,CAAC;;MAGxC;MACA,IAAInD,KAAK,CAACC,MAAM,KAAK,GAAG,IAAID,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;QAChDJ,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI,CAAC0D,yBAAyB,CAACL,GAAG,CAAC;;MAG5C,OAAO,IAAI,CAACxC,WAAW,CAACX,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQwD,yBAAyBA,CAACL,GAAa;IAC7C,MAAMM,cAAc,GAAGN,GAAG,CAAC/E,GAAG,CAAC+B,EAAE,IAC/B,IAAI,CAAC5B,IAAI,CAACyE,MAAM,CAAC,GAAG,IAAI,CAACvE,OAAO,GAAG0B,EAAE,EAAE,EAAE;MAAEV,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3EvB,UAAU,CAAC6B,KAAK,IAAG;MACjBH,OAAO,CAACG,KAAK,CAAC,2CAA2CG,EAAE,GAAG,EAAEH,KAAK,CAAC;MACtE,OAAO/B,UAAU,CAAC,MAAM+B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACF;IAED;IACA,OAAO,IAAIhC,UAAU,CAACyC,QAAQ,IAAG;MAC/B,IAAIiD,cAAc,GAAG,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,MAAMC,MAAM,GAAU,EAAE;MAExBH,cAAc,CAACI,OAAO,CAAC,CAACC,OAAO,EAAEnB,KAAK,KAAI;QACxCmB,OAAO,CAACC,SAAS,CAAC;UAChBnE,IAAI,EAAEA,CAAA,KAAK;YACT8D,cAAc,EAAE;YAChB,IAAIA,cAAc,KAAKP,GAAG,CAACpD,MAAM,IAAI,CAAC4D,QAAQ,EAAE;cAC9C;cACA,MAAMjC,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;cACzC,MAAMyB,WAAW,GAAG1B,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAI,CAACsC,GAAG,CAACG,QAAQ,CAACzC,MAAM,CAACV,EAAE,CAAC,CAAC;cAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACwD,WAAW,CAAC;cACjC3C,QAAQ,CAACb,IAAI,CAAC;gBAAEoE,YAAY,EAAEN;cAAc,CAAE,CAAC;cAC/CjD,QAAQ,CAACC,QAAQ,EAAE;;UAEvB,CAAC;UACDV,KAAK,EAAGA,KAAK,IAAI;YACf2D,QAAQ,GAAG,IAAI;YACfC,MAAM,CAACK,IAAI,CAAC;cAAE9D,EAAE,EAAEgD,GAAG,CAACR,KAAK,CAAC;cAAE3C;YAAK,CAAE,CAAC;YACtC,IAAI0D,cAAc,GAAGE,MAAM,CAAC7D,MAAM,KAAKoD,GAAG,CAACpD,MAAM,EAAE;cACjDU,QAAQ,CAACT,KAAK,CAAC;gBACbkE,OAAO,EAAE,+CAA+C;gBACxDN,MAAM;gBACNI,YAAY,EAAEN;eACf,CAAC;;UAEN;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQH,qBAAqBA,CAACJ,GAAa;IACzCtD,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEqD,GAAG,CAAC;IAErE;IACA,MAAMzB,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMyB,WAAW,GAAG1B,WAAW,CAAC2B,MAAM,CAACxC,MAAM,IAAI,CAACsC,GAAG,CAACG,QAAQ,CAACzC,MAAM,CAACV,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACzB,UAAU,CAACkB,IAAI,CAACwD,WAAW,CAAC;IAEjC;IACA,OAAO,IAAIpF,UAAU,CAACyC,QAAQ,IAAG;MAC/B0D,UAAU,CAAC,MAAK;QACd1D,QAAQ,CAACb,IAAI,CAAC;UACZoE,YAAY,EAAEb,GAAG,CAACpD,MAAM;UACxBmE,OAAO,EAAE;SACV,CAAC;QACFzD,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACQmC,mBAAmBA,CAAC1C,EAAU,EAAEiE,UAA2B;IACjEvE,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEK,EAAE,EAAEiE,UAAU,CAAC;IAE9E;IACA,MAAM1C,WAAW,GAAG,IAAI,CAAChD,UAAU,CAACiD,KAAK;IACzC,MAAMgB,KAAK,GAAGjB,WAAW,CAACkB,SAAS,CAAC/B,MAAM,IAAIA,MAAM,CAACV,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIwC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAO1E,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E;IACA,MAAMyB,aAAa,GAAW;MAC5B,GAAGhB,WAAW,CAACiB,KAAK,CAAC;MACrB,GAAGyB;KACJ;IAED;IACA1C,WAAW,CAACiB,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAAChE,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAG8B,WAAW,CAAC,CAAC;IAEtC;IACA,IAAI,IAAI,CAAC9C,oBAAoB,CAAC+C,KAAK,EAAExB,EAAE,KAAKA,EAAE,EAAE;MAC9C,IAAI,CAACvB,oBAAoB,CAACgB,IAAI,CAAC8C,aAAa,CAAC;MAC7CzD,YAAY,CAAC6B,OAAO,CAAC,eAAe,EAAE3B,IAAI,CAAC4B,SAAS,CAAC2B,aAAa,CAAC,CAAC;;IAGtE;IACA,OAAO,IAAI1E,UAAU,CAASyC,QAAQ,IAAG;MACvC0D,UAAU,CAAC,MAAK;QACd1D,QAAQ,CAACb,IAAI,CAAC8C,aAAa,CAAC;QAC5BjC,QAAQ,CAACC,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACAuC,kBAAkBA,CAAA;IAChBhE,YAAY,CAACoF,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACzF,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI0E,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC5F,UAAU,CAACiD,KAAK;EAC9B;EAEA4C,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQpD,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACqD,OAAO,CAAC,OAAO,EAAE,UAAShC,CAAC;MACvE,MAAMiC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGpC,CAAC,KAAK,GAAG,GAAGiC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQ3D,aAAaA,CAACZ,KAAa;IACjC,MAAMwE,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAAC9C,IAAI,CAAC1B,KAAK,CAAC;EACvB;EAEA;EACQuC,iBAAiBA,CAAC9C,KAAwB;IAChD,IAAIgF,YAAY,GAAG,yCAAyC;IAC5DnF,OAAO,CAACG,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IAExD;IACA,MAAMiF,QAAQ,GAAGjF,KAAK,CAACA,KAAK,EAAEkE,OAAO,IAAIlE,KAAK,CAACA,KAAK,EAAEkF,KAAK,IAAIlF,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAMmF,gBAAgB,GAAGnF,KAAK,CAACA,KAAK,EAAE4D,MAAM;IAE5C,IAAI5D,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB+E,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAIG,gBAAgB,EAAE;QACpB,MAAMC,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOD,gBAAgB,KAAK,QAAQ,EAAE;UACxC/C,MAAM,CAACC,IAAI,CAAC8C,gBAAgB,CAAC,CAACtB,OAAO,CAACwB,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGH,gBAAgB,CAACE,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAACzB,OAAO,CAAC4B,GAAG,IAAIL,YAAY,CAACnB,IAAI,CAAC,GAAGoB,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAACnB,IAAI,CAAC,GAAGoB,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAACrF,MAAM,GAAG,CAAC,EAAE;UAC3BiF,YAAY,IAAI,iBAAiBI,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAIT,QAAQ,EAAE;QACnBD,YAAY,IAAI,eAAeC,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC3B,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/D0B,YAAY,GAAG,0BAA0B;QACzC,IAAIhF,KAAK,CAACA,KAAK,EAAE4D,MAAM,EAAE;UACvBoB,YAAY,IAAI,0CAA0C;UAC1D5C,MAAM,CAACC,IAAI,CAACrC,KAAK,CAACA,KAAK,CAAC4D,MAAM,CAAC,CAACC,OAAO,CAACwB,KAAK,IAAG;YAC9CL,YAAY,IAAI,KAAKK,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACLL,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACxG,MAAM,CAACmH,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3F,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIC,QAAQ,EAAE;MACnBD,YAAY,GAAG,0BAA0BC,QAAQ,EAAE;;IAGrD,OAAOhH,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC+D,YAAY,CAAC,CAAC;EAClD;EAEQpD,iBAAiBA,CAAC5B,KAAwB;IAChD,IAAIgF,YAAY,GAAG,sCAAsC;IACzD,MAAMC,QAAQ,GAAGjF,KAAK,CAACA,KAAK,EAAEkE,OAAO,IAAIlE,KAAK,CAACA,KAAK,EAAEkF,KAAK,IAAIlF,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAIiF,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAIjF,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7B+E,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,mBAAmB;MAClC,IAAIhF,KAAK,CAACA,KAAK,EAAE4D,MAAM,EAAE;QACvB,MAAMuB,gBAAgB,GAAG/C,MAAM,CAACwD,MAAM,CAAC5F,KAAK,CAACA,KAAK,CAAC4D,MAAM,CAAC,CAACiC,IAAI,EAAE;QACjEb,YAAY,IAAI,aAAaG,gBAAgB,CAACO,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAI1F,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAACxG,MAAM,CAACmH,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3F,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,mCAAmC;;IAGpD,OAAO/G,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC+D,YAAY,CAAC,CAAC;EAClD;EAEQrE,WAAWA,CAACX,KAAwB;IAC1C,IAAIgF,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAGjF,KAAK,CAACA,KAAK,EAAEkE,OAAO,IAAIlE,KAAK,CAACA,KAAK,EAAEkF,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAIjF,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MAC7B+E,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACxG,MAAM,CAACmH,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI3F,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAIhF,KAAK,CAACC,MAAM,KAAK,GAAG,EAAE;MAC/B+E,YAAY,GAAG,kCAAkC;;IAGnD,OAAO/G,UAAU,CAAC,MAAM,IAAIgD,KAAK,CAAC+D,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAc,CAAA,G;qBA3hBUzH,aAAa,EAAA0H,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbhI,aAAa;IAAAiI,OAAA,EAAbjI,aAAa,CAAAkI,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}