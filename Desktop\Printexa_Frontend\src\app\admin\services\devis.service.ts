import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { Devis, CreateDevisDto } from '../Model/Devis';

@Injectable({
  providedIn: 'root'
})
export class DevisService {
  private apiUrl = 'https://localhost:5001/api/Devis'; // Adaptez selon votre configuration

  constructor(private http: HttpClient) { }

  // Récupérer tous les devis
  getAllDevis(): Observable<Devis[]> {
    return this.http.get<Devis[]>(this.apiUrl)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Récupérer un devis par son ID
  getDevisById(id: string): Observable<Devis> {
    return this.http.get<Devis>(`${this.apiUrl}/${id}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Rechercher des devis par référence
  searchDevisByReference(reference: string): Observable<Devis[]> {
    return this.http.get<Devis[]>(`${this.apiUrl}/search?reference=${encodeURIComponent(reference)}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Récupérer un devis par référence exacte
  getDevisByReference(reference: string): Observable<Devis> {
    return this.http.get<Devis>(`${this.apiUrl}/reference/${encodeURIComponent(reference)}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Créer un nouveau devis
  createDevis(devisDto: CreateDevisDto): Observable<Devis> {
    return this.http.post<Devis>(this.apiUrl, devisDto)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Mettre à jour un devis
  updateDevis(id: string, devis: Devis): Observable<void> {
    return this.http.put<void>(`${this.apiUrl}/${id}`, devis)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Supprimer un devis
  deleteDevis(id: string): Observable<void> {
    return this.http.delete<void>(`${this.apiUrl}/${id}`)
      .pipe(
        catchError(this.handleError)
      );
  }

  // Gestion des erreurs
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Une erreur inconnue est survenue';
    if (error.error instanceof ErrorEvent) {
      // Erreur côté client
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Erreur côté serveur
      if (error.status === 404) {
        errorMessage = 'Devis non trouvé';
      } else if (error.status === 400) {
        errorMessage = 'Données invalides';
      } else if (error.status === 409) {
        errorMessage = 'Un devis avec cet ID existe déjà';
      } else if (error.status === 500) {
        errorMessage = 'Erreur serveur interne';
      }
      errorMessage = `${errorMessage}: ${error.error}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }
}