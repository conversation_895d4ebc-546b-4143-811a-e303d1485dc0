{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(clients);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders()\n    }).pipe(tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) actualId = clientByCode.id;\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\n    }\n    // Nettoyer les données en utilisant le même format que createClient\n    const cleanedClient = {};\n    // Ajouter seulement les champs non vides\n    if (client.code?.trim()) {\n      cleanedClient.code = client.code.trim();\n    }\n    if (client.syntax?.trim()) {\n      cleanedClient.syntax = client.syntax.trim();\n    }\n    if (client.matFiscal?.trim()) {\n      cleanedClient.matFiscal = client.matFiscal.trim();\n    }\n    if (client.email?.trim()) {\n      cleanedClient.email = client.email.trim();\n    }\n    if (client.telephone?.trim()) {\n      cleanedClient.telephone = client.telephone.trim();\n    }\n    console.log('Données avant nettoyage:', client);\n    console.log('Données après nettoyage:', cleanedClient);\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    // Validation supplémentaire\n    if (cleanedClient.code && cleanedClient.code.length < 2) {\n      return throwError(() => new Error('Le code client doit contenir au moins 2 caractères'));\n    }\n    if (cleanedClient.email && !cleanedClient.email.includes('@')) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    console.log('=== SERVICE CLIENT UPDATE ===');\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Données nettoyées à envoyer:', cleanedClient);\n    console.log('Headers:', this.getHeaders());\n    console.log('==============================');\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Mise à jour réussie côté serveur');\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = {\n          ...currentData[index],\n          ...cleanedClient\n        };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(error => {\n      console.error('=== ERREUR SERVICE CLIENT ===');\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      console.error('Message:', error.message);\n      console.error('URL:', error.url);\n      console.error('Headers sent:', this.getHeaders());\n      console.error('Data sent:', cleanedClient);\n      console.error('==============================');\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || error.status === 400 && error.error?.message?.includes('validation')) {\n        console.log('Activation du mode simulation locale');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId);\n    }));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    }), catchError(error => {\n      // Simulation locale en cas d'erreur\n      if (error.status === 0 || error.status === 500) {\n        return this.simulateLocalDeletion(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next(currentData.filter(client => client.id !== id));\n    }), catchError(this.handleError));\n  }\n  // Simulation locale de mise à jour\n  simulateLocalUpdate(id, updateData) {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Simulation locale de suppression\n  simulateLocalDeletion(ids) {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length\n        });\n        observer.complete();\n      }, 300);\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "code", "trim", "Error", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "email", "telephone", "post", "newClient", "currentData", "value", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "cleanedClient", "console", "log", "Object", "keys", "length", "includes", "put", "index", "findIndex", "updatedClient", "status", "message", "url", "simulateLocalUpdate", "deleteSelectedClients", "ids", "delete", "body", "filter", "simulateLocalDeletion", "deleteClient", "updateData", "observer", "setTimeout", "complete", "deletedCount", "clearCurrentClient", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "toString", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.optimized.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\nimport { Router } from '@angular/router';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class ClientService {\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\n  private currentClientSubject: BehaviorSubject<Client | null>;\n  public currentClient$: Observable<Client | null>;\n  dataChange = new BehaviorSubject<Client[]>([]);\n  dialogData!: Client;\n  isTblLoading = true;\n\n  constructor(private http: HttpClient, private router: Router) {\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n\n  private getClientFromStorage(): Client | null {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n\n  private getHeaders(): HttpHeaders {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\n    });\n  }\n\n  // Récupérer tous les clients\n  getAllClients(): Observable<Client[]> {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\n      tap(clients => {\n        this.isTblLoading = false; // Désactivation du loading\n        this.dataChange.next(clients);\n      }),\n      catchError(error => {\n        this.isTblLoading = false; // Désactivation en cas d'erreur\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Récupérer un client par son ID\n  getClientById(id: string): Observable<Client> {\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(client => {\n        localStorage.setItem('currentClient', JSON.stringify(client));\n        this.currentClientSubject.next(client);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Créer un nouveau client\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\n    if (!client?.code?.trim()) {\n      return throwError(() => new Error('Code client requis'));\n    }\n\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n\n    return this.http.post<Client>(this.baseUrl, clientData, { headers: this.getHeaders() }).pipe(\n      tap(newClient => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next([...currentData, newClient]);\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Mettre à jour un client\nupdateClient(id: string, client: UpdateClientDto): Observable<Client> {\n  if (!id?.trim() || !client) {\n    return throwError(() => new Error('ID et données client requis'));\n  }\n\n  // Résoudre l'ID si c'est un code client\n  let actualId = id;\n  const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n  if (isCode) {\n    const clientByCode = this.dataChange.value.find(c => c.code === id);\n    if (clientByCode) actualId = clientByCode.id;\n  }\n\n  // Valider que l'ID final est un GUID\n  const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n  if (!isGuid) {\n    return throwError(() => new Error(`ID invalide: ${actualId}`));\n  }\n\n  // Nettoyer les données en utilisant le même format que createClient\n  const cleanedClient: any = {};\n\n  // Ajouter seulement les champs non vides\n  if (client.code?.trim()) {\n    cleanedClient.code = client.code.trim();\n  }\n  if (client.syntax?.trim()) {\n    cleanedClient.syntax = client.syntax.trim();\n  }\n  if (client.matFiscal?.trim()) {\n    cleanedClient.matFiscal = client.matFiscal.trim();\n  }\n  if (client.email?.trim()) {\n    cleanedClient.email = client.email.trim();\n  }\n  if (client.telephone?.trim()) {\n    cleanedClient.telephone = client.telephone.trim();\n  }\n\n  console.log('Données avant nettoyage:', client);\n  console.log('Données après nettoyage:', cleanedClient);\n\n  if (Object.keys(cleanedClient).length === 0) {\n    return throwError(() => new Error('Aucune donnée à mettre à jour'));\n  }\n\n  // Validation supplémentaire\n  if (cleanedClient.code && cleanedClient.code.length < 2) {\n    return throwError(() => new Error('Le code client doit contenir au moins 2 caractères'));\n  }\n  if (cleanedClient.email && !cleanedClient.email.includes('@')) {\n    return throwError(() => new Error('Format d\\'email invalide'));\n  }\n\n  console.log('=== SERVICE CLIENT UPDATE ===');\n  console.log('URL finale:', `${this.baseUrl}${actualId}`);\n  console.log('Données nettoyées à envoyer:', cleanedClient);\n  console.log('Headers:', this.getHeaders());\n  console.log('==============================');\n\n  return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n    headers: this.getHeaders()\n  }).pipe(\n    tap(() => {\n      console.log('Mise à jour réussie côté serveur');\n      // Mettre à jour les données locales après une réponse réussie (204)\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        const updatedClient = { ...currentData[index], ...cleanedClient };\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }),\n    catchError(error => {\n      console.error('=== ERREUR SERVICE CLIENT ===');\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      console.error('Message:', error.message);\n      console.error('URL:', error.url);\n      console.error('Headers sent:', this.getHeaders());\n      console.error('Data sent:', cleanedClient);\n      console.error('==============================');\n\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || (error.status === 400 && error.error?.message?.includes('validation'))) {\n        console.log('Activation du mode simulation locale');\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }),\n    // Retourner le client mis à jour même si la réponse est 204\n    map(() => {\n      const currentData = this.dataChange.value;\n      return currentData.find(c => c.id === actualId)!;\n    })\n  );\n}\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids: string[]): Observable<any> {\n    if (!ids?.length) {\n      return throwError(() => new Error('Aucun ID fourni'));\n    }\n\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n      }),\n      catchError(error => {\n        // Simulation locale en cas d'erreur\n        if (error.status === 0 || error.status === 500) {\n          return this.simulateLocalDeletion(ids);\n        }\n        return this.handleError(error);\n      })\n    );\n  }\n\n  // Supprimer un client\n  deleteClient(id: string): Observable<void> {\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\n      tap(() => {\n        const currentData = this.dataChange.value;\n        this.dataChange.next(currentData.filter(client => client.id !== id));\n      }),\n      catchError(this.handleError)\n    );\n  }\n\n  // Simulation locale de mise à jour\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    \n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé'));\n    }\n\n    const updatedClient: Client = { ...currentData[index], ...updateData };\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    \n    return new Observable<Client>(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Simulation locale de suppression\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\n    const currentData = this.dataChange.value;\n    this.dataChange.next(currentData.filter(client => !ids.includes(client.id)));\n    \n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({ deletedCount: ids.length });\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  // Effacer le client courant\n  clearCurrentClient(): void {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n\n  get data(): Client[] {\n    return this.dataChange.value;\n  }\n\n  getDialogData() {\n    return this.dialogData;\n  }\n\n  // Méthodes utilitaires\n  private generateGuid(): string {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\n      return v.toString(16);\n    });\n  }\n}\n"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,CAAC;IAC/B,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC,IAAI,CAACA,MAAM,EAAEI,IAAI,EAAEC,IAAI,EAAE,EAAE;MACzB,OAAOrC,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,oBAAoB,CAAC,CAAC;;IAG1D,MAAMvB,UAAU,GAAG;MACjBgB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACQ,YAAY,EAAE;MACpCH,IAAI,EAAEJ,MAAM,CAACI,IAAI,CAACC,IAAI,EAAE;MACxBG,MAAM,EAAER,MAAM,CAACQ,MAAM,EAAEH,IAAI,EAAE;MAC7BI,SAAS,EAAET,MAAM,CAACS,SAAS,EAAEJ,IAAI,EAAE;MACnCK,KAAK,EAAEV,MAAM,CAACU,KAAK,EAAEL,IAAI,EAAE;MAC3BM,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAAC/B,IAAI,CAACsC,IAAI,CAAS,IAAI,CAACpC,OAAO,EAAEO,UAAU,EAAE;MAAES,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC1FxB,GAAG,CAAC4C,SAAS,IAAG;MACd,MAAMC,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;MACzC,IAAI,CAACtC,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGmB,WAAW,EAAED,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF3C,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACFmB,YAAYA,CAACjB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEM,IAAI,EAAE,IAAI,CAACL,MAAM,EAAE;MAC1B,OAAOhC,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIW,QAAQ,GAAGlB,EAAE;IACjB,MAAMmB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACpB,EAAE,CAAC;IAC1C,IAAImB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAAC3C,UAAU,CAACsC,KAAK,CAACM,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClB,IAAI,KAAKL,EAAE,CAAC;MACnE,IAAIqB,YAAY,EAAEH,QAAQ,GAAGG,YAAY,CAACrB,EAAE;;IAG9C;IACA,MAAMwB,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAOvD,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,gBAAgBW,QAAQ,EAAE,CAAC,CAAC;;IAGhE;IACA,MAAMO,aAAa,GAAQ,EAAE;IAE7B;IACA,IAAIxB,MAAM,CAACI,IAAI,EAAEC,IAAI,EAAE,EAAE;MACvBmB,aAAa,CAACpB,IAAI,GAAGJ,MAAM,CAACI,IAAI,CAACC,IAAI,EAAE;;IAEzC,IAAIL,MAAM,CAACQ,MAAM,EAAEH,IAAI,EAAE,EAAE;MACzBmB,aAAa,CAAChB,MAAM,GAAGR,MAAM,CAACQ,MAAM,CAACH,IAAI,EAAE;;IAE7C,IAAIL,MAAM,CAACS,SAAS,EAAEJ,IAAI,EAAE,EAAE;MAC5BmB,aAAa,CAACf,SAAS,GAAGT,MAAM,CAACS,SAAS,CAACJ,IAAI,EAAE;;IAEnD,IAAIL,MAAM,CAACU,KAAK,EAAEL,IAAI,EAAE,EAAE;MACxBmB,aAAa,CAACd,KAAK,GAAGV,MAAM,CAACU,KAAK,CAACL,IAAI,EAAE;;IAE3C,IAAIL,MAAM,CAACW,SAAS,EAAEN,IAAI,EAAE,EAAE;MAC5BmB,aAAa,CAACb,SAAS,GAAGX,MAAM,CAACW,SAAS,CAACN,IAAI,EAAE;;IAGnDoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE1B,MAAM,CAAC;IAC/CyB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEF,aAAa,CAAC;IAEtD,IAAIG,MAAM,CAACC,IAAI,CAACJ,aAAa,CAAC,CAACK,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO7D,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE;IACA,IAAIkB,aAAa,CAACpB,IAAI,IAAIoB,aAAa,CAACpB,IAAI,CAACyB,MAAM,GAAG,CAAC,EAAE;MACvD,OAAO7D,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,oDAAoD,CAAC,CAAC;;IAE1F,IAAIkB,aAAa,CAACd,KAAK,IAAI,CAACc,aAAa,CAACd,KAAK,CAACoB,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC7D,OAAO9D,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhEmB,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5CD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAAClD,OAAO,GAAGyC,QAAQ,EAAE,CAAC;IACxDQ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEF,aAAa,CAAC;IAC1DC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACtC,UAAU,EAAE,CAAC;IAC1CqC,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;IAE7C,OAAO,IAAI,CAACpD,IAAI,CAACyD,GAAG,CAAC,GAAG,IAAI,CAACvD,OAAO,GAAGyC,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChEhC,OAAO,EAAE,IAAI,CAACJ,UAAU;KACzB,CAAC,CAACK,IAAI,CACLxB,GAAG,CAAC,MAAK;MACPwD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MAC/C;MACA,MAAMZ,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;MACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAACX,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKkB,QAAQ,CAAC;MAC3D,IAAIe,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,MAAME,aAAa,GAAG;UAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;UAAE,GAAGR;QAAa,CAAE;QACjEV,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;QAClC,IAAI,CAACzD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGmB,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACF5C,UAAU,CAAC0B,KAAK,IAAG;MACjB6B,OAAO,CAAC7B,KAAK,CAAC,+BAA+B,CAAC;MAC9C6B,OAAO,CAAC7B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACuC,MAAM,CAAC;MACtCV,OAAO,CAAC7B,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MACzC6B,OAAO,CAAC7B,KAAK,CAAC,UAAU,EAAEA,KAAK,CAACwC,OAAO,CAAC;MACxCX,OAAO,CAAC7B,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACyC,GAAG,CAAC;MAChCZ,OAAO,CAAC7B,KAAK,CAAC,eAAe,EAAE,IAAI,CAACR,UAAU,EAAE,CAAC;MACjDqC,OAAO,CAAC7B,KAAK,CAAC,YAAY,EAAE4B,aAAa,CAAC;MAC1CC,OAAO,CAAC7B,KAAK,CAAC,gCAAgC,CAAC;MAE/C;MACA,IAAIA,KAAK,CAACuC,MAAM,KAAK,CAAC,IAAKvC,KAAK,CAACuC,MAAM,KAAK,GAAG,IAAIvC,KAAK,CAACA,KAAK,EAAEwC,OAAO,EAAEN,QAAQ,CAAC,YAAY,CAAE,EAAE;QAChGL,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;QACnD,OAAO,IAAI,CAACY,mBAAmB,CAACrB,QAAQ,EAAEO,aAAa,CAAC;;MAE1D,OAAO,IAAI,CAAC3B,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC;IACF;IACAzB,GAAG,CAAC,MAAK;MACP,MAAM2C,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;MACzC,OAAOD,WAAW,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACvB,EAAE,KAAKkB,QAAQ,CAAE;IAClD,CAAC,CAAC,CACH;EACH;EACE;EACAsB,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,EAAEX,MAAM,EAAE;MAChB,OAAO7D,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,iBAAiB,CAAC,CAAC;;IAGvD,OAAO,IAAI,CAAChC,IAAI,CAACmE,MAAM,CAAC,GAAG,IAAI,CAACjE,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1BsD,IAAI,EAAEF;KACP,CAAC,CAAC/C,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP,MAAM6C,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;MACzC,IAAI,CAACtC,UAAU,CAACkB,IAAI,CAACmB,WAAW,CAAC6B,MAAM,CAAC3C,MAAM,IAAI,CAACwC,GAAG,CAACV,QAAQ,CAAC9B,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAC9E,CAAC,CAAC,EACF7B,UAAU,CAAC0B,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACuC,MAAM,KAAK,CAAC,IAAIvC,KAAK,CAACuC,MAAM,KAAK,GAAG,EAAE;QAC9C,OAAO,IAAI,CAACS,qBAAqB,CAACJ,GAAG,CAAC;;MAExC,OAAO,IAAI,CAAC3C,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAiD,YAAYA,CAAC9C,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAACmE,MAAM,CAAO,GAAG,IAAI,CAACjE,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,MAAM6C,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;MACzC,IAAI,CAACtC,UAAU,CAACkB,IAAI,CAACmB,WAAW,CAAC6B,MAAM,CAAC3C,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC,CAAC;IACtE,CAAC,CAAC,EACF7B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACQyC,mBAAmBA,CAACvC,EAAU,EAAE+C,UAA2B;IACjE,MAAMhC,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;IACzC,MAAMiB,KAAK,GAAGlB,WAAW,CAACmB,SAAS,CAACjC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIiC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOhE,UAAU,CAAC,MAAM,IAAIsC,KAAK,CAAC,mBAAmB,CAAC,CAAC;;IAGzD,MAAM4B,aAAa,GAAW;MAAE,GAAGpB,WAAW,CAACkB,KAAK,CAAC;MAAE,GAAGc;IAAU,CAAE;IACtEhC,WAAW,CAACkB,KAAK,CAAC,GAAGE,aAAa;IAClC,IAAI,CAACzD,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGmB,WAAW,CAAC,CAAC;IAEtC,OAAO,IAAI/C,UAAU,CAASgF,QAAQ,IAAG;MACvCC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAACpD,IAAI,CAACuC,aAAa,CAAC;QAC5Ba,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACQL,qBAAqBA,CAACJ,GAAa;IACzC,MAAM1B,WAAW,GAAG,IAAI,CAACrC,UAAU,CAACsC,KAAK;IACzC,IAAI,CAACtC,UAAU,CAACkB,IAAI,CAACmB,WAAW,CAAC6B,MAAM,CAAC3C,MAAM,IAAI,CAACwC,GAAG,CAACV,QAAQ,CAAC9B,MAAM,CAACD,EAAE,CAAC,CAAC,CAAC;IAE5E,OAAO,IAAIhC,UAAU,CAACgF,QAAQ,IAAG;MAC/BC,UAAU,CAAC,MAAK;QACdD,QAAQ,CAACpD,IAAI,CAAC;UAAEuD,YAAY,EAAEV,GAAG,CAACX;QAAM,CAAE,CAAC;QAC3CkB,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;EACAE,kBAAkBA,CAAA;IAChBnE,YAAY,CAACoE,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACzE,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI0D,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC5E,UAAU,CAACsC,KAAK;EAC9B;EAEAuC,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQhD,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAACiD,OAAO,CAAC,OAAO,EAAE,UAASlC,CAAC;MACvE,MAAMmC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGtC,CAAC,KAAK,GAAG,GAAGmC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAAC,QAAAC,CAAA,G;qBAzQU1F,aAAa,EAAA2F,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAbjG,aAAa;IAAAkG,OAAA,EAAblG,aAAa,CAAAmG,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}