{"ast": null, "code": "export class ProduitModel {\n  constructor(produit) {\n    this.id = produit.id || this.getRandomID();\n    this.type = produit.type || '';\n    this.description = produit.description || '';\n    this.prixUnitaireHT = produit.prixUnitaireHT || 0;\n    this.prixUnitaireTTC = produit.prixUnitaireTTC || 0;\n    this.tva = produit.tva || 20;\n    this.codeProd = produit.codeProd || '';\n  }\n  getRandomID() {\n    const S4 = () => {\n      return (1 + Math.random()) * 0x10000 | 0;\n    };\n    return (S4() + S4()).toString();\n  }\n}", "map": {"version": 3, "names": ["ProduitModel", "constructor", "produit", "id", "getRandomID", "type", "description", "prixUnitaireHT", "prixUnitaireTTC", "tva", "codeProd", "S4", "Math", "random", "toString"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\Model\\Produit.ts"], "sourcesContent": ["export interface Produit {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\nexport class ProduitModel {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n\r\n  constructor(produit: Partial<Produit>) {\r\n    this.id = produit.id || this.getRandomID();\r\n    this.type = produit.type || '';\r\n    this.description = produit.description || '';\r\n    this.prixUnitaireHT = produit.prixUnitaireHT || 0;\r\n    this.prixUnitaireTTC = produit.prixUnitaireTTC || 0;\r\n    this.tva = produit.tva || 20;\r\n    this.codeProd = produit.codeProd || '';\r\n  }\r\n\r\n  public getRandomID(): string {\r\n    const S4 = () => {\r\n      return ((1 + Math.random()) * 0x10000) | 0;\r\n    };\r\n    return (S4() + S4()).toString();\r\n  }\r\n}\r\n// DTOs correspondants\r\nexport interface ProduitDTO {\r\n  id: string;\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  prixUnitaireTTC: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\nexport interface CreateProduitDTO {\r\n  type: string;\r\n  description: string;\r\n  prixUnitaireHT: number;\r\n  tva: number;\r\n  codeProd?: string;\r\n}\r\n\r\nexport interface UpdateProduitDTO {\r\n  type?: string;\r\n  description?: string;\r\n  prixUnitaireHT?: number;\r\n  tva?: number;\r\n  codeProd?: string;\r\n}"], "mappings": "AAUA,OAAM,MAAOA,YAAY;EASvBC,YAAYC,OAAyB;IACnC,IAAI,CAACC,EAAE,GAAGD,OAAO,CAACC,EAAE,IAAI,IAAI,CAACC,WAAW,EAAE;IAC1C,IAAI,CAACC,IAAI,GAAGH,OAAO,CAACG,IAAI,IAAI,EAAE;IAC9B,IAAI,CAACC,WAAW,GAAGJ,OAAO,CAACI,WAAW,IAAI,EAAE;IAC5C,IAAI,CAACC,cAAc,GAAGL,OAAO,CAACK,cAAc,IAAI,CAAC;IACjD,IAAI,CAACC,eAAe,GAAGN,OAAO,CAACM,eAAe,IAAI,CAAC;IACnD,IAAI,CAACC,GAAG,GAAGP,OAAO,CAACO,GAAG,IAAI,EAAE;IAC5B,IAAI,CAACC,QAAQ,GAAGR,OAAO,CAACQ,QAAQ,IAAI,EAAE;EACxC;EAEON,WAAWA,CAAA;IAChB,MAAMO,EAAE,GAAGA,CAAA,KAAK;MACd,OAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,IAAI,OAAO,GAAI,CAAC;IAC5C,CAAC;IACD,OAAO,CAACF,EAAE,EAAE,GAAGA,EAAE,EAAE,EAAEG,QAAQ,EAAE;EACjC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}