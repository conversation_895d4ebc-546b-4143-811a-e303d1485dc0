{"ast": null, "code": "import { ClientComponent } from './client/client.component';\nexport const CLIENT_ROUTE = [{\n  path: '',\n  redirectTo: 'list',\n  pathMatch: 'full'\n}, {\n  path: 'list',\n  component: ClientComponent\n}];", "map": {"version": 3, "names": ["ClientComponent", "CLIENT_ROUTE", "path", "redirectTo", "pathMatch", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\gestion_Client\\client.routes.ts"], "sourcesContent": ["import { Route } from '@angular/router';\nimport { ClientComponent } from './client/client.component';\n\nexport const CLIENT_ROUTE: Route[] = [\n  {\n    path: '',\n    redirectTo: 'list',\n    pathMatch: 'full',\n  },\n  {\n    path: 'list',\n    component: ClientComponent,\n  },\n];\n"], "mappings": "AACA,SAASA,eAAe,QAAQ,2BAA2B;AAE3D,OAAO,MAAMC,YAAY,GAAY,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,MAAM;EAClBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,SAAS,EAAEL;CACZ,CACF"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}