import { Client } from "./Client";
import { LigneDevisAvecCodeDTO } from "./LignesDevis";

export interface Devis {
  id: string;
  reference: string;
  dateCreation: Date;
  status: string;
  periodeProd: string;
  totalHT: number;
  remise: number;
  totalTTC: number;
  totalFodec: number;
  totalTimber: string;
  clientId: string;
  client?: Client; // Si vous avez un modèle Client
  lignesDevis?: LigneDevisAvecCodeDTO[]; // Si vous voulez inclure les lignes
}

export interface CreateDevisDto {
  id: string;
  reference: string;
  dateCreation?: Date;
  status?: string;
  periodeProd?: string;
  totalHT?: number;
  remise?: number;
  totalTTC?: number;
  totalFodec?: number;
  totalTimber?: string;
  clientId: string;
}

// Si vous avez besoin d'une classe pour les méthodes
export class DevisClass implements Devis {
  constructor(
    public id: string = '',
    public reference: string = '',
    public dateCreation: Date = new Date(),
    public status: string = 'Brouillon',
    public periodeProd: string = '',
    public totalHT: number = 0,
    public remise: number = 0,
    public totalTTC: number = 0,
    public totalFodec: number = 0,
    public totalTimber: string = '',
    public clientId: string = '',
    public client?: Client,
    public lignesDevis: LigneDevisAvecCodeDTO[] = []
  ) {}

  // Méthodes supplémentaires si nécessaire
  calculerTotaux(): void {
    if (this.lignesDevis && this.lignesDevis.length > 0) {
      // Calculer le total TTC
      this.totalTTC = this.lignesDevis.reduce((sum, ligne) => sum + (ligne.prixTotTTC || 0), 0);

      // Calculer le total HT en déduisant la TVA du total TTC
      this.totalHT = this.lignesDevis.reduce((sum, ligne) => {
        const prixTTC = ligne.prixTotTTC || 0;
        const tva = ligne.TVAProd || 0;
        const prixHT = prixTTC / (1 + tva / 100);
        return sum + prixHT;
      }, 0);
    }
  }
}