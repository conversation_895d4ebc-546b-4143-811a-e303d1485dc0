{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\n// import { environment } from '../../environments/environment';\nexport class ProduitService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Produits/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentProduitSubject = new BehaviorSubject(this.getProduitFromStorage());\n    this.currentProduit$ = this.currentProduitSubject.asObservable();\n  }\n  getProduitFromStorage() {\n    const produitData = localStorage.getItem('currentProduit');\n    return produitData ? JSON.parse(produitData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les produits\n  getAllProduits() {\n    this.isTblLoading = true; // Activation du loading\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(produits => {\n      this.isTblLoading = false; // Désactivation du loading\n      this.dataChange.next(produits);\n    }), catchError(error => {\n      this.isTblLoading = false; // Désactivation en cas d'erreur\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un produit par son ID\n  getProduitById(id) {\n    return this.http.get(`${this.baseUrl}/${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(produit => {\n      localStorage.setItem('currentProduit', JSON.stringify(produit));\n      this.currentProduitSubject.next(produit);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau produit\n  createProduit(produit) {\n    // Validation des données d'entrée\n    if (!produit) {\n      return throwError(() => new Error('Les données du produit sont requises'));\n    }\n    if (!produit.type || produit.type.trim() === '') {\n      return throwError(() => new Error('Le type de produit est requis'));\n    }\n    if (!produit.description || produit.description.trim() === '') {\n      return throwError(() => new Error('La description du produit est requise'));\n    }\n    if (produit.prixUnitaireHT === undefined || produit.prixUnitaireHT === null || produit.prixUnitaireHT < 0) {\n      return throwError(() => new Error('Le prix unitaire HT doit être un nombre positif'));\n    }\n    if (produit.tva === undefined || produit.tva === null || produit.tva < 0 || produit.tva > 100) {\n      return throwError(() => new Error('La TVA doit être comprise entre 0 et 100%'));\n    }\n    // Préparer les données à envoyer\n    const produitData = {\n      type: produit.type.trim(),\n      description: produit.description.trim(),\n      prixUnitaireHT: Number(produit.prixUnitaireHT),\n      tva: Number(produit.tva),\n      codeProd: produit.codeProd ? produit.codeProd.trim() : undefined\n    };\n    console.log('Tentative de création du produit:', produitData);\n    return this.http.post(this.baseUrl, produitData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      console.log('Réponse complète du serveur:', response);\n      const newProduit = response.body;\n      if (!newProduit) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newProduit;\n    }), tap(newProduit => {\n      console.log('Produit créé avec succès:', newProduit);\n      // Mettre à jour le cache local\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newProduit]);\n    }), catchError(error => {\n      console.error('Erreur détaillée lors de la création:', {\n        status: error.status,\n        statusText: error.statusText,\n        error: error.error,\n        message: error.message,\n        url: error.url,\n        headers: error.headers\n      });\n      return this.handleCreateError(error);\n    }));\n  }\n  // Mettre à jour un produit\n  updateProduit(id, produit) {\n    return this.http.put(`${this.baseUrl}${id}`, produit, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Produit mis à jour avec succès');\n      this.clearCurrentProduit();\n    }), catchError(this.handleError));\n  }\n  // Supprimer un produit\n  deleteProduit(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      console.log('Produit supprimé avec succès');\n      this.clearCurrentProduit();\n    }), catchError(this.handleError));\n  }\n  // Calculer le prix TTC\n  calculateTTC(prixHT, tva) {\n    if (isNaN(prixHT) || isNaN(tva) || prixHT < 0 || tva < 0) {\n      console.warn('Valeurs invalides pour le calcul TTC:', {\n        prixHT,\n        tva\n      });\n      return 0;\n    }\n    const result = Math.round(prixHT * (1 + tva / 100) * 100) / 100;\n    console.log(`Calcul TTC: ${prixHT} * (1 + ${tva}/100) = ${result}`);\n    return result;\n  }\n  // Méthode pour supprimer plusieurs produits\n  deleteSelectedProduits(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de produit fourni pour la suppression'));\n    }\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log(`${ids.length} produits supprimés avec succès`);\n    }), catchError(this.handleError));\n  }\n  // Effacer le produit courant\n  clearCurrentProduit() {\n    localStorage.removeItem('currentProduit');\n    this.currentProduitSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthode de debug pour tester la connexion\n  testConnection() {\n    console.log('Test de connexion vers:', this.baseUrl);\n    console.log('Headers utilisés:', this.getHeaders());\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('Test de connexion réussi:', response);\n    }), catchError(error => {\n      console.error('Test de connexion échoué:', error);\n      return throwError(() => error);\n    }));\n  }\n  // Gestionnaire d'erreur spécialisé pour la création\n  handleCreateError(error) {\n    console.error('Erreur lors de la création du produit:', error);\n    let errorMessage = 'Erreur lors de la création du produit';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un produit.';\n    } else if (error.status === 409) {\n      errorMessage = 'Un produit avec ces caractéristiques existe déjà.';\n    } else if (error.status === 422) {\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\n    } else if (error.status >= 500) {\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    console.error('ProduitService error:', error);\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 403) {\n      errorMessage = 'Forbidden';\n    } else if (error.status === 404) {\n      errorMessage = 'Product not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - product already exists';\n    } else if (error.status >= 500) {\n      errorMessage = 'Server error';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ProduitService_Factory(t) {\n    return new (t || ProduitService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ProduitService,\n    factory: ProduitService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "throwError", "tap", "catchError", "map", "ProduitService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentProduitSubject", "getProduitFromStorage", "currentProduit$", "asObservable", "produitData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllProduits", "get", "headers", "pipe", "produits", "next", "error", "handleError", "getProduitById", "id", "produit", "setItem", "stringify", "createProduit", "Error", "type", "trim", "description", "prixUnitaireHT", "undefined", "tva", "Number", "codeProd", "console", "log", "post", "observe", "response", "newProduit", "body", "currentData", "value", "status", "statusText", "message", "url", "handleCreateError", "updateProduit", "put", "clearCurrentProduit", "deleteProduit", "delete", "calculateTTC", "prixHT", "isNaN", "warn", "result", "Math", "round", "deleteSelectedProduits", "ids", "length", "removeItem", "data", "getDialogData", "dialogData", "testConnection", "errorMessage", "apiError", "title", "errors", "validationErrors", "Object", "values", "flat", "join", "navigate", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\produit.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Produit, ProduitDTO, CreateProduitDTO, UpdateProduitDTO } from '../Model/Produit';\r\n\r\n// import { environment } from '../../environments/environment';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ProduitService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Produits/';\r\n  private currentProduitSubject: BehaviorSubject<Produit | null>;\r\n  public currentProduit$: Observable<Produit | null>;\r\n  dataChange = new BehaviorSubject<Produit[]>([]);\r\n  dialogData!: Produit;\r\n    isTblLoading = true;\r\n\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentProduitSubject = new BehaviorSubject<Produit | null>(this.getProduitFromStorage());\r\n    this.currentProduit$ = this.currentProduitSubject.asObservable();\r\n  }\r\n\r\n  private getProduitFromStorage(): Produit | null {\r\n    const produitData = localStorage.getItem('currentProduit');\r\n    return produitData ? JSON.parse(produitData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les produits\r\ngetAllProduits(): Observable<Produit[]> {\r\n    this.isTblLoading = true; // Activation du loading\r\n    return this.http.get<Produit[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(produits => {\r\n        this.isTblLoading = false; // Désactivation du loading\r\n        this.dataChange.next(produits);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false; // Désactivation en cas d'erreur\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n  // Récupérer un produit par son ID\r\n  getProduitById(id: string): Observable<ProduitDTO> {\r\n    return this.http.get<ProduitDTO>(`${this.baseUrl}/${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(produit => {\r\n        localStorage.setItem('currentProduit', JSON.stringify(produit));\r\n        this.currentProduitSubject.next(produit);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau produit\r\n  createProduit(produit: CreateProduitDTO): Observable<ProduitDTO> {\r\n    // Validation des données d'entrée\r\n    if (!produit) {\r\n      return throwError(() => new Error('Les données du produit sont requises'));\r\n    }\r\n\r\n    if (!produit.type || produit.type.trim() === '') {\r\n      return throwError(() => new Error('Le type de produit est requis'));\r\n    }\r\n\r\n    if (!produit.description || produit.description.trim() === '') {\r\n      return throwError(() => new Error('La description du produit est requise'));\r\n    }\r\n\r\n    if (produit.prixUnitaireHT === undefined || produit.prixUnitaireHT === null || produit.prixUnitaireHT < 0) {\r\n      return throwError(() => new Error('Le prix unitaire HT doit être un nombre positif'));\r\n    }\r\n\r\n    if (produit.tva === undefined || produit.tva === null || produit.tva < 0 || produit.tva > 100) {\r\n      return throwError(() => new Error('La TVA doit être comprise entre 0 et 100%'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const produitData: CreateProduitDTO = {\r\n      type: produit.type.trim(),\r\n      description: produit.description.trim(),\r\n      prixUnitaireHT: Number(produit.prixUnitaireHT),\r\n      tva: Number(produit.tva),\r\n      codeProd: produit.codeProd ? produit.codeProd.trim() : undefined\r\n    };\r\n\r\n    console.log('Tentative de création du produit:', produitData);\r\n\r\n    return this.http.post<ProduitDTO>(this.baseUrl, produitData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        console.log('Réponse complète du serveur:', response);\r\n        const newProduit: ProduitDTO = response.body;\r\n        if (!newProduit) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newProduit;\r\n      }),\r\n      tap((newProduit: ProduitDTO) => {\r\n        console.log('Produit créé avec succès:', newProduit);\r\n        // Mettre à jour le cache local\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newProduit as Produit]);\r\n      }),\r\n      catchError((error: HttpErrorResponse) => {\r\n        console.error('Erreur détaillée lors de la création:', {\r\n          status: error.status,\r\n          statusText: error.statusText,\r\n          error: error.error,\r\n          message: error.message,\r\n          url: error.url,\r\n          headers: error.headers\r\n        });\r\n        return this.handleCreateError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un produit\r\n  updateProduit(id: string, produit: UpdateProduitDTO): Observable<void> {\r\n    return this.http.put<void>(`${this.baseUrl}${id}`, produit, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        console.log('Produit mis à jour avec succès');\r\n        this.clearCurrentProduit();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer un produit\r\n  deleteProduit(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        console.log('Produit supprimé avec succès');\r\n        this.clearCurrentProduit();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Calculer le prix TTC\r\n  calculateTTC(prixHT: number, tva: number): number {\r\n    if (isNaN(prixHT) || isNaN(tva) || prixHT < 0 || tva < 0) {\r\n      console.warn('Valeurs invalides pour le calcul TTC:', { prixHT, tva });\r\n      return 0;\r\n    }\r\n    const result = Math.round((prixHT * (1 + tva / 100)) * 100) / 100;\r\n    console.log(`Calcul TTC: ${prixHT} * (1 + ${tva}/100) = ${result}`);\r\n    return result;\r\n  }\r\n // Méthode pour supprimer plusieurs produits\r\n  deleteSelectedProduits(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de produit fourni pour la suppression'));\r\n    }\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log(`${ids.length} produits supprimés avec succès`);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n  // Effacer le produit courant\r\n  clearCurrentProduit(): void {\r\n    localStorage.removeItem('currentProduit');\r\n    this.currentProduitSubject.next(null);\r\n  }\r\nget data(): Produit[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthode de debug pour tester la connexion\r\n  testConnection(): Observable<any> {\r\n    console.log('Test de connexion vers:', this.baseUrl);\r\n    console.log('Headers utilisés:', this.getHeaders());\r\n\r\n    return this.http.get(this.baseUrl, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('Test de connexion réussi:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Test de connexion échoué:', error);\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Gestionnaire d'erreur spécialisé pour la création\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    console.error('Erreur lors de la création du produit:', error);\r\n\r\n    let errorMessage = 'Erreur lors de la création du produit';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur. Vérifiez votre connexion internet.';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides. Vérifiez les informations saisies.';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée. Veuillez vous reconnecter.';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Vous n\\'avez pas les permissions pour créer un produit.';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un produit avec ces caractéristiques existe déjà.';\r\n    } else if (error.status === 422) {\r\n      errorMessage = 'Données non valides. Vérifiez tous les champs obligatoires.';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Erreur du serveur. Veuillez réessayer plus tard.';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    console.error('ProduitService error:', error);\r\n\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 403) {\r\n      errorMessage = 'Forbidden';\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Product not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - product already exists';\r\n    } else if (error.status >= 500) {\r\n      errorMessage = 'Server error';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAcC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAGpF;AAKA,OAAM,MAAOC,cAAc;EASzBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAR5C,KAAAC,OAAO,GAAW,sCAAsC;IAGhE,KAAAC,UAAU,GAAG,IAAIV,eAAe,CAAY,EAAE,CAAC;IAE7C,KAAAW,YAAY,GAAG,IAAI;IAInB,IAAI,CAACC,qBAAqB,GAAG,IAAIZ,eAAe,CAAiB,IAAI,CAACa,qBAAqB,EAAE,CAAC;IAC9F,IAAI,CAACC,eAAe,GAAG,IAAI,CAACF,qBAAqB,CAACG,YAAY,EAAE;EAClE;EAEQF,qBAAqBA,CAAA;IAC3B,MAAMG,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,gBAAgB,CAAC;IAC1D,OAAOF,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACJ,WAAW,CAAC,GAAG,IAAI;EACrD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAInB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIuB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACFC,cAAcA,CAAA;IACV,IAAI,CAACZ,YAAY,GAAG,IAAI,CAAC,CAAC;IAC1B,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAY,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAChFxB,GAAG,CAACyB,QAAQ,IAAG;MACb,IAAI,CAAChB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,QAAQ,CAAC;IAChC,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK,CAAC,CAAC;MAC3B,OAAO,IAAI,CAACmB,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EACA;EACAE,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAa,GAAG,IAAI,CAACf,OAAO,IAAIuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC5FxB,GAAG,CAAC+B,OAAO,IAAG;MACZhB,YAAY,CAACiB,OAAO,CAAC,gBAAgB,EAAEf,IAAI,CAACgB,SAAS,CAACF,OAAO,CAAC,CAAC;MAC/D,IAAI,CAACrB,qBAAqB,CAACgB,IAAI,CAACK,OAAO,CAAC;IAC1C,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,aAAaA,CAACH,OAAyB;IACrC;IACA,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,sCAAsC,CAAC,CAAC;;IAG5E,IAAI,CAACJ,OAAO,CAACK,IAAI,IAAIL,OAAO,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC/C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE,IAAI,CAACJ,OAAO,CAACO,WAAW,IAAIP,OAAO,CAACO,WAAW,CAACD,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7D,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E,IAAIJ,OAAO,CAACQ,cAAc,KAAKC,SAAS,IAAIT,OAAO,CAACQ,cAAc,KAAK,IAAI,IAAIR,OAAO,CAACQ,cAAc,GAAG,CAAC,EAAE;MACzG,OAAOxC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,iDAAiD,CAAC,CAAC;;IAGvF,IAAIJ,OAAO,CAACU,GAAG,KAAKD,SAAS,IAAIT,OAAO,CAACU,GAAG,KAAK,IAAI,IAAIV,OAAO,CAACU,GAAG,GAAG,CAAC,IAAIV,OAAO,CAACU,GAAG,GAAG,GAAG,EAAE;MAC7F,OAAO1C,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2CAA2C,CAAC,CAAC;;IAGjF;IACA,MAAMrB,WAAW,GAAqB;MACpCsB,IAAI,EAAEL,OAAO,CAACK,IAAI,CAACC,IAAI,EAAE;MACzBC,WAAW,EAAEP,OAAO,CAACO,WAAW,CAACD,IAAI,EAAE;MACvCE,cAAc,EAAEG,MAAM,CAACX,OAAO,CAACQ,cAAc,CAAC;MAC9CE,GAAG,EAAEC,MAAM,CAACX,OAAO,CAACU,GAAG,CAAC;MACxBE,QAAQ,EAAEZ,OAAO,CAACY,QAAQ,GAAGZ,OAAO,CAACY,QAAQ,CAACN,IAAI,EAAE,GAAGG;KACxD;IAEDI,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE/B,WAAW,CAAC;IAE7D,OAAO,IAAI,CAACT,IAAI,CAACyC,IAAI,CAAa,IAAI,CAACvC,OAAO,EAAEO,WAAW,EAAE;MAC3DS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B4B,OAAO,EAAE;KACV,CAAC,CAACvB,IAAI,CACLtB,GAAG,CAAE8C,QAAa,IAAI;MACpBJ,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEG,QAAQ,CAAC;MACrD,MAAMC,UAAU,GAAeD,QAAQ,CAACE,IAAI;MAC5C,IAAI,CAACD,UAAU,EAAE;QACf,MAAM,IAAId,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOc,UAAU;IACnB,CAAC,CAAC,EACFjD,GAAG,CAAEiD,UAAsB,IAAI;MAC7BL,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEI,UAAU,CAAC;MACpD;MACA,MAAME,WAAW,GAAG,IAAI,CAAC3C,UAAU,CAAC4C,KAAK;MACzC,IAAI,CAAC5C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGyB,WAAW,EAAEF,UAAqB,CAAC,CAAC;IAC/D,CAAC,CAAC,EACFhD,UAAU,CAAE0B,KAAwB,IAAI;MACtCiB,OAAO,CAACjB,KAAK,CAAC,uCAAuC,EAAE;QACrD0B,MAAM,EAAE1B,KAAK,CAAC0B,MAAM;QACpBC,UAAU,EAAE3B,KAAK,CAAC2B,UAAU;QAC5B3B,KAAK,EAAEA,KAAK,CAACA,KAAK;QAClB4B,OAAO,EAAE5B,KAAK,CAAC4B,OAAO;QACtBC,GAAG,EAAE7B,KAAK,CAAC6B,GAAG;QACdjC,OAAO,EAAEI,KAAK,CAACJ;OAChB,CAAC;MACF,OAAO,IAAI,CAACkC,iBAAiB,CAAC9B,KAAK,CAAC;IACtC,CAAC,CAAC,CACH;EACH;EAEA;EACA+B,aAAaA,CAAC5B,EAAU,EAAEC,OAAyB;IACjD,OAAO,IAAI,CAAC1B,IAAI,CAACsD,GAAG,CAAO,GAAG,IAAI,CAACpD,OAAO,GAAGuB,EAAE,EAAE,EAAEC,OAAO,EAAE;MAAER,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC9FxB,GAAG,CAAC,MAAK;MACP4C,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAC7C,IAAI,CAACe,mBAAmB,EAAE;IAC5B,CAAC,CAAC,EACF3D,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAiC,aAAaA,CAAC/B,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACyD,MAAM,CAAO,GAAG,IAAI,CAACvD,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP4C,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C,IAAI,CAACe,mBAAmB,EAAE;IAC5B,CAAC,CAAC,EACF3D,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmC,YAAYA,CAACC,MAAc,EAAEvB,GAAW;IACtC,IAAIwB,KAAK,CAACD,MAAM,CAAC,IAAIC,KAAK,CAACxB,GAAG,CAAC,IAAIuB,MAAM,GAAG,CAAC,IAAIvB,GAAG,GAAG,CAAC,EAAE;MACxDG,OAAO,CAACsB,IAAI,CAAC,uCAAuC,EAAE;QAAEF,MAAM;QAAEvB;MAAG,CAAE,CAAC;MACtE,OAAO,CAAC;;IAEV,MAAM0B,MAAM,GAAGC,IAAI,CAACC,KAAK,CAAEL,MAAM,IAAI,CAAC,GAAGvB,GAAG,GAAG,GAAG,CAAC,GAAI,GAAG,CAAC,GAAG,GAAG;IACjEG,OAAO,CAACC,GAAG,CAAC,eAAemB,MAAM,WAAWvB,GAAG,WAAW0B,MAAM,EAAE,CAAC;IACnE,OAAOA,MAAM;EACf;EACD;EACCG,sBAAsBA,CAACC,GAAa;IAClC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOzE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,gDAAgD,CAAC,CAAC;;IAGtF,OAAO,IAAI,CAAC9B,IAAI,CAACyD,MAAM,CAAC,GAAG,IAAI,CAACvD,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B+B,IAAI,EAAEqB;KACP,CAAC,CAAC/C,IAAI,CACLxB,GAAG,CAAC,MAAK;MACP4C,OAAO,CAACC,GAAG,CAAC,GAAG0B,GAAG,CAACC,MAAM,iCAAiC,CAAC;IAC7D,CAAC,CAAC,EACFvE,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EACA;EACAgC,mBAAmBA,CAAA;IACjB7C,YAAY,CAAC0D,UAAU,CAAC,gBAAgB,CAAC;IACzC,IAAI,CAAC/D,qBAAqB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACvC;EACF,IAAIgD,IAAIA,CAAA;IACJ,OAAO,IAAI,CAAClE,UAAU,CAAC4C,KAAK;EAC9B;EAEAuB,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACAC,cAAcA,CAAA;IACZjC,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACtC,OAAO,CAAC;IACpDqC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAAC1B,UAAU,EAAE,CAAC;IAEnD,OAAO,IAAI,CAACd,IAAI,CAACiB,GAAG,CAAC,IAAI,CAACf,OAAO,EAAE;MACjCgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B4B,OAAO,EAAE;KACV,CAAC,CAACvB,IAAI,CACLxB,GAAG,CAACgD,QAAQ,IAAG;MACbJ,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEG,QAAQ,CAAC;IACpD,CAAC,CAAC,EACF/C,UAAU,CAAC0B,KAAK,IAAG;MACjBiB,OAAO,CAACjB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQ8B,iBAAiBA,CAAC9B,KAAwB;IAChDiB,OAAO,CAACjB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;IAE9D,IAAImD,YAAY,GAAG,uCAAuC;IAC1D,MAAMC,QAAQ,GAAGpD,KAAK,CAACA,KAAK,EAAE4B,OAAO,IAAI5B,KAAK,CAACA,KAAK,EAAEqD,KAAK,IAAIrD,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAIoD,QAAQ,EAAE;MACZD,YAAY,GAAG,uBAAuBC,QAAQ,EAAE;KACjD,MAAM,IAAIpD,KAAK,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC7ByB,YAAY,GAAG,2EAA2E;KAC3F,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,uDAAuD;MACtE,IAAInD,KAAK,CAACA,KAAK,EAAEsD,MAAM,EAAE;QACvB,MAAMC,gBAAgB,GAAGC,MAAM,CAACC,MAAM,CAACzD,KAAK,CAACA,KAAK,CAACsD,MAAM,CAAC,CAACI,IAAI,EAAE;QACjEP,YAAY,IAAI,aAAaI,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAI3D,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,6CAA6C;MAC5D,IAAI,CAACxE,MAAM,CAACiF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI5D,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,yDAAyD;KACzE,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,mDAAmD;KACnE,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,6DAA6D;KAC7E,MAAM,IAAInD,KAAK,CAAC0B,MAAM,IAAI,GAAG,EAAE;MAC9ByB,YAAY,GAAG,kDAAkD;;IAGnE,OAAO/E,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC2C,YAAY,CAAC,CAAC;EAClD;EAEQlD,WAAWA,CAACD,KAAwB;IAC1CiB,OAAO,CAACjB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAE7C,IAAImD,YAAY,GAAG,mBAAmB;IACtC,MAAMC,QAAQ,GAAGpD,KAAK,CAACA,KAAK,EAAE4B,OAAO,IAAI5B,KAAK,CAACA,KAAK,EAAEqD,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZD,YAAY,GAAGC,QAAQ;KACxB,MAAM,IAAIpD,KAAK,CAAC0B,MAAM,KAAK,CAAC,EAAE;MAC7ByB,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,cAAc;MAC7B,IAAI,CAACxE,MAAM,CAACiF,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAI5D,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,WAAW;KAC3B,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAInD,KAAK,CAAC0B,MAAM,KAAK,GAAG,EAAE;MAC/ByB,YAAY,GAAG,mCAAmC;KACnD,MAAM,IAAInD,KAAK,CAAC0B,MAAM,IAAI,GAAG,EAAE;MAC9ByB,YAAY,GAAG,cAAc;;IAG/B,OAAO/E,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC2C,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAU,CAAA,G;qBAjQUrF,cAAc,EAAAsF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAd5F,cAAc;IAAA6F,OAAA,EAAd7F,cAAc,CAAA8F,IAAA;IAAAC,UAAA,EAFb;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}