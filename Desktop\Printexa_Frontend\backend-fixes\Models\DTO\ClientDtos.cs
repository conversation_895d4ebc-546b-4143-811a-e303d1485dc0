using System.ComponentModel.DataAnnotations;

namespace Printexa.Models.DTO
{
    // DTO pour la création d'un client (sans ID car généré par le backend)
    public class CreateClientDto
    {
        [Required(ErrorMessage = "Le code client est obligatoire")]
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Le code client doit contenir entre 1 et 255 caractères")]
        public string Code { get; set; }

        [StringLength(255, ErrorMessage = "La syntaxe ne peut pas dépasser 255 caractères")]
        public string Syntax { get; set; }

        [StringLength(255, ErrorMessage = "Le matricule fiscal ne peut pas dépasser 255 caractères")]
        public string MatFiscal { get; set; }

        [StringLength(255, ErrorMessage = "L'email ne peut pas dépasser 255 caractères")]
        public string Email { get; set; }

        [StringLength(255, ErrorMessage = "Le téléphone ne peut pas dépasser 255 caractères")]
        public string Telephone { get; set; }
    }

    // DTO pour la mise à jour d'un client
    public class UpdateClientDto
    {
        [StringLength(255, MinimumLength = 1, ErrorMessage = "Le code client doit contenir entre 1 et 255 caractères")]
        public string Code { get; set; }

        [StringLength(255, ErrorMessage = "La syntaxe ne peut pas dépasser 255 caractères")]
        public string Syntax { get; set; }

        [StringLength(255, ErrorMessage = "Le matricule fiscal ne peut pas dépasser 255 caractères")]
        public string MatFiscal { get; set; }

        [StringLength(255, ErrorMessage = "L'email ne peut pas dépasser 255 caractères")]
        public string Email { get; set; }

        [StringLength(255, ErrorMessage = "Le téléphone ne peut pas dépasser 255 caractères")]
        public string Telephone { get; set; }
    }

    // DTO pour la réponse (lecture)
    public class ClientResponseDto
    {
        public string Id { get; set; }
        public string Code { get; set; }
        public string Syntax { get; set; }
        public string MatFiscal { get; set; }
        public string Email { get; set; }
        public string Telephone { get; set; }
    }
}
