{"ast": null, "code": "import { HttpHeaders } from '@angular/common/http';\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"@angular/router\";\nexport class ClientService {\n  constructor(http, router) {\n    this.http = http;\n    this.router = router;\n    this.baseUrl = 'https://localhost:5001/api/Client/';\n    this.dataChange = new BehaviorSubject([]);\n    this.isTblLoading = true;\n    this.currentClientSubject = new BehaviorSubject(this.getClientFromStorage());\n    this.currentClient$ = this.currentClientSubject.asObservable();\n  }\n  getClientFromStorage() {\n    const clientData = localStorage.getItem('currentClient');\n    return clientData ? JSON.parse(clientData) : null;\n  }\n  getHeaders() {\n    const token = localStorage.getItem('auth_token');\n    return new HttpHeaders({\n      'Content-Type': 'application/json',\n      ...(token ? {\n        'Authorization': `Bearer ${token}`\n      } : {})\n    });\n  }\n  // Récupérer tous les clients\n  getAllClients() {\n    this.isTblLoading = true;\n    return this.http.get(this.baseUrl, {\n      headers: this.getHeaders()\n    }).pipe(tap(clients => {\n      this.isTblLoading = false;\n      this.dataChange.next(clients || []);\n    }), catchError(error => {\n      this.isTblLoading = false;\n      this.dataChange.next([]);\n      return this.handleError(error);\n    }));\n  }\n  // Récupérer un client par son ID\n  getClientById(id) {\n    return this.http.get(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(client => {\n      localStorage.setItem('currentClient', JSON.stringify(client));\n      this.currentClientSubject.next(client);\n    }), catchError(this.handleError));\n  }\n  // Créer un nouveau client\n  createClient(client) {\n    // Validation des données d'entrée\n    if (!client) {\n      return throwError(() => new Error('Les données du client sont requises'));\n    }\n    if (!client.code || client.code.trim() === '') {\n      return throwError(() => new Error('Le code client est requis'));\n    }\n    if (client.email && !this.validateEmail(client.email)) {\n      return throwError(() => new Error('Format d\\'email invalide'));\n    }\n    // Préparer les données à envoyer\n    const clientData = {\n      id: client.id || this.generateGuid(),\n      code: client.code.trim(),\n      syntax: client.syntax?.trim(),\n      matFiscal: client.matFiscal?.trim(),\n      email: client.email?.trim(),\n      telephone: client.telephone?.trim()\n    };\n    return this.http.post(this.baseUrl, clientData, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      const newClient = response.body;\n      if (!newClient) {\n        throw new Error('Aucune donnée reçue du serveur');\n      }\n      return newClient;\n    }), tap(newClient => {\n      const currentData = this.dataChange.value;\n      this.dataChange.next([...currentData, newClient]);\n    }), catchError(this.handleCreateError));\n  }\n  // Mettre à jour un client\n  updateClient(id, client) {\n    if (!id?.trim() || !client) {\n      return throwError(() => new Error('ID et données client requis'));\n    }\n    // Résoudre l'ID si c'est un code client\n    let actualId = id;\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    if (isCode) {\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\n      if (clientByCode) actualId = clientByCode.id;\n    }\n    // Valider que l'ID final est un GUID\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    if (!isGuid) {\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\n    }\n    // Nettoyer les données (supprimer l'id et les champs vides)\n    const cleanedClient = {};\n    Object.keys(client).forEach(key => {\n      if (key !== 'id' && client[key]?.toString().trim()) {\n        cleanedClient[key] = client[key];\n      }\n    });\n    if (Object.keys(cleanedClient).length === 0) {\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\n    }\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\n      headers: this.getHeaders(),\n      observe: 'response'\n    }).pipe(map(response => {\n      // Gérer NoContent (204) ou réponse avec body\n      if (response.status === 204 || !response.body) {\n        const existing = this.dataChange.value.find(c => c.id === actualId);\n        return existing ? {\n          ...existing,\n          ...cleanedClient\n        } : cleanedClient;\n      }\n      return response.body;\n    }), tap(updatedClient => {\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const index = currentData.findIndex(c => c.id === actualId);\n      if (index !== -1) {\n        currentData[index] = updatedClient;\n        this.dataChange.next([...currentData]);\n      }\n    }), catchError(error => {\n      // Mode simulation en cas d'erreur\n      if (error.status === 0 || error.status === 400 && error.error?.message?.includes('validation')) {\n        return this.simulateLocalUpdate(actualId, cleanedClient);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Supprimer un client\n  deleteClient(id) {\n    return this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(tap(() => {\n      this.clearCurrentClient();\n    }), catchError(this.handleError));\n  }\n  // Supprimer plusieurs clients\n  deleteSelectedClients(ids) {\n    if (!ids || ids.length === 0) {\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\n    }\n    console.log('Tentative de suppression des clients:', ids);\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\n      headers: this.getHeaders(),\n      body: ids\n    }).pipe(tap(() => {\n      console.log('Suppression en masse réussie');\n      // Mettre à jour les données locales\n      const currentData = this.dataChange.value;\n      const updatedData = currentData.filter(client => !ids.includes(client.id));\n      this.dataChange.next(updatedData);\n    }), catchError(error => {\n      console.error('Erreur lors de la suppression en masse:', error);\n      // Si le serveur n'est pas disponible, simuler la suppression localement\n      if (error.status === 0) {\n        console.log('Serveur non disponible, simulation de la suppression...');\n        return this.simulateLocalDeletion(ids);\n      }\n      // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\n      if (error.status === 500 || error.status === 404) {\n        console.log('Tentative de suppression individuelle...');\n        return this.deleteClientsIndividually(ids);\n      }\n      return this.handleError(error);\n    }));\n  }\n  // Méthode de fallback pour supprimer les clients individuellement\n  deleteClientsIndividually(ids) {\n    const deleteRequests = ids.map(id => this.http.delete(`${this.baseUrl}${id}`, {\n      headers: this.getHeaders()\n    }).pipe(catchError(error => {\n      console.error(`Erreur lors de la suppression du client ${id}:`, error);\n      return throwError(() => error);\n    })));\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\n    return new Observable(observer => {\n      let completedCount = 0;\n      let hasError = false;\n      const errors = [];\n      deleteRequests.forEach((request, index) => {\n        request.subscribe({\n          next: () => {\n            completedCount++;\n            if (completedCount === ids.length && !hasError) {\n              // Mettre à jour les données locales\n              const currentData = this.dataChange.value;\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\n              this.dataChange.next(updatedData);\n              observer.next({\n                deletedCount: completedCount\n              });\n              observer.complete();\n            }\n          },\n          error: error => {\n            hasError = true;\n            errors.push({\n              id: ids[index],\n              error\n            });\n            if (completedCount + errors.length === ids.length) {\n              observer.error({\n                message: 'Certains clients n\\'ont pas pu être supprimés',\n                errors,\n                deletedCount: completedCount\n              });\n            }\n          }\n        });\n      });\n    });\n  }\n  // Simuler la suppression locale quand le serveur n'est pas disponible\n  simulateLocalDeletion(ids) {\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\n    // Mettre à jour les données locales\n    const currentData = this.dataChange.value;\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\n    this.dataChange.next(updatedData);\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next({\n          deletedCount: ids.length,\n          message: 'Suppression simulée localement (serveur non disponible)'\n        });\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\n  simulateLocalUpdate(id, updateData) {\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\n    // Trouver et mettre à jour le client dans les données locales\n    const currentData = this.dataChange.value;\n    const index = currentData.findIndex(client => client.id === id);\n    if (index === -1) {\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\n    }\n    // Créer le client mis à jour\n    const updatedClient = {\n      ...currentData[index],\n      ...updateData\n    };\n    // Mettre à jour les données locales\n    currentData[index] = updatedClient;\n    this.dataChange.next([...currentData]);\n    // Mettre à jour le client courant si c'est le même\n    if (this.currentClientSubject.value?.id === id) {\n      this.currentClientSubject.next(updatedClient);\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\n    }\n    // Retourner un Observable qui simule le succès\n    return new Observable(observer => {\n      setTimeout(() => {\n        observer.next(updatedClient);\n        observer.complete();\n      }, 500); // Simuler un délai réseau\n    });\n  }\n  // Effacer le client courant\n  clearCurrentClient() {\n    localStorage.removeItem('currentClient');\n    this.currentClientSubject.next(null);\n  }\n  get data() {\n    return this.dataChange.value;\n  }\n  getDialogData() {\n    return this.dialogData;\n  }\n  // Méthodes utilitaires\n  generateGuid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      const r = Math.random() * 16 | 0,\n        v = c === 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  }\n  validateEmail(email) {\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return re.test(email);\n  }\n  // Méthode de test pour diagnostiquer les problèmes d'ID\n  testClientId(id) {\n    console.log('=== TEST ID CLIENT ===');\n    console.log('ID fourni:', id);\n    console.log('Type:', typeof id);\n    console.log('Longueur:', id.length);\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\n    const currentData = this.dataChange.value;\n    console.log('Nombre de clients dans les données:', currentData.length);\n    const clientById = currentData.find(c => c.id === id);\n    const clientByCode = currentData.find(c => c.code === id);\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\n    if (clientById) {\n      console.log('Client par ID:', clientById);\n    }\n    if (clientByCode) {\n      console.log('Client par code:', clientByCode);\n    }\n    // Test de résolution d'ID\n    console.log('--- Test de résolution ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\n    let actualId = id;\n    if (isCode && !isGuid) {\n      const clientByCodeResolution = currentData.find(c => c.code === id);\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\n        actualId = clientByCodeResolution.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\n    console.log('ID final:', actualId);\n    console.log('ID final est GUID:', finalIsGuid);\n    console.log('===================');\n  }\n  // Méthode de test pour valider une mise à jour sans l'envoyer\n  testUpdateValidation(id, updateData) {\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\n    console.log('ID:', id);\n    console.log('Données:', updateData);\n    const validation = this.validateUpdateData(updateData);\n    console.log('Validation réussie:', validation.isValid);\n    if (!validation.isValid) {\n      console.log('Erreurs:', validation.errors);\n    }\n    console.log('Données nettoyées qui seraient envoyées:');\n    const cleaned = {};\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\n    console.log('Cleaned data:', cleaned);\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\n    console.log('================================');\n  }\n  // Validation stricte pour la mise à jour\n  validateUpdateData(client) {\n    const errors = [];\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\n    // Validation des champs\n    if (client.code !== undefined) {\n      if (!client.code || client.code.trim() === '') {\n        errors.push('Code ne peut pas être vide');\n      } else if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Code doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\n      // Validation de format supprimée - accepter tout format\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email !== undefined && client.email !== null) {\n      if (client.email.trim() !== '') {\n        // Validation de format supprimée - garder seulement la limite de longueur\n        if (client.email.length > 100) {\n          errors.push('Email ne peut pas dépasser 100 caractères');\n        }\n      }\n    }\n    if (client.telephone !== undefined && client.telephone !== null) {\n      if (client.telephone.trim() !== '') {\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n        if (!phonePattern.test(client.telephone)) {\n          errors.push('Format de téléphone invalide');\n        }\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Méthode publique pour tester une mise à jour complète\n  debugUpdate(clientId, updateData) {\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\n    // Test 1: Validation de l'ID\n    this.testClientId(clientId);\n    // Test 2: Validation des données\n    this.testUpdateValidation(clientId, updateData);\n    // Test 3: Simulation de la requête\n    console.log('--- Simulation de la requête ---');\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\n    let actualId = clientId;\n    if (isCode && !isGuid) {\n      const currentData = this.dataChange.value;\n      const clientByCode = currentData.find(c => c.code === clientId);\n      if (clientByCode) {\n        actualId = clientByCode.id;\n        console.log('ID résolu:', actualId);\n      }\n    }\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\n    console.log('Méthode: PUT');\n    console.log('Headers:', this.getHeaders());\n    const cleanedData = {};\n    if (updateData.code) cleanedData.code = updateData.code.trim();\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\n    console.log('================================');\n  }\n  // Méthode pour tester la requête HTTP brute\n  testRawHttpRequest(clientId, updateData) {\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\n    const url = `${this.baseUrl}${clientId}`;\n    const headers = this.getHeaders();\n    console.log('URL:', url);\n    console.log('Headers:', headers);\n    console.log('Body:', JSON.stringify(updateData, null, 2));\n    return this.http.put(url, updateData, {\n      headers: headers,\n      observe: 'response'\n    }).pipe(tap(response => {\n      console.log('SUCCÈS - Response:', response);\n    }), catchError(error => {\n      console.error('ÉCHEC - Error détaillé:', error);\n      console.error('Status:', error.status);\n      console.error('Error body:', error.error);\n      // Retourner l'erreur pour que l'appelant puisse la voir\n      return throwError(() => error);\n    }));\n  }\n  // Test avec des données minimales pour identifier le problème\n  testMinimalUpdate(clientId) {\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\n    // Test 1: Seulement le code\n    const minimalData1 = {\n      code: 'TEST001'\n    };\n    console.log('Test 1 - Seulement code:', minimalData1);\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(catchError(() => {\n      console.log('Test 1 échoué, essai test 2...');\n      // Test 2: Seulement la syntax\n      const minimalData2 = {\n        syntax: 'Test Client'\n      };\n      console.log('Test 2 - Seulement syntax:', minimalData2);\n      return this.testRawHttpRequest(clientId, minimalData2).pipe(catchError(() => {\n        console.log('Test 2 échoué, essai test 3...');\n        // Test 3: Objet vide\n        const minimalData3 = {};\n        console.log('Test 3 - Objet vide:', minimalData3);\n        return this.testRawHttpRequest(clientId, minimalData3);\n      }));\n    }));\n  }\n  // Méthode utilitaire pour tester la validation des données client\n  validateClientData(client) {\n    const errors = [];\n    if (client.code) {\n      if (client.code.length < 2 || client.code.length > 20) {\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\n      }\n    }\n    if (client.syntax && client.syntax.length > 100) {\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\n    }\n    if (client.matFiscal) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.matFiscal.length > 50) {\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\n      }\n    }\n    if (client.email) {\n      // Validation de format supprimée - garder seulement la limite de longueur\n      if (client.email.length > 100) {\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\n      }\n    }\n    if (client.telephone) {\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\n      if (!phonePattern.test(client.telephone)) {\n        errors.push('Format de téléphone invalide');\n      }\n    }\n    return {\n      isValid: errors.length === 0,\n      errors\n    };\n  }\n  // Gestion des erreurs\n  handleUpdateError(error) {\n    let errorMessage = 'Erreur lors de la mise à jour du client';\n    console.error('=== ERREUR DE MISE À JOUR ===');\n    console.error('Status:', error.status);\n    console.error('StatusText:', error.statusText);\n    console.error('URL:', error.url);\n    console.error('Error object:', error.error);\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\n    console.error('Full error:', error);\n    console.error('============================');\n    // Analyser la structure de l'erreur en détail\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    const validationErrors = error.error?.errors;\n    const traceId = error.error?.traceId;\n    const type = error.error?.type;\n    console.log('API Error:', apiError);\n    console.log('Validation Errors:', validationErrors);\n    console.log('Validation Errors type:', typeof validationErrors);\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\n    console.log('Trace ID:', traceId);\n    console.log('Error Type:', type);\n    // Essayer de capturer d'autres propriétés d'erreur\n    if (error.error) {\n      console.log('Toutes les propriétés de error.error:');\n      for (const key in error.error) {\n        console.log(`  ${key}:`, error.error[key]);\n      }\n    }\n    if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides pour la mise à jour';\n      // Gestion spécifique des erreurs de validation ASP.NET Core\n      if (validationErrors) {\n        const errorDetails = [];\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\n        if (typeof validationErrors === 'object') {\n          Object.keys(validationErrors).forEach(field => {\n            const fieldErrors = validationErrors[field];\n            if (Array.isArray(fieldErrors)) {\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\n            } else {\n              errorDetails.push(`${field}: ${fieldErrors}`);\n            }\n          });\n        }\n        if (errorDetails.length > 0) {\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\n        }\n      } else if (apiError) {\n        errorMessage += `\\n\\nDétail: ${apiError}`;\n      }\n      // Cas spécifique pour \"One or more validation errors occurred\"\n      if (apiError && apiError.includes('validation errors occurred')) {\n        errorMessage = 'Erreurs de validation:\\n';\n        if (error.error?.errors) {\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\n          Object.keys(error.error.errors).forEach(field => {\n            errorMessage += `- ${field}\\n`;\n          });\n        } else {\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\n        }\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client non trouvé';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\n    } else if (apiError) {\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleCreateError(error) {\n    let errorMessage = 'Erreur lors de la création du client';\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\n    if (apiError) {\n      errorMessage = `Erreur de création: ${apiError}`;\n    } else if (error.status === 0) {\n      errorMessage = 'Impossible de se connecter au serveur';\n    } else if (error.status === 400) {\n      errorMessage = 'Données invalides';\n      if (error.error?.errors) {\n        const validationErrors = Object.values(error.error.errors).flat();\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\n      }\n    } else if (error.status === 401) {\n      errorMessage = 'Session expirée';\n      this.router.navigate(['/login']);\n    } else if (error.status === 409) {\n      errorMessage = 'Un client avec cet ID existe déjà';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    const apiError = error.error?.message || error.error?.title;\n    if (apiError) {\n      errorMessage = apiError;\n    } else if (error.status === 0) {\n      errorMessage = 'Unable to connect to server';\n    } else if (error.status === 400) {\n      errorMessage = 'Invalid request data';\n    } else if (error.status === 401) {\n      errorMessage = 'Unauthorized';\n      this.router.navigate(['/login']);\n    } else if (error.status === 404) {\n      errorMessage = 'Client not found';\n    } else if (error.status === 409) {\n      errorMessage = 'Conflict - client already exists';\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static #_ = this.ɵfac = function ClientService_Factory(t) {\n    return new (t || ClientService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.Router));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: ClientService,\n    factory: ClientService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "tap", "catchError", "map", "ClientService", "constructor", "http", "router", "baseUrl", "dataChange", "isTblLoading", "currentClientSubject", "getClientFromStorage", "currentClient$", "asObservable", "clientData", "localStorage", "getItem", "JSON", "parse", "getHeaders", "token", "getAllClients", "get", "headers", "pipe", "clients", "next", "error", "handleError", "getClientById", "id", "client", "setItem", "stringify", "createClient", "Error", "code", "trim", "email", "validateEmail", "generateGuid", "syntax", "mat<PERSON><PERSON><PERSON>", "telephone", "post", "observe", "response", "newClient", "body", "currentData", "value", "handleCreateError", "updateClient", "actualId", "isCode", "test", "clientByCode", "find", "c", "isGuid", "cleanedClient", "Object", "keys", "for<PERSON>ach", "key", "toString", "length", "put", "status", "existing", "updatedClient", "index", "findIndex", "message", "includes", "simulateLocalUpdate", "deleteClient", "delete", "clearCurrentClient", "deleteSelectedClients", "ids", "console", "log", "updatedData", "filter", "simulateLocalDeletion", "deleteClientsIndividually", "deleteRequests", "observer", "completedCount", "<PERSON><PERSON><PERSON><PERSON>", "errors", "request", "subscribe", "deletedCount", "complete", "push", "setTimeout", "updateData", "removeItem", "data", "getDialogData", "dialogData", "replace", "r", "Math", "random", "v", "re", "testClientId", "clientById", "clientByCodeResolution", "finalIsGuid", "testUpdateValidation", "validation", "validateUpdateData", "<PERSON><PERSON><PERSON><PERSON>", "cleaned", "toUpperCase", "toLowerCase", "undefined", "phonePattern", "debugUpdate", "clientId", "cleanedData", "testRawHttpRequest", "url", "testMinimalUpdate", "minimalData1", "minimalData2", "minimalData3", "validateClientData", "handleUpdateError", "errorMessage", "statusText", "apiError", "title", "validationErrors", "traceId", "type", "errorDetails", "field", "fieldErrors", "Array", "isArray", "err", "join", "navigate", "values", "flat", "_", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "Router", "_2", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\Printexa_Frontend\\src\\app\\admin\\services\\client.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\r\nimport { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';\r\nimport { Router } from '@angular/router';\r\nimport { BehaviorSubject, Observable, throwError, tap, catchError, map } from 'rxjs';\r\nimport { Client, CreateClientSimpleDto, UpdateClientDto } from '../Model/Client';\r\n\r\n@Injectable({\r\n  providedIn: 'root'\r\n})\r\nexport class ClientService {\r\n  private baseUrl: string = 'https://localhost:5001/api/Client/';\r\n  private currentClientSubject: BehaviorSubject<Client | null>;\r\n  public currentClient$: Observable<Client | null>;\r\n  dataChange = new BehaviorSubject<Client[]>([]);\r\n  dialogData!: Client;\r\n  isTblLoading = true;\r\n\r\n  constructor(private http: HttpClient, private router: Router) {\r\n    this.currentClientSubject = new BehaviorSubject<Client | null>(this.getClientFromStorage());\r\n    this.currentClient$ = this.currentClientSubject.asObservable();\r\n  }\r\n\r\n  private getClientFromStorage(): Client | null {\r\n    const clientData = localStorage.getItem('currentClient');\r\n    return clientData ? JSON.parse(clientData) : null;\r\n  }\r\n\r\n  private getHeaders(): HttpHeaders {\r\n    const token = localStorage.getItem('auth_token');\r\n    return new HttpHeaders({\r\n      'Content-Type': 'application/json',\r\n      ...(token ? { 'Authorization': `Bearer ${token}` } : {})\r\n    });\r\n  }\r\n\r\n  // Récupérer tous les clients\r\n  getAllClients(): Observable<Client[]> {\r\n    this.isTblLoading = true;\r\n    return this.http.get<Client[]>(this.baseUrl, { headers: this.getHeaders() }).pipe(\r\n      tap(clients => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next(clients || []);\r\n      }),\r\n      catchError(error => {\r\n        this.isTblLoading = false;\r\n        this.dataChange.next([]);\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Récupérer un client par son ID\r\n  getClientById(id: string): Observable<Client> {\r\n    return this.http.get<Client>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(client => {\r\n        localStorage.setItem('currentClient', JSON.stringify(client));\r\n        this.currentClientSubject.next(client);\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Créer un nouveau client\r\n  createClient(client: CreateClientSimpleDto): Observable<Client> {\r\n    // Validation des données d'entrée\r\n    if (!client) {\r\n      return throwError(() => new Error('Les données du client sont requises'));\r\n    }\r\n\r\n    if (!client.code || client.code.trim() === '') {\r\n      return throwError(() => new Error('Le code client est requis'));\r\n    }\r\n\r\n    if (client.email && !this.validateEmail(client.email)) {\r\n      return throwError(() => new Error('Format d\\'email invalide'));\r\n    }\r\n\r\n    // Préparer les données à envoyer\r\n    const clientData: CreateClientSimpleDto = {\r\n      id: client.id || this.generateGuid(),\r\n      code: client.code.trim(),\r\n      syntax: client.syntax?.trim(),\r\n      matFiscal: client.matFiscal?.trim(),\r\n      email: client.email?.trim(),\r\n      telephone: client.telephone?.trim()\r\n    };\r\n\r\n    return this.http.post<Client>(this.baseUrl, clientData, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        const newClient: Client = response.body;\r\n        if (!newClient) {\r\n          throw new Error('Aucune donnée reçue du serveur');\r\n        }\r\n        return newClient;\r\n      }),\r\n      tap((newClient: Client) => {\r\n        const currentData = this.dataChange.value;\r\n        this.dataChange.next([...currentData, newClient]);\r\n      }),\r\n      catchError(this.handleCreateError)\r\n    );\r\n  }\r\n\r\n  // Mettre à jour un client\r\n  updateClient(id: string, client: UpdateClientDto): Observable<Client> {\r\n    if (!id?.trim() || !client) {\r\n      return throwError(() => new Error('ID et données client requis'));\r\n    }\r\n\r\n    // Résoudre l'ID si c'est un code client\r\n    let actualId = id;\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n    if (isCode) {\r\n      const clientByCode = this.dataChange.value.find(c => c.code === id);\r\n      if (clientByCode) actualId = clientByCode.id;\r\n    }\r\n\r\n    // Valider que l'ID final est un GUID\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    if (!isGuid) {\r\n      return throwError(() => new Error(`ID invalide: ${actualId}`));\r\n    }\r\n\r\n    // Nettoyer les données (supprimer l'id et les champs vides)\r\n    const cleanedClient: UpdateClientDto = {};\r\n    Object.keys(client).forEach(key => {\r\n      if (key !== 'id' && client[key as keyof UpdateClientDto]?.toString().trim()) {\r\n        cleanedClient[key as keyof UpdateClientDto] = client[key as keyof UpdateClientDto];\r\n      }\r\n    });\r\n\r\n    if (Object.keys(cleanedClient).length === 0) {\r\n      return throwError(() => new Error('Aucune donnée à mettre à jour'));\r\n    }\r\n\r\n    return this.http.put(`${this.baseUrl}${actualId}`, cleanedClient, {\r\n      headers: this.getHeaders(),\r\n      observe: 'response'\r\n    }).pipe(\r\n      map((response: any) => {\r\n        // Gérer NoContent (204) ou réponse avec body\r\n        if (response.status === 204 || !response.body) {\r\n          const existing = this.dataChange.value.find(c => c.id === actualId);\r\n          return existing ? { ...existing, ...cleanedClient } : cleanedClient as Client;\r\n        }\r\n        return response.body;\r\n      }),\r\n      tap((updatedClient: Client) => {\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const index = currentData.findIndex(c => c.id === actualId);\r\n        if (index !== -1) {\r\n          currentData[index] = updatedClient;\r\n          this.dataChange.next([...currentData]);\r\n        }\r\n      }),\r\n      catchError(error => {\r\n        // Mode simulation en cas d'erreur\r\n        if (error.status === 0 || (error.status === 400 && error.error?.message?.includes('validation'))) {\r\n          return this.simulateLocalUpdate(actualId, cleanedClient);\r\n        }\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Supprimer un client\r\n  deleteClient(id: string): Observable<void> {\r\n    return this.http.delete<void>(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n      tap(() => {\r\n        this.clearCurrentClient();\r\n      }),\r\n      catchError(this.handleError)\r\n    );\r\n  }\r\n\r\n  // Supprimer plusieurs clients\r\n  deleteSelectedClients(ids: string[]): Observable<any> {\r\n    if (!ids || ids.length === 0) {\r\n      return throwError(() => new Error('Aucun ID de client fourni pour la suppression'));\r\n    }\r\n\r\n    console.log('Tentative de suppression des clients:', ids);\r\n\r\n    return this.http.delete(`${this.baseUrl}delete-selected`, {\r\n      headers: this.getHeaders(),\r\n      body: ids\r\n    }).pipe(\r\n      tap(() => {\r\n        console.log('Suppression en masse réussie');\r\n        // Mettre à jour les données locales\r\n        const currentData = this.dataChange.value;\r\n        const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n        this.dataChange.next(updatedData);\r\n      }),\r\n      catchError(error => {\r\n        console.error('Erreur lors de la suppression en masse:', error);\r\n\r\n        // Si le serveur n'est pas disponible, simuler la suppression localement\r\n        if (error.status === 0) {\r\n          console.log('Serveur non disponible, simulation de la suppression...');\r\n          return this.simulateLocalDeletion(ids);\r\n        }\r\n\r\n        // Si l'endpoint de suppression en masse ne fonctionne pas, essayer la suppression individuelle\r\n        if (error.status === 500 || error.status === 404) {\r\n          console.log('Tentative de suppression individuelle...');\r\n          return this.deleteClientsIndividually(ids);\r\n        }\r\n\r\n        return this.handleError(error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode de fallback pour supprimer les clients individuellement\r\n  private deleteClientsIndividually(ids: string[]): Observable<any> {\r\n    const deleteRequests = ids.map(id =>\r\n      this.http.delete(`${this.baseUrl}${id}`, { headers: this.getHeaders() }).pipe(\r\n        catchError(error => {\r\n          console.error(`Erreur lors de la suppression du client ${id}:`, error);\r\n          return throwError(() => error);\r\n        })\r\n      )\r\n    );\r\n\r\n    // Utiliser forkJoin pour exécuter toutes les suppressions en parallèle\r\n    return new Observable(observer => {\r\n      let completedCount = 0;\r\n      let hasError = false;\r\n      const errors: any[] = [];\r\n\r\n      deleteRequests.forEach((request, index) => {\r\n        request.subscribe({\r\n          next: () => {\r\n            completedCount++;\r\n            if (completedCount === ids.length && !hasError) {\r\n              // Mettre à jour les données locales\r\n              const currentData = this.dataChange.value;\r\n              const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n              this.dataChange.next(updatedData);\r\n              observer.next({ deletedCount: completedCount });\r\n              observer.complete();\r\n            }\r\n          },\r\n          error: (error) => {\r\n            hasError = true;\r\n            errors.push({ id: ids[index], error });\r\n            if (completedCount + errors.length === ids.length) {\r\n              observer.error({\r\n                message: 'Certains clients n\\'ont pas pu être supprimés',\r\n                errors,\r\n                deletedCount: completedCount\r\n              });\r\n            }\r\n          }\r\n        });\r\n      });\r\n    });\r\n  }\r\n\r\n  // Simuler la suppression locale quand le serveur n'est pas disponible\r\n  private simulateLocalDeletion(ids: string[]): Observable<any> {\r\n    console.log('Simulation de la suppression locale pour les IDs:', ids);\r\n\r\n    // Mettre à jour les données locales\r\n    const currentData = this.dataChange.value;\r\n    const updatedData = currentData.filter(client => !ids.includes(client.id));\r\n    this.dataChange.next(updatedData);\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable(observer => {\r\n      setTimeout(() => {\r\n        observer.next({\r\n          deletedCount: ids.length,\r\n          message: 'Suppression simulée localement (serveur non disponible)'\r\n        });\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Simuler la mise à jour locale quand le serveur n'est pas disponible\r\n  private simulateLocalUpdate(id: string, updateData: UpdateClientDto): Observable<Client> {\r\n    console.log('Simulation de la mise à jour locale pour l\\'ID:', id, updateData);\r\n\r\n    // Trouver et mettre à jour le client dans les données locales\r\n    const currentData = this.dataChange.value;\r\n    const index = currentData.findIndex(client => client.id === id);\r\n\r\n    if (index === -1) {\r\n      return throwError(() => new Error('Client non trouvé pour la mise à jour'));\r\n    }\r\n\r\n    // Créer le client mis à jour\r\n    const updatedClient: Client = {\r\n      ...currentData[index],\r\n      ...updateData\r\n    };\r\n\r\n    // Mettre à jour les données locales\r\n    currentData[index] = updatedClient;\r\n    this.dataChange.next([...currentData]);\r\n\r\n    // Mettre à jour le client courant si c'est le même\r\n    if (this.currentClientSubject.value?.id === id) {\r\n      this.currentClientSubject.next(updatedClient);\r\n      localStorage.setItem('currentClient', JSON.stringify(updatedClient));\r\n    }\r\n\r\n    // Retourner un Observable qui simule le succès\r\n    return new Observable<Client>(observer => {\r\n      setTimeout(() => {\r\n        observer.next(updatedClient);\r\n        observer.complete();\r\n      }, 500); // Simuler un délai réseau\r\n    });\r\n  }\r\n\r\n  // Effacer le client courant\r\n  clearCurrentClient(): void {\r\n    localStorage.removeItem('currentClient');\r\n    this.currentClientSubject.next(null);\r\n  }\r\n\r\n  get data(): Client[] {\r\n    return this.dataChange.value;\r\n  }\r\n\r\n  getDialogData() {\r\n    return this.dialogData;\r\n  }\r\n\r\n  // Méthodes utilitaires\r\n  private generateGuid(): string {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {\r\n      const r = Math.random() * 16 | 0,\r\n        v = c === 'x' ? r : (r & 0x3 | 0x8);\r\n      return v.toString(16);\r\n    });\r\n  }\r\n\r\n  private validateEmail(email: string): boolean {\r\n    const re = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n    return re.test(email);\r\n  }\r\n\r\n  // Méthode de test pour diagnostiquer les problèmes d'ID\r\n  public testClientId(id: string): void {\r\n    console.log('=== TEST ID CLIENT ===');\r\n    console.log('ID fourni:', id);\r\n    console.log('Type:', typeof id);\r\n    console.log('Longueur:', id.length);\r\n    console.log('Est GUID:', /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id));\r\n    console.log('Est code:', /^[A-Z]{3}-\\d{3}$/.test(id));\r\n\r\n    const currentData = this.dataChange.value;\r\n    console.log('Nombre de clients dans les données:', currentData.length);\r\n\r\n    const clientById = currentData.find(c => c.id === id);\r\n    const clientByCode = currentData.find(c => c.code === id);\r\n\r\n    console.log('Client trouvé par ID:', clientById ? 'OUI' : 'NON');\r\n    console.log('Client trouvé par code:', clientByCode ? 'OUI' : 'NON');\r\n\r\n    if (clientById) {\r\n      console.log('Client par ID:', clientById);\r\n    }\r\n    if (clientByCode) {\r\n      console.log('Client par code:', clientByCode);\r\n    }\r\n\r\n    // Test de résolution d'ID\r\n    console.log('--- Test de résolution ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(id);\r\n\r\n    let actualId = id;\r\n    if (isCode && !isGuid) {\r\n      const clientByCodeResolution = currentData.find(c => c.code === id);\r\n      if (clientByCodeResolution && clientByCodeResolution.id !== id) {\r\n        actualId = clientByCodeResolution.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    const finalIsGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(actualId);\r\n    console.log('ID final:', actualId);\r\n    console.log('ID final est GUID:', finalIsGuid);\r\n    console.log('===================');\r\n  }\r\n\r\n  // Méthode de test pour valider une mise à jour sans l'envoyer\r\n  public testUpdateValidation(id: string, updateData: UpdateClientDto): void {\r\n    console.log('=== TEST VALIDATION MISE À JOUR ===');\r\n    console.log('ID:', id);\r\n    console.log('Données:', updateData);\r\n\r\n    const validation = this.validateUpdateData(updateData);\r\n    console.log('Validation réussie:', validation.isValid);\r\n    if (!validation.isValid) {\r\n      console.log('Erreurs:', validation.errors);\r\n    }\r\n\r\n    console.log('Données nettoyées qui seraient envoyées:');\r\n    const cleaned: UpdateClientDto = {};\r\n    if (updateData.code && updateData.code.trim()) cleaned.code = updateData.code.trim();\r\n    if (updateData.syntax && updateData.syntax.trim()) cleaned.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal && updateData.matFiscal.trim()) cleaned.matFiscal = updateData.matFiscal.trim().toUpperCase();\r\n    if (updateData.email && updateData.email.trim()) cleaned.email = updateData.email.trim().toLowerCase();\r\n    if (updateData.telephone && updateData.telephone.trim()) cleaned.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Cleaned data:', cleaned);\r\n    console.log('URL qui serait utilisée:', `${this.baseUrl}${id}`);\r\n    console.log('================================');\r\n  }\r\n\r\n  // Validation stricte pour la mise à jour\r\n  private validateUpdateData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    // L'ID est déjà validé avant d'appeler cette méthode, pas besoin de le revalider\r\n\r\n    // Validation des champs\r\n    if (client.code !== undefined) {\r\n      if (!client.code || client.code.trim() === '') {\r\n        errors.push('Code ne peut pas être vide');\r\n      } else if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Code doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax !== undefined && client.syntax !== null && client.syntax.length > 100) {\r\n      errors.push('Raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal !== undefined && client.matFiscal !== null) {\r\n      // Validation de format supprimée - accepter tout format\r\n      if (client.matFiscal.trim() !== '' && client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email !== undefined && client.email !== null) {\r\n      if (client.email.trim() !== '') {\r\n        // Validation de format supprimée - garder seulement la limite de longueur\r\n        if (client.email.length > 100) {\r\n          errors.push('Email ne peut pas dépasser 100 caractères');\r\n        }\r\n      }\r\n    }\r\n\r\n    if (client.telephone !== undefined && client.telephone !== null) {\r\n      if (client.telephone.trim() !== '') {\r\n        const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n        if (!phonePattern.test(client.telephone)) {\r\n          errors.push('Format de téléphone invalide');\r\n        }\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Méthode publique pour tester une mise à jour complète\r\n  public debugUpdate(clientId: string, updateData: any): void {\r\n    console.log('=== DEBUG MISE À JOUR COMPLÈTE ===');\r\n\r\n    // Test 1: Validation de l'ID\r\n    this.testClientId(clientId);\r\n\r\n    // Test 2: Validation des données\r\n    this.testUpdateValidation(clientId, updateData);\r\n\r\n    // Test 3: Simulation de la requête\r\n    console.log('--- Simulation de la requête ---');\r\n    const isGuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(clientId);\r\n    const isCode = /^[A-Z]{3}-\\d{3}$/.test(clientId);\r\n\r\n    let actualId = clientId;\r\n    if (isCode && !isGuid) {\r\n      const currentData = this.dataChange.value;\r\n      const clientByCode = currentData.find(c => c.code === clientId);\r\n      if (clientByCode) {\r\n        actualId = clientByCode.id;\r\n        console.log('ID résolu:', actualId);\r\n      }\r\n    }\r\n\r\n    console.log('URL finale:', `${this.baseUrl}${actualId}`);\r\n    console.log('Méthode: PUT');\r\n    console.log('Headers:', this.getHeaders());\r\n\r\n    const cleanedData: any = {};\r\n    if (updateData.code) cleanedData.code = updateData.code.trim();\r\n    if (updateData.syntax) cleanedData.syntax = updateData.syntax.trim();\r\n    if (updateData.matFiscal) cleanedData.matFiscal = updateData.matFiscal.trim(); // Plus de toUpperCase\r\n    if (updateData.email) cleanedData.email = updateData.email.trim(); // Plus de toLowerCase\r\n    if (updateData.telephone) cleanedData.telephone = updateData.telephone.trim();\r\n\r\n    console.log('Body final:', JSON.stringify(cleanedData, null, 2));\r\n    console.log('================================');\r\n  }\r\n\r\n  // Méthode pour tester la requête HTTP brute\r\n  public testRawHttpRequest(clientId: string, updateData: any): Observable<any> {\r\n    console.log('=== TEST REQUÊTE HTTP BRUTE ===');\r\n\r\n    const url = `${this.baseUrl}${clientId}`;\r\n    const headers = this.getHeaders();\r\n\r\n    console.log('URL:', url);\r\n    console.log('Headers:', headers);\r\n    console.log('Body:', JSON.stringify(updateData, null, 2));\r\n\r\n    return this.http.put(url, updateData, {\r\n      headers: headers,\r\n      observe: 'response'\r\n    }).pipe(\r\n      tap(response => {\r\n        console.log('SUCCÈS - Response:', response);\r\n      }),\r\n      catchError(error => {\r\n        console.error('ÉCHEC - Error détaillé:', error);\r\n        console.error('Status:', error.status);\r\n        console.error('Error body:', error.error);\r\n\r\n        // Retourner l'erreur pour que l'appelant puisse la voir\r\n        return throwError(() => error);\r\n      })\r\n    );\r\n  }\r\n\r\n  // Test avec des données minimales pour identifier le problème\r\n  public testMinimalUpdate(clientId: string): Observable<any> {\r\n    console.log('=== TEST MISE À JOUR MINIMALE ===');\r\n\r\n    // Test 1: Seulement le code\r\n    const minimalData1 = { code: 'TEST001' };\r\n    console.log('Test 1 - Seulement code:', minimalData1);\r\n\r\n    return this.testRawHttpRequest(clientId, minimalData1).pipe(\r\n      catchError(() => {\r\n        console.log('Test 1 échoué, essai test 2...');\r\n\r\n        // Test 2: Seulement la syntax\r\n        const minimalData2 = { syntax: 'Test Client' };\r\n        console.log('Test 2 - Seulement syntax:', minimalData2);\r\n\r\n        return this.testRawHttpRequest(clientId, minimalData2).pipe(\r\n          catchError(() => {\r\n            console.log('Test 2 échoué, essai test 3...');\r\n\r\n            // Test 3: Objet vide\r\n            const minimalData3 = {};\r\n            console.log('Test 3 - Objet vide:', minimalData3);\r\n\r\n            return this.testRawHttpRequest(clientId, minimalData3);\r\n          })\r\n        );\r\n      })\r\n    );\r\n  }\r\n\r\n  // Méthode utilitaire pour tester la validation des données client\r\n  public validateClientData(client: UpdateClientDto): { isValid: boolean; errors: string[] } {\r\n    const errors: string[] = [];\r\n\r\n    if (client.code) {\r\n      if (client.code.length < 2 || client.code.length > 20) {\r\n        errors.push('Le code client doit contenir entre 2 et 20 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.syntax && client.syntax.length > 100) {\r\n      errors.push('La raison sociale ne peut pas dépasser 100 caractères');\r\n    }\r\n\r\n    if (client.matFiscal) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.matFiscal.length > 50) {\r\n        errors.push('Matricule fiscal ne peut pas dépasser 50 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.email) {\r\n      // Validation de format supprimée - garder seulement la limite de longueur\r\n      if (client.email.length > 100) {\r\n        errors.push('L\\'email ne peut pas dépasser 100 caractères');\r\n      }\r\n    }\r\n\r\n    if (client.telephone) {\r\n      const phonePattern = /^[+]?[0-9\\s\\-\\(\\)\\.]{8,20}$/;\r\n      if (!phonePattern.test(client.telephone)) {\r\n        errors.push('Format de téléphone invalide');\r\n      }\r\n    }\r\n\r\n    return {\r\n      isValid: errors.length === 0,\r\n      errors\r\n    };\r\n  }\r\n\r\n  // Gestion des erreurs\r\n  private handleUpdateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la mise à jour du client';\r\n\r\n    console.error('=== ERREUR DE MISE À JOUR ===');\r\n    console.error('Status:', error.status);\r\n    console.error('StatusText:', error.statusText);\r\n    console.error('URL:', error.url);\r\n    console.error('Error object:', error.error);\r\n    console.error('Error keys:', error.error ? Object.keys(error.error) : 'No error object');\r\n    console.error('Full error:', error);\r\n    console.error('============================');\r\n\r\n    // Analyser la structure de l'erreur en détail\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n    const validationErrors = error.error?.errors;\r\n    const traceId = error.error?.traceId;\r\n    const type = error.error?.type;\r\n\r\n    console.log('API Error:', apiError);\r\n    console.log('Validation Errors:', validationErrors);\r\n    console.log('Validation Errors type:', typeof validationErrors);\r\n    console.log('Validation Errors keys:', validationErrors ? Object.keys(validationErrors) : 'No validation errors');\r\n    console.log('Trace ID:', traceId);\r\n    console.log('Error Type:', type);\r\n\r\n    // Essayer de capturer d'autres propriétés d'erreur\r\n    if (error.error) {\r\n      console.log('Toutes les propriétés de error.error:');\r\n      for (const key in error.error) {\r\n        console.log(`  ${key}:`, error.error[key]);\r\n      }\r\n    }\r\n\r\n    if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides pour la mise à jour';\r\n\r\n      // Gestion spécifique des erreurs de validation ASP.NET Core\r\n      if (validationErrors) {\r\n        const errorDetails: string[] = [];\r\n\r\n        // Si c'est un objet avec des propriétés (format ASP.NET Core)\r\n        if (typeof validationErrors === 'object') {\r\n          Object.keys(validationErrors).forEach(field => {\r\n            const fieldErrors = validationErrors[field];\r\n            if (Array.isArray(fieldErrors)) {\r\n              fieldErrors.forEach(err => errorDetails.push(`${field}: ${err}`));\r\n            } else {\r\n              errorDetails.push(`${field}: ${fieldErrors}`);\r\n            }\r\n          });\r\n        }\r\n\r\n        if (errorDetails.length > 0) {\r\n          errorMessage += `\\n\\nDétails:\\n${errorDetails.join('\\n')}`;\r\n        }\r\n      } else if (apiError) {\r\n        errorMessage += `\\n\\nDétail: ${apiError}`;\r\n      }\r\n\r\n      // Cas spécifique pour \"One or more validation errors occurred\"\r\n      if (apiError && apiError.includes('validation errors occurred')) {\r\n        errorMessage = 'Erreurs de validation:\\n';\r\n        if (error.error?.errors) {\r\n          errorMessage += 'Veuillez vérifier les champs suivants:\\n';\r\n          Object.keys(error.error.errors).forEach(field => {\r\n            errorMessage += `- ${field}\\n`;\r\n          });\r\n        } else {\r\n          errorMessage += 'Veuillez vérifier tous les champs du formulaire.';\r\n        }\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client non trouvé';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflit - un autre client avec ces données existe déjà';\r\n    } else if (apiError) {\r\n      errorMessage = `Erreur de mise à jour: ${apiError}`;\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleCreateError(error: HttpErrorResponse) {\r\n    let errorMessage = 'Erreur lors de la création du client';\r\n    const apiError = error.error?.message || error.error?.title || error.error?.error;\r\n\r\n    if (apiError) {\r\n      errorMessage = `Erreur de création: ${apiError}`;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Impossible de se connecter au serveur';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Données invalides';\r\n      if (error.error?.errors) {\r\n        const validationErrors = Object.values(error.error.errors).flat();\r\n        errorMessage += ` Détails: ${validationErrors.join(', ')}`;\r\n      }\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Session expirée';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Un client avec cet ID existe déjà';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n\r\n  private handleError(error: HttpErrorResponse) {\r\n    let errorMessage = 'An error occurred';\r\n    const apiError = error.error?.message || error.error?.title;\r\n\r\n    if (apiError) {\r\n      errorMessage = apiError;\r\n    } else if (error.status === 0) {\r\n      errorMessage = 'Unable to connect to server';\r\n    } else if (error.status === 400) {\r\n      errorMessage = 'Invalid request data';\r\n    } else if (error.status === 401) {\r\n      errorMessage = 'Unauthorized';\r\n      this.router.navigate(['/login']);\r\n    } else if (error.status === 404) {\r\n      errorMessage = 'Client not found';\r\n    } else if (error.status === 409) {\r\n      errorMessage = 'Conflict - client already exists';\r\n    }\r\n\r\n    return throwError(() => new Error(errorMessage));\r\n  }\r\n}"], "mappings": "AACA,SAAwCA,WAAW,QAAQ,sBAAsB;AAEjF,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAEC,GAAG,EAAEC,UAAU,EAAEC,GAAG,QAAQ,MAAM;;;;AAMpF,OAAM,MAAOC,aAAa;EAQxBC,YAAoBC,IAAgB,EAAUC,MAAc;IAAxC,KAAAD,IAAI,GAAJA,IAAI;IAAsB,KAAAC,MAAM,GAANA,MAAM;IAP5C,KAAAC,OAAO,GAAW,oCAAoC;IAG9D,KAAAC,UAAU,GAAG,IAAIX,eAAe,CAAW,EAAE,CAAC;IAE9C,KAAAY,YAAY,GAAG,IAAI;IAGjB,IAAI,CAACC,oBAAoB,GAAG,IAAIb,eAAe,CAAgB,IAAI,CAACc,oBAAoB,EAAE,CAAC;IAC3F,IAAI,CAACC,cAAc,GAAG,IAAI,CAACF,oBAAoB,CAACG,YAAY,EAAE;EAChE;EAEQF,oBAAoBA,CAAA;IAC1B,MAAMG,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;IACxD,OAAOF,UAAU,GAAGG,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC,GAAG,IAAI;EACnD;EAEQK,UAAUA,CAAA;IAChB,MAAMC,KAAK,GAAGL,YAAY,CAACC,OAAO,CAAC,YAAY,CAAC;IAChD,OAAO,IAAIpB,WAAW,CAAC;MACrB,cAAc,EAAE,kBAAkB;MAClC,IAAIwB,KAAK,GAAG;QAAE,eAAe,EAAE,UAAUA,KAAK;MAAE,CAAE,GAAG,EAAE;KACxD,CAAC;EACJ;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACZ,YAAY,GAAG,IAAI;IACxB,OAAO,IAAI,CAACJ,IAAI,CAACiB,GAAG,CAAW,IAAI,CAACf,OAAO,EAAE;MAAEgB,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC/ExB,GAAG,CAACyB,OAAO,IAAG;MACZ,IAAI,CAAChB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAACD,OAAO,IAAI,EAAE,CAAC;IACrC,CAAC,CAAC,EACFxB,UAAU,CAAC0B,KAAK,IAAG;MACjB,IAAI,CAAClB,YAAY,GAAG,KAAK;MACzB,IAAI,CAACD,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;MACxB,OAAO,IAAI,CAACE,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAE,aAAaA,CAACC,EAAU;IACtB,OAAO,IAAI,CAACzB,IAAI,CAACiB,GAAG,CAAS,GAAG,IAAI,CAACf,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACvFxB,GAAG,CAAC+B,MAAM,IAAG;MACXhB,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACF,MAAM,CAAC,CAAC;MAC7D,IAAI,CAACrB,oBAAoB,CAACgB,IAAI,CAACK,MAAM,CAAC;IACxC,CAAC,CAAC,EACF9B,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAM,YAAYA,CAACH,MAA6B;IACxC;IACA,IAAI,CAACA,MAAM,EAAE;MACX,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,qCAAqC,CAAC,CAAC;;IAG3E,IAAI,CAACJ,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;MAC7C,OAAOtC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,2BAA2B,CAAC,CAAC;;IAGjE,IAAIJ,MAAM,CAACO,KAAK,IAAI,CAAC,IAAI,CAACC,aAAa,CAACR,MAAM,CAACO,KAAK,CAAC,EAAE;MACrD,OAAOvC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE;IACA,MAAMrB,UAAU,GAA0B;MACxCgB,EAAE,EAAEC,MAAM,CAACD,EAAE,IAAI,IAAI,CAACU,YAAY,EAAE;MACpCJ,IAAI,EAAEL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE;MACxBI,MAAM,EAAEV,MAAM,CAACU,MAAM,EAAEJ,IAAI,EAAE;MAC7BK,SAAS,EAAEX,MAAM,CAACW,SAAS,EAAEL,IAAI,EAAE;MACnCC,KAAK,EAAEP,MAAM,CAACO,KAAK,EAAED,IAAI,EAAE;MAC3BM,SAAS,EAAEZ,MAAM,CAACY,SAAS,EAAEN,IAAI;KAClC;IAED,OAAO,IAAI,CAAChC,IAAI,CAACuC,IAAI,CAAS,IAAI,CAACrC,OAAO,EAAEO,UAAU,EAAE;MACtDS,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB,MAAMC,SAAS,GAAWD,QAAQ,CAACE,IAAI;MACvC,IAAI,CAACD,SAAS,EAAE;QACd,MAAM,IAAIZ,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,OAAOY,SAAS;IAClB,CAAC,CAAC,EACF/C,GAAG,CAAE+C,SAAiB,IAAI;MACxB,MAAME,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,IAAI,CAAC1C,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,EAAEF,SAAS,CAAC,CAAC;IACnD,CAAC,CAAC,EACF9C,UAAU,CAAC,IAAI,CAACkD,iBAAiB,CAAC,CACnC;EACH;EAEA;EACAC,YAAYA,CAACtB,EAAU,EAAEC,MAAuB;IAC9C,IAAI,CAACD,EAAE,EAAEO,IAAI,EAAE,IAAI,CAACN,MAAM,EAAE;MAC1B,OAAOhC,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,IAAIkB,QAAQ,GAAGvB,EAAE;IACjB,MAAMwB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACzB,EAAE,CAAC;IAC1C,IAAIwB,MAAM,EAAE;MACV,MAAME,YAAY,GAAG,IAAI,CAAChD,UAAU,CAAC0C,KAAK,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAI0B,YAAY,EAAEH,QAAQ,GAAGG,YAAY,CAAC1B,EAAE;;IAG9C;IACA,MAAM6B,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACF,QAAQ,CAAC;IAC/F,IAAI,CAACM,MAAM,EAAE;MACX,OAAO5D,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,gBAAgBkB,QAAQ,EAAE,CAAC,CAAC;;IAGhE;IACA,MAAMO,aAAa,GAAoB,EAAE;IACzCC,MAAM,CAACC,IAAI,CAAC/B,MAAM,CAAC,CAACgC,OAAO,CAACC,GAAG,IAAG;MAChC,IAAIA,GAAG,KAAK,IAAI,IAAIjC,MAAM,CAACiC,GAA4B,CAAC,EAAEC,QAAQ,EAAE,CAAC5B,IAAI,EAAE,EAAE;QAC3EuB,aAAa,CAACI,GAA4B,CAAC,GAAGjC,MAAM,CAACiC,GAA4B,CAAC;;IAEtF,CAAC,CAAC;IAEF,IAAIH,MAAM,CAACC,IAAI,CAACF,aAAa,CAAC,CAACM,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAOnE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+BAA+B,CAAC,CAAC;;IAGrE,OAAO,IAAI,CAAC9B,IAAI,CAAC8D,GAAG,CAAC,GAAG,IAAI,CAAC5D,OAAO,GAAG8C,QAAQ,EAAE,EAAEO,aAAa,EAAE;MAChErC,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B0B,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLtB,GAAG,CAAE4C,QAAa,IAAI;MACpB;MACA,IAAIA,QAAQ,CAACsB,MAAM,KAAK,GAAG,IAAI,CAACtB,QAAQ,CAACE,IAAI,EAAE;QAC7C,MAAMqB,QAAQ,GAAG,IAAI,CAAC7D,UAAU,CAAC0C,KAAK,CAACO,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKuB,QAAQ,CAAC;QACnE,OAAOgB,QAAQ,GAAG;UAAE,GAAGA,QAAQ;UAAE,GAAGT;QAAa,CAAE,GAAGA,aAAuB;;MAE/E,OAAOd,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,EACFhD,GAAG,CAAEsE,aAAqB,IAAI;MAC5B;MACA,MAAMrB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMqB,KAAK,GAAGtB,WAAW,CAACuB,SAAS,CAACd,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKuB,QAAQ,CAAC;MAC3D,IAAIkB,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBtB,WAAW,CAACsB,KAAK,CAAC,GAAGD,aAAa;QAClC,IAAI,CAAC9D,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;;IAE1C,CAAC,CAAC,EACFhD,UAAU,CAAC0B,KAAK,IAAG;MACjB;MACA,IAAIA,KAAK,CAACyC,MAAM,KAAK,CAAC,IAAKzC,KAAK,CAACyC,MAAM,KAAK,GAAG,IAAIzC,KAAK,CAACA,KAAK,EAAE8C,OAAO,EAAEC,QAAQ,CAAC,YAAY,CAAE,EAAE;QAChG,OAAO,IAAI,CAACC,mBAAmB,CAACtB,QAAQ,EAAEO,aAAa,CAAC;;MAE1D,OAAO,IAAI,CAAChC,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACAiD,YAAYA,CAAC9C,EAAU;IACrB,OAAO,IAAI,CAACzB,IAAI,CAACwE,MAAM,CAAO,GAAG,IAAI,CAACtE,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CACxFxB,GAAG,CAAC,MAAK;MACP,IAAI,CAAC8E,kBAAkB,EAAE;IAC3B,CAAC,CAAC,EACF7E,UAAU,CAAC,IAAI,CAAC2B,WAAW,CAAC,CAC7B;EACH;EAEA;EACAmD,qBAAqBA,CAACC,GAAa;IACjC,IAAI,CAACA,GAAG,IAAIA,GAAG,CAACd,MAAM,KAAK,CAAC,EAAE;MAC5B,OAAOnE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,+CAA+C,CAAC,CAAC;;IAGrF8C,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAEF,GAAG,CAAC;IAEzD,OAAO,IAAI,CAAC3E,IAAI,CAACwE,MAAM,CAAC,GAAG,IAAI,CAACtE,OAAO,iBAAiB,EAAE;MACxDgB,OAAO,EAAE,IAAI,CAACJ,UAAU,EAAE;MAC1B6B,IAAI,EAAEgC;KACP,CAAC,CAACxD,IAAI,CACLxB,GAAG,CAAC,MAAK;MACPiF,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAC3C;MACA,MAAMjC,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMiC,WAAW,GAAGlC,WAAW,CAACmC,MAAM,CAACrD,MAAM,IAAI,CAACiD,GAAG,CAACN,QAAQ,CAAC3C,MAAM,CAACD,EAAE,CAAC,CAAC;MAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACyD,WAAW,CAAC;IACnC,CAAC,CAAC,EACFlF,UAAU,CAAC0B,KAAK,IAAG;MACjBsD,OAAO,CAACtD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAE/D;MACA,IAAIA,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;QACtBa,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC;QACtE,OAAO,IAAI,CAACG,qBAAqB,CAACL,GAAG,CAAC;;MAGxC;MACA,IAAIrD,KAAK,CAACyC,MAAM,KAAK,GAAG,IAAIzC,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;QAChDa,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;QACvD,OAAO,IAAI,CAACI,yBAAyB,CAACN,GAAG,CAAC;;MAG5C,OAAO,IAAI,CAACpD,WAAW,CAACD,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACQ2D,yBAAyBA,CAACN,GAAa;IAC7C,MAAMO,cAAc,GAAGP,GAAG,CAAC9E,GAAG,CAAC4B,EAAE,IAC/B,IAAI,CAACzB,IAAI,CAACwE,MAAM,CAAC,GAAG,IAAI,CAACtE,OAAO,GAAGuB,EAAE,EAAE,EAAE;MAAEP,OAAO,EAAE,IAAI,CAACJ,UAAU;IAAE,CAAE,CAAC,CAACK,IAAI,CAC3EvB,UAAU,CAAC0B,KAAK,IAAG;MACjBsD,OAAO,CAACtD,KAAK,CAAC,2CAA2CG,EAAE,GAAG,EAAEH,KAAK,CAAC;MACtE,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH,CACF;IAED;IACA,OAAO,IAAI7B,UAAU,CAAC0F,QAAQ,IAAG;MAC/B,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,QAAQ,GAAG,KAAK;MACpB,MAAMC,MAAM,GAAU,EAAE;MAExBJ,cAAc,CAACxB,OAAO,CAAC,CAAC6B,OAAO,EAAErB,KAAK,KAAI;QACxCqB,OAAO,CAACC,SAAS,CAAC;UAChBnE,IAAI,EAAEA,CAAA,KAAK;YACT+D,cAAc,EAAE;YAChB,IAAIA,cAAc,KAAKT,GAAG,CAACd,MAAM,IAAI,CAACwB,QAAQ,EAAE;cAC9C;cACA,MAAMzC,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;cACzC,MAAMiC,WAAW,GAAGlC,WAAW,CAACmC,MAAM,CAACrD,MAAM,IAAI,CAACiD,GAAG,CAACN,QAAQ,CAAC3C,MAAM,CAACD,EAAE,CAAC,CAAC;cAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACyD,WAAW,CAAC;cACjCK,QAAQ,CAAC9D,IAAI,CAAC;gBAAEoE,YAAY,EAAEL;cAAc,CAAE,CAAC;cAC/CD,QAAQ,CAACO,QAAQ,EAAE;;UAEvB,CAAC;UACDpE,KAAK,EAAGA,KAAK,IAAI;YACf+D,QAAQ,GAAG,IAAI;YACfC,MAAM,CAACK,IAAI,CAAC;cAAElE,EAAE,EAAEkD,GAAG,CAACT,KAAK,CAAC;cAAE5C;YAAK,CAAE,CAAC;YACtC,IAAI8D,cAAc,GAAGE,MAAM,CAACzB,MAAM,KAAKc,GAAG,CAACd,MAAM,EAAE;cACjDsB,QAAQ,CAAC7D,KAAK,CAAC;gBACb8C,OAAO,EAAE,+CAA+C;gBACxDkB,MAAM;gBACNG,YAAY,EAAEL;eACf,CAAC;;UAEN;SACD,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;EACQJ,qBAAqBA,CAACL,GAAa;IACzCC,OAAO,CAACC,GAAG,CAAC,mDAAmD,EAAEF,GAAG,CAAC;IAErE;IACA,MAAM/B,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAMiC,WAAW,GAAGlC,WAAW,CAACmC,MAAM,CAACrD,MAAM,IAAI,CAACiD,GAAG,CAACN,QAAQ,CAAC3C,MAAM,CAACD,EAAE,CAAC,CAAC;IAC1E,IAAI,CAACtB,UAAU,CAACkB,IAAI,CAACyD,WAAW,CAAC;IAEjC;IACA,OAAO,IAAIrF,UAAU,CAAC0F,QAAQ,IAAG;MAC/BS,UAAU,CAAC,MAAK;QACdT,QAAQ,CAAC9D,IAAI,CAAC;UACZoE,YAAY,EAAEd,GAAG,CAACd,MAAM;UACxBO,OAAO,EAAE;SACV,CAAC;QACFe,QAAQ,CAACO,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACQpB,mBAAmBA,CAAC7C,EAAU,EAAEoE,UAA2B;IACjEjB,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAEpD,EAAE,EAAEoE,UAAU,CAAC;IAE9E;IACA,MAAMjD,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC,MAAMqB,KAAK,GAAGtB,WAAW,CAACuB,SAAS,CAACzC,MAAM,IAAIA,MAAM,CAACD,EAAE,KAAKA,EAAE,CAAC;IAE/D,IAAIyC,KAAK,KAAK,CAAC,CAAC,EAAE;MAChB,OAAOxE,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAAC,uCAAuC,CAAC,CAAC;;IAG7E;IACA,MAAMmC,aAAa,GAAW;MAC5B,GAAGrB,WAAW,CAACsB,KAAK,CAAC;MACrB,GAAG2B;KACJ;IAED;IACAjD,WAAW,CAACsB,KAAK,CAAC,GAAGD,aAAa;IAClC,IAAI,CAAC9D,UAAU,CAACkB,IAAI,CAAC,CAAC,GAAGuB,WAAW,CAAC,CAAC;IAEtC;IACA,IAAI,IAAI,CAACvC,oBAAoB,CAACwC,KAAK,EAAEpB,EAAE,KAAKA,EAAE,EAAE;MAC9C,IAAI,CAACpB,oBAAoB,CAACgB,IAAI,CAAC4C,aAAa,CAAC;MAC7CvD,YAAY,CAACiB,OAAO,CAAC,eAAe,EAAEf,IAAI,CAACgB,SAAS,CAACqC,aAAa,CAAC,CAAC;;IAGtE;IACA,OAAO,IAAIxE,UAAU,CAAS0F,QAAQ,IAAG;MACvCS,UAAU,CAAC,MAAK;QACdT,QAAQ,CAAC9D,IAAI,CAAC4C,aAAa,CAAC;QAC5BkB,QAAQ,CAACO,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;IACX,CAAC,CAAC;EACJ;EAEA;EACAjB,kBAAkBA,CAAA;IAChB/D,YAAY,CAACoF,UAAU,CAAC,eAAe,CAAC;IACxC,IAAI,CAACzF,oBAAoB,CAACgB,IAAI,CAAC,IAAI,CAAC;EACtC;EAEA,IAAI0E,IAAIA,CAAA;IACN,OAAO,IAAI,CAAC5F,UAAU,CAAC0C,KAAK;EAC9B;EAEAmD,aAAaA,CAAA;IACX,OAAO,IAAI,CAACC,UAAU;EACxB;EAEA;EACQ9D,YAAYA,CAAA;IAClB,OAAO,sCAAsC,CAAC+D,OAAO,CAAC,OAAO,EAAE,UAAS7C,CAAC;MACvE,MAAM8C,CAAC,GAAGC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC;QAC9BC,CAAC,GAAGjD,CAAC,KAAK,GAAG,GAAG8C,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;MACrC,OAAOG,CAAC,CAAC1C,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ;EAEQ1B,aAAaA,CAACD,KAAa;IACjC,MAAMsE,EAAE,GAAG,4BAA4B;IACvC,OAAOA,EAAE,CAACrD,IAAI,CAACjB,KAAK,CAAC;EACvB;EAEA;EACOuE,YAAYA,CAAC/E,EAAU;IAC5BmD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrCD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEpD,EAAE,CAAC;IAC7BmD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE,OAAOpD,EAAE,CAAC;IAC/BmD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEpD,EAAE,CAACoC,MAAM,CAAC;IACnCe,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,iEAAiE,CAAC3B,IAAI,CAACzB,EAAE,CAAC,CAAC;IACpGmD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,kBAAkB,CAAC3B,IAAI,CAACzB,EAAE,CAAC,CAAC;IAErD,MAAMmB,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;IACzC+B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEjC,WAAW,CAACiB,MAAM,CAAC;IAEtE,MAAM4C,UAAU,GAAG7D,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC5B,EAAE,KAAKA,EAAE,CAAC;IACrD,MAAM0B,YAAY,GAAGP,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;IAEzDmD,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE4B,UAAU,GAAG,KAAK,GAAG,KAAK,CAAC;IAChE7B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE1B,YAAY,GAAG,KAAK,GAAG,KAAK,CAAC;IAEpE,IAAIsD,UAAU,EAAE;MACd7B,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE4B,UAAU,CAAC;;IAE3C,IAAItD,YAAY,EAAE;MAChByB,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE1B,YAAY,CAAC;;IAG/C;IACAyB,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,MAAMvB,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACzB,EAAE,CAAC;IACzF,MAAMwB,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACzB,EAAE,CAAC;IAE1C,IAAIuB,QAAQ,GAAGvB,EAAE;IACjB,IAAIwB,MAAM,IAAI,CAACK,MAAM,EAAE;MACrB,MAAMoD,sBAAsB,GAAG9D,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKN,EAAE,CAAC;MACnE,IAAIiF,sBAAsB,IAAIA,sBAAsB,CAACjF,EAAE,KAAKA,EAAE,EAAE;QAC9DuB,QAAQ,GAAG0D,sBAAsB,CAACjF,EAAE;QACpCmD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE7B,QAAQ,CAAC;;;IAIvC,MAAM2D,WAAW,GAAG,iEAAiE,CAACzD,IAAI,CAACF,QAAQ,CAAC;IACpG4B,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE7B,QAAQ,CAAC;IAClC4B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE8B,WAAW,CAAC;IAC9C/B,OAAO,CAACC,GAAG,CAAC,qBAAqB,CAAC;EACpC;EAEA;EACO+B,oBAAoBA,CAACnF,EAAU,EAAEoE,UAA2B;IACjEjB,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;IAClDD,OAAO,CAACC,GAAG,CAAC,KAAK,EAAEpD,EAAE,CAAC;IACtBmD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgB,UAAU,CAAC;IAEnC,MAAMgB,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAACjB,UAAU,CAAC;IACtDjB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgC,UAAU,CAACE,OAAO,CAAC;IACtD,IAAI,CAACF,UAAU,CAACE,OAAO,EAAE;MACvBnC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEgC,UAAU,CAACvB,MAAM,CAAC;;IAG5CV,OAAO,CAACC,GAAG,CAAC,0CAA0C,CAAC;IACvD,MAAMmC,OAAO,GAAoB,EAAE;IACnC,IAAInB,UAAU,CAAC9D,IAAI,IAAI8D,UAAU,CAAC9D,IAAI,CAACC,IAAI,EAAE,EAAEgF,OAAO,CAACjF,IAAI,GAAG8D,UAAU,CAAC9D,IAAI,CAACC,IAAI,EAAE;IACpF,IAAI6D,UAAU,CAACzD,MAAM,IAAIyD,UAAU,CAACzD,MAAM,CAACJ,IAAI,EAAE,EAAEgF,OAAO,CAAC5E,MAAM,GAAGyD,UAAU,CAACzD,MAAM,CAACJ,IAAI,EAAE;IAC5F,IAAI6D,UAAU,CAACxD,SAAS,IAAIwD,UAAU,CAACxD,SAAS,CAACL,IAAI,EAAE,EAAEgF,OAAO,CAAC3E,SAAS,GAAGwD,UAAU,CAACxD,SAAS,CAACL,IAAI,EAAE,CAACiF,WAAW,EAAE;IACtH,IAAIpB,UAAU,CAAC5D,KAAK,IAAI4D,UAAU,CAAC5D,KAAK,CAACD,IAAI,EAAE,EAAEgF,OAAO,CAAC/E,KAAK,GAAG4D,UAAU,CAAC5D,KAAK,CAACD,IAAI,EAAE,CAACkF,WAAW,EAAE;IACtG,IAAIrB,UAAU,CAACvD,SAAS,IAAIuD,UAAU,CAACvD,SAAS,CAACN,IAAI,EAAE,EAAEgF,OAAO,CAAC1E,SAAS,GAAGuD,UAAU,CAACvD,SAAS,CAACN,IAAI,EAAE;IAExG4C,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEmC,OAAO,CAAC;IACrCpC,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAG,IAAI,CAAC3E,OAAO,GAAGuB,EAAE,EAAE,CAAC;IAC/DmD,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACQiC,kBAAkBA,CAACpF,MAAuB;IAChD,MAAM4D,MAAM,GAAa,EAAE;IAE3B;IAEA;IACA,IAAI5D,MAAM,CAACK,IAAI,KAAKoF,SAAS,EAAE;MAC7B,IAAI,CAACzF,MAAM,CAACK,IAAI,IAAIL,MAAM,CAACK,IAAI,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;QAC7CsD,MAAM,CAACK,IAAI,CAAC,4BAA4B,CAAC;OAC1C,MAAM,IAAIjE,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,CAAC,IAAInC,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,EAAE,EAAE;QAC5DyB,MAAM,CAACK,IAAI,CAAC,6CAA6C,CAAC;;;IAI9D,IAAIjE,MAAM,CAACU,MAAM,KAAK+E,SAAS,IAAIzF,MAAM,CAACU,MAAM,KAAK,IAAI,IAAIV,MAAM,CAACU,MAAM,CAACyB,MAAM,GAAG,GAAG,EAAE;MACvFyB,MAAM,CAACK,IAAI,CAAC,oDAAoD,CAAC;;IAGnE,IAAIjE,MAAM,CAACW,SAAS,KAAK8E,SAAS,IAAIzF,MAAM,CAACW,SAAS,KAAK,IAAI,EAAE;MAC/D;MACA,IAAIX,MAAM,CAACW,SAAS,CAACL,IAAI,EAAE,KAAK,EAAE,IAAIN,MAAM,CAACW,SAAS,CAACwB,MAAM,GAAG,EAAE,EAAE;QAClEyB,MAAM,CAACK,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAIjE,MAAM,CAACO,KAAK,KAAKkF,SAAS,IAAIzF,MAAM,CAACO,KAAK,KAAK,IAAI,EAAE;MACvD,IAAIP,MAAM,CAACO,KAAK,CAACD,IAAI,EAAE,KAAK,EAAE,EAAE;QAC9B;QACA,IAAIN,MAAM,CAACO,KAAK,CAAC4B,MAAM,GAAG,GAAG,EAAE;UAC7ByB,MAAM,CAACK,IAAI,CAAC,2CAA2C,CAAC;;;;IAK9D,IAAIjE,MAAM,CAACY,SAAS,KAAK6E,SAAS,IAAIzF,MAAM,CAACY,SAAS,KAAK,IAAI,EAAE;MAC/D,IAAIZ,MAAM,CAACY,SAAS,CAACN,IAAI,EAAE,KAAK,EAAE,EAAE;QAClC,MAAMoF,YAAY,GAAG,6BAA6B;QAClD,IAAI,CAACA,YAAY,CAAClE,IAAI,CAACxB,MAAM,CAACY,SAAS,CAAC,EAAE;UACxCgD,MAAM,CAACK,IAAI,CAAC,8BAA8B,CAAC;;;;IAKjD,OAAO;MACLoB,OAAO,EAAEzB,MAAM,CAACzB,MAAM,KAAK,CAAC;MAC5ByB;KACD;EACH;EAEA;EACO+B,WAAWA,CAACC,QAAgB,EAAEzB,UAAe;IAClDjB,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;IAEjD;IACA,IAAI,CAAC2B,YAAY,CAACc,QAAQ,CAAC;IAE3B;IACA,IAAI,CAACV,oBAAoB,CAACU,QAAQ,EAAEzB,UAAU,CAAC;IAE/C;IACAjB,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAC/C,MAAMvB,MAAM,GAAG,iEAAiE,CAACJ,IAAI,CAACoE,QAAQ,CAAC;IAC/F,MAAMrE,MAAM,GAAG,kBAAkB,CAACC,IAAI,CAACoE,QAAQ,CAAC;IAEhD,IAAItE,QAAQ,GAAGsE,QAAQ;IACvB,IAAIrE,MAAM,IAAI,CAACK,MAAM,EAAE;MACrB,MAAMV,WAAW,GAAG,IAAI,CAACzC,UAAU,CAAC0C,KAAK;MACzC,MAAMM,YAAY,GAAGP,WAAW,CAACQ,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACtB,IAAI,KAAKuF,QAAQ,CAAC;MAC/D,IAAInE,YAAY,EAAE;QAChBH,QAAQ,GAAGG,YAAY,CAAC1B,EAAE;QAC1BmD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE7B,QAAQ,CAAC;;;IAIvC4B,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,GAAG,IAAI,CAAC3E,OAAO,GAAG8C,QAAQ,EAAE,CAAC;IACxD4B,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3BD,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC/D,UAAU,EAAE,CAAC;IAE1C,MAAMyG,WAAW,GAAQ,EAAE;IAC3B,IAAI1B,UAAU,CAAC9D,IAAI,EAAEwF,WAAW,CAACxF,IAAI,GAAG8D,UAAU,CAAC9D,IAAI,CAACC,IAAI,EAAE;IAC9D,IAAI6D,UAAU,CAACzD,MAAM,EAAEmF,WAAW,CAACnF,MAAM,GAAGyD,UAAU,CAACzD,MAAM,CAACJ,IAAI,EAAE;IACpE,IAAI6D,UAAU,CAACxD,SAAS,EAAEkF,WAAW,CAAClF,SAAS,GAAGwD,UAAU,CAACxD,SAAS,CAACL,IAAI,EAAE,CAAC,CAAC;IAC/E,IAAI6D,UAAU,CAAC5D,KAAK,EAAEsF,WAAW,CAACtF,KAAK,GAAG4D,UAAU,CAAC5D,KAAK,CAACD,IAAI,EAAE,CAAC,CAAC;IACnE,IAAI6D,UAAU,CAACvD,SAAS,EAAEiF,WAAW,CAACjF,SAAS,GAAGuD,UAAU,CAACvD,SAAS,CAACN,IAAI,EAAE;IAE7E4C,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEjE,IAAI,CAACgB,SAAS,CAAC2F,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAChE3C,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;EACjD;EAEA;EACO2C,kBAAkBA,CAACF,QAAgB,EAAEzB,UAAe;IACzDjB,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAE9C,MAAM4C,GAAG,GAAG,GAAG,IAAI,CAACvH,OAAO,GAAGoH,QAAQ,EAAE;IACxC,MAAMpG,OAAO,GAAG,IAAI,CAACJ,UAAU,EAAE;IAEjC8D,OAAO,CAACC,GAAG,CAAC,MAAM,EAAE4C,GAAG,CAAC;IACxB7C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE3D,OAAO,CAAC;IAChC0D,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEjE,IAAI,CAACgB,SAAS,CAACiE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAEzD,OAAO,IAAI,CAAC7F,IAAI,CAAC8D,GAAG,CAAC2D,GAAG,EAAE5B,UAAU,EAAE;MACpC3E,OAAO,EAAEA,OAAO;MAChBsB,OAAO,EAAE;KACV,CAAC,CAACrB,IAAI,CACLxB,GAAG,CAAC8C,QAAQ,IAAG;MACbmC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEpC,QAAQ,CAAC;IAC7C,CAAC,CAAC,EACF7C,UAAU,CAAC0B,KAAK,IAAG;MACjBsD,OAAO,CAACtD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CsD,OAAO,CAACtD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACyC,MAAM,CAAC;MACtCa,OAAO,CAACtD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,CAAC;MAEzC;MACA,OAAO5B,UAAU,CAAC,MAAM4B,KAAK,CAAC;IAChC,CAAC,CAAC,CACH;EACH;EAEA;EACOoG,iBAAiBA,CAACJ,QAAgB;IACvC1C,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAEhD;IACA,MAAM8C,YAAY,GAAG;MAAE5F,IAAI,EAAE;IAAS,CAAE;IACxC6C,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE8C,YAAY,CAAC;IAErD,OAAO,IAAI,CAACH,kBAAkB,CAACF,QAAQ,EAAEK,YAAY,CAAC,CAACxG,IAAI,CACzDvB,UAAU,CAAC,MAAK;MACdgF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;MAE7C;MACA,MAAM+C,YAAY,GAAG;QAAExF,MAAM,EAAE;MAAa,CAAE;MAC9CwC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE+C,YAAY,CAAC;MAEvD,OAAO,IAAI,CAACJ,kBAAkB,CAACF,QAAQ,EAAEM,YAAY,CAAC,CAACzG,IAAI,CACzDvB,UAAU,CAAC,MAAK;QACdgF,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;QAE7C;QACA,MAAMgD,YAAY,GAAG,EAAE;QACvBjD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEgD,YAAY,CAAC;QAEjD,OAAO,IAAI,CAACL,kBAAkB,CAACF,QAAQ,EAAEO,YAAY,CAAC;MACxD,CAAC,CAAC,CACH;IACH,CAAC,CAAC,CACH;EACH;EAEA;EACOC,kBAAkBA,CAACpG,MAAuB;IAC/C,MAAM4D,MAAM,GAAa,EAAE;IAE3B,IAAI5D,MAAM,CAACK,IAAI,EAAE;MACf,IAAIL,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,CAAC,IAAInC,MAAM,CAACK,IAAI,CAAC8B,MAAM,GAAG,EAAE,EAAE;QACrDyB,MAAM,CAACK,IAAI,CAAC,uDAAuD,CAAC;;;IAIxE,IAAIjE,MAAM,CAACU,MAAM,IAAIV,MAAM,CAACU,MAAM,CAACyB,MAAM,GAAG,GAAG,EAAE;MAC/CyB,MAAM,CAACK,IAAI,CAAC,uDAAuD,CAAC;;IAGtE,IAAIjE,MAAM,CAACW,SAAS,EAAE;MACpB;MACA,IAAIX,MAAM,CAACW,SAAS,CAACwB,MAAM,GAAG,EAAE,EAAE;QAChCyB,MAAM,CAACK,IAAI,CAAC,qDAAqD,CAAC;;;IAItE,IAAIjE,MAAM,CAACO,KAAK,EAAE;MAChB;MACA,IAAIP,MAAM,CAACO,KAAK,CAAC4B,MAAM,GAAG,GAAG,EAAE;QAC7ByB,MAAM,CAACK,IAAI,CAAC,8CAA8C,CAAC;;;IAI/D,IAAIjE,MAAM,CAACY,SAAS,EAAE;MACpB,MAAM8E,YAAY,GAAG,6BAA6B;MAClD,IAAI,CAACA,YAAY,CAAClE,IAAI,CAACxB,MAAM,CAACY,SAAS,CAAC,EAAE;QACxCgD,MAAM,CAACK,IAAI,CAAC,8BAA8B,CAAC;;;IAI/C,OAAO;MACLoB,OAAO,EAAEzB,MAAM,CAACzB,MAAM,KAAK,CAAC;MAC5ByB;KACD;EACH;EAEA;EACQyC,iBAAiBA,CAACzG,KAAwB;IAChD,IAAI0G,YAAY,GAAG,yCAAyC;IAE5DpD,OAAO,CAACtD,KAAK,CAAC,+BAA+B,CAAC;IAC9CsD,OAAO,CAACtD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAACyC,MAAM,CAAC;IACtCa,OAAO,CAACtD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC2G,UAAU,CAAC;IAC9CrD,OAAO,CAACtD,KAAK,CAAC,MAAM,EAAEA,KAAK,CAACmG,GAAG,CAAC;IAChC7C,OAAO,CAACtD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACA,KAAK,CAAC;IAC3CsD,OAAO,CAACtD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACA,KAAK,GAAGkC,MAAM,CAACC,IAAI,CAACnC,KAAK,CAACA,KAAK,CAAC,GAAG,iBAAiB,CAAC;IACxFsD,OAAO,CAACtD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;IACnCsD,OAAO,CAACtD,KAAK,CAAC,8BAA8B,CAAC;IAE7C;IACA,MAAM4G,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAE6G,KAAK,IAAI7G,KAAK,CAACA,KAAK,EAAEA,KAAK;IACjF,MAAM8G,gBAAgB,GAAG9G,KAAK,CAACA,KAAK,EAAEgE,MAAM;IAC5C,MAAM+C,OAAO,GAAG/G,KAAK,CAACA,KAAK,EAAE+G,OAAO;IACpC,MAAMC,IAAI,GAAGhH,KAAK,CAACA,KAAK,EAAEgH,IAAI;IAE9B1D,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqD,QAAQ,CAAC;IACnCtD,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEuD,gBAAgB,CAAC;IACnDxD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,OAAOuD,gBAAgB,CAAC;IAC/DxD,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEuD,gBAAgB,GAAG5E,MAAM,CAACC,IAAI,CAAC2E,gBAAgB,CAAC,GAAG,sBAAsB,CAAC;IACjHxD,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEwD,OAAO,CAAC;IACjCzD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEyD,IAAI,CAAC;IAEhC;IACA,IAAIhH,KAAK,CAACA,KAAK,EAAE;MACfsD,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,KAAK,MAAMlB,GAAG,IAAIrC,KAAK,CAACA,KAAK,EAAE;QAC7BsD,OAAO,CAACC,GAAG,CAAC,KAAKlB,GAAG,GAAG,EAAErC,KAAK,CAACA,KAAK,CAACqC,GAAG,CAAC,CAAC;;;IAI9C,IAAIrC,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MACtBiE,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,uCAAuC;MAEtD;MACA,IAAII,gBAAgB,EAAE;QACpB,MAAMG,YAAY,GAAa,EAAE;QAEjC;QACA,IAAI,OAAOH,gBAAgB,KAAK,QAAQ,EAAE;UACxC5E,MAAM,CAACC,IAAI,CAAC2E,gBAAgB,CAAC,CAAC1E,OAAO,CAAC8E,KAAK,IAAG;YAC5C,MAAMC,WAAW,GAAGL,gBAAgB,CAACI,KAAK,CAAC;YAC3C,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;cAC9BA,WAAW,CAAC/E,OAAO,CAACkF,GAAG,IAAIL,YAAY,CAAC5C,IAAI,CAAC,GAAG6C,KAAK,KAAKI,GAAG,EAAE,CAAC,CAAC;aAClE,MAAM;cACLL,YAAY,CAAC5C,IAAI,CAAC,GAAG6C,KAAK,KAAKC,WAAW,EAAE,CAAC;;UAEjD,CAAC,CAAC;;QAGJ,IAAIF,YAAY,CAAC1E,MAAM,GAAG,CAAC,EAAE;UAC3BmE,YAAY,IAAI,iBAAiBO,YAAY,CAACM,IAAI,CAAC,IAAI,CAAC,EAAE;;OAE7D,MAAM,IAAIX,QAAQ,EAAE;QACnBF,YAAY,IAAI,eAAeE,QAAQ,EAAE;;MAG3C;MACA,IAAIA,QAAQ,IAAIA,QAAQ,CAAC7D,QAAQ,CAAC,4BAA4B,CAAC,EAAE;QAC/D2D,YAAY,GAAG,0BAA0B;QACzC,IAAI1G,KAAK,CAACA,KAAK,EAAEgE,MAAM,EAAE;UACvB0C,YAAY,IAAI,0CAA0C;UAC1DxE,MAAM,CAACC,IAAI,CAACnC,KAAK,CAACA,KAAK,CAACgE,MAAM,CAAC,CAAC5B,OAAO,CAAC8E,KAAK,IAAG;YAC9CR,YAAY,IAAI,KAAKQ,KAAK,IAAI;UAChC,CAAC,CAAC;SACH,MAAM;UACLR,YAAY,IAAI,kDAAkD;;;KAGvE,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC/H,MAAM,CAAC6I,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,mBAAmB;KACnC,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,wDAAwD;KACxE,MAAM,IAAIE,QAAQ,EAAE;MACnBF,YAAY,GAAG,0BAA0BE,QAAQ,EAAE;;IAGrD,OAAOxI,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAACkG,YAAY,CAAC,CAAC;EAClD;EAEQlF,iBAAiBA,CAACxB,KAAwB;IAChD,IAAI0G,YAAY,GAAG,sCAAsC;IACzD,MAAME,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAE6G,KAAK,IAAI7G,KAAK,CAACA,KAAK,EAAEA,KAAK;IAEjF,IAAI4G,QAAQ,EAAE;MACZF,YAAY,GAAG,uBAAuBE,QAAQ,EAAE;KACjD,MAAM,IAAI5G,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC7BiE,YAAY,GAAG,uCAAuC;KACvD,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,mBAAmB;MAClC,IAAI1G,KAAK,CAACA,KAAK,EAAEgE,MAAM,EAAE;QACvB,MAAM8C,gBAAgB,GAAG5E,MAAM,CAACuF,MAAM,CAACzH,KAAK,CAACA,KAAK,CAACgE,MAAM,CAAC,CAAC0D,IAAI,EAAE;QACjEhB,YAAY,IAAI,aAAaI,gBAAgB,CAACS,IAAI,CAAC,IAAI,CAAC,EAAE;;KAE7D,MAAM,IAAIvH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,iBAAiB;MAChC,IAAI,CAAC/H,MAAM,CAAC6I,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,mCAAmC;;IAGpD,OAAOtI,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAACkG,YAAY,CAAC,CAAC;EAClD;EAEQzG,WAAWA,CAACD,KAAwB;IAC1C,IAAI0G,YAAY,GAAG,mBAAmB;IACtC,MAAME,QAAQ,GAAG5G,KAAK,CAACA,KAAK,EAAE8C,OAAO,IAAI9C,KAAK,CAACA,KAAK,EAAE6G,KAAK;IAE3D,IAAID,QAAQ,EAAE;MACZF,YAAY,GAAGE,QAAQ;KACxB,MAAM,IAAI5G,KAAK,CAACyC,MAAM,KAAK,CAAC,EAAE;MAC7BiE,YAAY,GAAG,6BAA6B;KAC7C,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,sBAAsB;KACtC,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,cAAc;MAC7B,IAAI,CAAC/H,MAAM,CAAC6I,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;KACjC,MAAM,IAAIxH,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,kBAAkB;KAClC,MAAM,IAAI1G,KAAK,CAACyC,MAAM,KAAK,GAAG,EAAE;MAC/BiE,YAAY,GAAG,kCAAkC;;IAGnD,OAAOtI,UAAU,CAAC,MAAM,IAAIoC,KAAK,CAACkG,YAAY,CAAC,CAAC;EAClD;EAAC,QAAAiB,CAAA,G;qBA9tBUnJ,aAAa,EAAAoJ,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,MAAA;EAAA;EAAA,QAAAC,EAAA,G;WAAb1J,aAAa;IAAA2J,OAAA,EAAb3J,aAAa,CAAA4J,IAAA;IAAAC,UAAA,EAFZ;EAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}